using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common.Http;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.OrderService.Domain;
using TestStack.BDDfy.Xunit;
using TestStack.BDDfy;
using Xunit.Abstractions;
using RJO.OrderService.WebApi.Testing;
using Xunit;
using RJO.BuildingBlocks.Testing;
using RJO.OrderService.Domain.Settings;
using RJO.OrderService.Services.DTO.RJO;
using RJO.OrderService.Services.DTO.Offer;
using RJO.OrderService.Domain.ERP;

namespace RJO.OrderService.WebApi.Tests.Endpoints.Offers;

public class CreateTests : EndpointOutboundHttpTests
{
	TenantData _tenantData;
	Commodity _commodity;
	Bidsheet _bidsheet;
	Product _product;
	Offer _offer;
	MobileIntegrationLocationMapping _myGrowerDestinationLocation;
	Location _deliveryLocation;

	public CreateTests(OrderService fixture, ITestOutputHelper outputHelper) : base(fixture, outputHelper) { }

	[BddfyTheory]
	[InlineData(ContractTypeNames.Basis, TenantNames.MidIowa, "CornSample001")]
	[InlineData(ContractTypeNames.HTA, TenantNames.EnterpriseGrain, "CornSample002")]
	void Scenario1(string contractType, string tenant, string commodityName)
	{
		_tenantData = TestData.For(tenant);
		_deliveryLocation = _tenantData.Locations.Oakbrook;
		this
			.Given(x => x.InitData(_tenantData, commodityName))
			.And(x => x.ValidInput(contractType, _tenantData.Employees.Dave.Id, 10))
			.And(x => x.RjoRespondsWithExpectedData(MarketStatus.ReadyToTrade, DateTime.UtcNow.ToString("yyyy-MM-dd")))
			.When(x => x.SendingRequest(_tenantData.FakeUsers.Alice))
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.TheOfferIsCreatedWithExpectedData())
			.And(x => x.ClearCommodity(_commodity, _tenantData))
			.BDDfy();
	}

	[BddfyTheory]
	[InlineData(ContractTypeNames.Basis, TenantNames.MidIowa, "CornSample001")]
	[InlineData(ContractTypeNames.HTA, TenantNames.EnterpriseGrain, "CornSample002")]
	void Scenario2(string contractType, string tenant, string commodityName)
	{
		_tenantData = TestData.For(tenant);
		_deliveryLocation = _tenantData.Locations.Oakbrook;
		this.Given(x => x.InitData(_tenantData, commodityName))
			.And(x => x.ValidInputForOfferWithExpiry(contractType))
			.When(x => x.SendingRequest(_tenantData.FakeUsers.Alice))
			.Then(x => x.TheResponseStatusIsBadRequest())
			.And(x => x.ClearCommodity(_commodity, _tenantData))
			.BDDfy();
	}

	[BddfyTheory]
	[InlineData(ContractTypeNames.Basis, TenantNames.MidIowa, "CornSample003")]
	[InlineData(ContractTypeNames.HTA, TenantNames.EnterpriseGrain, "CornSample004")]
	void GroupLocationMapingFromMyGrowerMismatched(string contractType, string tenant, string commodityName)
	{
		_tenantData = TestData.For(tenant);
		_deliveryLocation = _tenantData.Locations.Oakbrook;

		this.Given(x => x.InitData(_tenantData, commodityName))
			.And(x => x.CreateMyGrowerGroupedLocationMap(_tenantData))
			.And(x => x.ValidGroupLocationInputForOffer(Guid.NewGuid(), 5000, "", contractType))
			.When(x => x.SendingRequest(_tenantData.FakeUsers.MyGrower))
			.Then(x => x.TheResponseStatusIsBadRequest())
			.And(x => x.AnErrorMessageIsReturned("Grouped location for the mobile user could not be found"))
			.And(x => x.ClearCommodity(_commodity, _tenantData))
			.BDDfy();
	}

	[BddfyTheory]
	[InlineData(ContractTypeNames.Basis, TenantNames.MidIowa, "CornSample005")]
	[InlineData(ContractTypeNames.HTA, TenantNames.EnterpriseGrain, "CornSample005")]
	void GroupLocationMapingFromMyGrower(string contractType, string tenant, string commodityName)
	{
		_tenantData = TestData.For(tenant);
		_deliveryLocation = _tenantData.Locations.Oakbrook;
		var comments = "Group location mapping from mygrower test";

		this.Given(x => x.InitData(_tenantData, commodityName))
			.And(x => x.CreateMyGrowerGroupedLocationMap(_tenantData))
			.And(x => x.ValidGroupLocationInputForOffer(_deliveryLocation.Id, 5000, comments, contractType))
			.When(x => x.SendingRequest(_tenantData.FakeUsers.MyGrower))
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.TheExpectedDataIsReturned())
			.And(x => x.OfferWasCreatedSuccessfully(comments))
			.And(x => x.ClearCommodity(_commodity, _tenantData))
			.BDDfy();
	}

	[BddfyTheory]
	[InlineData(ContractTypeNames.Basis, TenantNames.MidIowa, "CornSample001")]
	[InlineData(ContractTypeNames.HTA, TenantNames.EnterpriseGrain, "CornSample002")]
	void MobileUserCreatesAnOfferSuccessfully(string contractType, string tenant, string commodityName)
	{
		_tenantData = TestData.For(tenant);
		_deliveryLocation = _tenantData.Locations.Oakbrook;
		this.Given(x => x.InitData(_tenantData, commodityName))
			.And(x => x.CreateMyGrowerGroupedLocationMap(_tenantData))
			.And(x => x.ValidInput(contractType, _tenantData.Employees.MyGrower.Id, 5000))
			.And(x => x.UpdateCustomerMobileAppLimit(10000))
			.And(x => x.RjoRespondsWithExpectedData(MarketStatus.ReadyToTrade, DateTime.UtcNow.ToString("yyyy-MM-dd")))
			.When(x => x.SendingRequest(_tenantData.FakeUsers.MyGrower))
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.TheOfferIsCreatedWithExpectedData())
			.And(x => x.ClearCommodity(_commodity, _tenantData))
			.BDDfy();
	}

	[BddfyTheory]
	[InlineData(ContractTypeNames.Basis, TenantNames.MidIowa, "CornSample001")]
	[InlineData(ContractTypeNames.HTA, TenantNames.EnterpriseGrain, "CornSample002")]
	void MobileUserFailsToCreateAnOfferDueToQuantityAboveMobileAppLimit(string contractType, string tenant, string commodityName)
	{
		_tenantData = TestData.For(tenant);
		_deliveryLocation = _tenantData.Locations.Oakbrook;
		this.Given(x => x.InitData(_tenantData, commodityName))
			.And(x => x.ValidInput(contractType, _tenantData.Employees.MyGrower.Id, 15000))
			.And(x => x.UpdateCustomerMobileAppLimit(10000))
			.And(x => x.RjoRespondsWithExpectedData(MarketStatus.ReadyToTrade, DateTime.UtcNow.ToString("yyyy-MM-dd")))
			.When(x => x.SendingRequest(_tenantData.FakeUsers.MyGrower))
			.Then(x => x.TheResponseStatusIsBadRequest())
			.And(x => x.AnErrorMessageIsReturned("Offer Quantity is above bushel limit"))
			.And(x => x.ClearCommodity(_commodity, _tenantData))
			.BDDfy();
	}

	[BddfyTheory]
	[InlineData(ContractTypeNames.Basis, "CornSample601")]
	[InlineData(ContractTypeNames.HTA, "CornSample602")]
	void MobileUserFailsToCreateAnOfferDueToInactiveLocation(string contractType, string commodityName)
	{
		_tenantData = TestData.MidIowa;
		_deliveryLocation = _tenantData.Locations.InactiveLocation;

		this.Given(x => x.InitData(_tenantData, commodityName))
			.And(x => x.CreateMyGrowerInactiveGroupedLocationMap(_tenantData))
			.And(x => x.ValidInput(contractType, _tenantData.Employees.MyGrower.Id, 5000))
			.And(x => x.UpdateCustomerMobileAppLimit(10000))
			.And(x => x.RjoRespondsWithExpectedData(MarketStatus.ReadyToTrade, DateTime.UtcNow.ToString("yyyy-MM-dd")))
			.When(x => x.SendingRequest(_tenantData.FakeUsers.MyGrower))
			.Then(x => x.TheResponseStatusIsBadRequest())
			.And(x => x.AnErrorMessageIsReturned("Grouped location for the mobile user is inactive"))
			.And(x => x.ClearCommodity(_commodity, _tenantData))
			.BDDfy();
	}

	async Task UpdateCustomerMobileAppLimit(decimal? limit) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var customer = await dbContext.Customers.FirstOrDefaultAsync(a => a.Id == _tenantData.Customers.RichFranz.Id);
			customer.ChangeMobile(true, limit);
		});

	async Task AnErrorMessageIsReturned(string message)
	{
		var content = await Response.Content.ReadResultMessage();
		content.Should().Be(message);
	}

	async Task CreateMyGrowerGroupedLocationMap(TenantData tenantData) =>
		await Fixture.WithDbContextFor(tenantData, async dbContext =>
		{
			var groupedLocation = await dbContext.GroupedLocations.FirstOrDefaultAsync(a => a.RegionId != null && a.IsActive);
			groupedLocation.DestinationLocationId = _deliveryLocation.Id;
			var item = dbContext.MobileIntegrationLocationMapping.Add(new(groupedLocation.Id, _deliveryLocation.Id));
			_myGrowerDestinationLocation = item.Entity;
			await dbContext.SaveChangesAsync();
		});

	async Task CreateMyGrowerInactiveGroupedLocationMap(TenantData tenantData) =>
		await Fixture.WithDbContextFor(tenantData, async dbContext =>
		{
			var groupedLocation = await dbContext.GroupedLocations.FirstOrDefaultAsync(a => a.RegionId != null && !a.IsActive);
			groupedLocation.DestinationLocationId = _deliveryLocation.Id;
			var item = dbContext.MobileIntegrationLocationMapping.Add(new(groupedLocation.Id, _deliveryLocation.Id));
			_myGrowerDestinationLocation = item.Entity;
			await dbContext.SaveChangesAsync();
		});

	async Task InitData(TenantData tenantData, string commodityName) =>
		await Fixture.WithDbContextFor(tenantData, async dbContext =>
		{
			_commodity = await dbContext.CreateCommodity(tenantData, commodityName);
			_product = _tenantData.Products.GetById(_commodity.ProductId);

			await dbContext.CreateBidsheets(_commodity);
			_bidsheet = await dbContext.SettingsBidsheets.FirstOrDefaultAsync(x => x.CommodityId == _commodity.Id && x.DeliveryLocationId == _deliveryLocation.Id);
		});

	async Task ClearCommodity(Commodity commodity, TenantData tenantData) =>
		await Fixture.WithDbContextFor(tenantData, async dbContext =>
		{
			if (_myGrowerDestinationLocation != null)
			{
				dbContext.MobileIntegrationLocationMapping.Remove(_myGrowerDestinationLocation);
				await dbContext.SaveChangesAsync();
				_myGrowerDestinationLocation = null;
			}
			await dbContext.ClearCommodity(commodity);
		});

	void ValidInput(string contractType, Guid employeeId, decimal quantity) =>
		RequestData = new OfferCreateDto
		{
			TransactionTypeId = TestData.TransactionTypes.Cash.Id,
			ContractTypeId = ContractTypeNames.DataFor(contractType).Id,
			IsSell = true,
			CommodityId = _commodity.Id,
			LocationId = _tenantData.Locations.LakeFalls.Id,
			DeliveryLocationId = _deliveryLocation.Id,
			RegionId = _tenantData.Regions.Default.Id,
			IsDeliveryDatesCustom = false,
			DeliveryStartDate = _bidsheet.DeliveryStart,
			DeliveryEndDate = _bidsheet.DeliveryEnd,
			CropYear = _bidsheet.CropYear,
			CustomerId = _tenantData.Customers.RichFranz.Id,
			CustomerNumber = _tenantData.Customers.RichFranz.Number,
			EmployeeId = employeeId,
			FuturesMonth = _bidsheet.FutureMonth,
			FuturesPrice = 6.12m,
			PostedBasis = -0.35m,
			PushBasis = -0.21m,
			NetBasis = -0.33m,
			Fees1 = 0.05m,
			Fees2 = 0,
			Price = 7.00m,
			FreightPrice = 50.00m,
			Quantity = quantity,
			Comments = $"Integration test to Create Contract with {_tenantData.Name}",
			Gtc = true,
			TheirContract = null,
			CustomFields = null,
			Expiration = null,
			Groups = new List<Guid>()
		};

	void ValidInputForOfferWithExpiry(string contractType) =>
		RequestData = new OfferCreateDto
		{
			TransactionTypeId = TestData.TransactionTypes.Cash.Id,
			ContractTypeId = ContractTypeNames.DataFor(contractType).Id,
			IsSell = true,
			CommodityId = _commodity.Id,
			LocationId = _tenantData.Locations.LakeFalls.Id,
			DeliveryLocationId = _deliveryLocation.Id,
			RegionId = _tenantData.Regions.Default.Id,
			IsDeliveryDatesCustom = false,
			DeliveryStartDate = _bidsheet.DeliveryStart,
			DeliveryEndDate = _bidsheet.DeliveryEnd,
			CropYear = _bidsheet.CropYear,
			CustomerId = _tenantData.Customers.RichFranz.Id,
			EmployeeId = _tenantData.Employees.Dave.Id,
			FuturesMonth = _bidsheet.FutureMonth,
			FuturesPrice = 6.12m,
			PostedBasis = -0.35m,
			PushBasis = -0.21m,
			NetBasis = -0.33m,
			Fees1 = 0.05m,
			Fees2 = 0,
			Price = 7.00m,
			FreightPrice = 50.00m,
			Quantity = 10,
			Comments = $"Integration test to Create Contract with {_tenantData.Name}",
			Gtc = false,
			TheirContract = "test",
			CustomFields = null,
			Expiration = GetNextSaturday(),
			Groups = new List<Guid>()
		};

	void ValidGroupLocationInputForOffer(Guid deliveryLocationId, decimal quantity, string comments, string contractType) =>
		RequestData = new OfferCreateDto
		{
			TransactionTypeId = TestData.TransactionTypes.Cash.Id,
			ContractTypeId = ContractTypeNames.DataFor(contractType).Id,
			IsSell = true,
			CommodityId = _commodity.Id,
			LocationId = _tenantData.Locations.LakeFalls.Id,
			DeliveryLocationId = deliveryLocationId,
			RegionId = _tenantData.Regions.Default.Id,
			IsDeliveryDatesCustom = false,
			DeliveryStartDate = _bidsheet.DeliveryStart,
			DeliveryEndDate = _bidsheet.DeliveryEnd,
			CropYear = _bidsheet.CropYear,
			CustomerId = _tenantData.Customers.RichFranz.Id,
			EmployeeId = _tenantData.Employees.MyGrower.Id,
			FuturesMonth = _bidsheet.FutureMonth,
			FuturesPrice = 6.12m,
			PostedBasis = -0.35m,
			PushBasis = -0.21m,
			NetBasis = -0.33m,
			Fees1 = 0.05m,
			Fees2 = 0,
			Price = 7.00m,
			FreightPrice = 50.00m,
			Quantity = quantity,
			Comments = comments,
			Gtc = true,
			TheirContract = "test",
			CustomFields = null,
			Groups = new List<Guid>()
		};

	static DateTime GetNextSaturday()
	{
		var daysUntilSaturday = ((int)DayOfWeek.Saturday - (int)DateTime.Today.DayOfWeek + 7) % 7;

		if (daysUntilSaturday == 0)
		{
			daysUntilSaturday = 7;
		}

		return DateTime.Today.AddDays(daysUntilSaturday);
	}

	async Task SendingRequest(FakeUser user) => Response = await Client.AuthenticatedAs(user).PostAsync("/api/Offers", RequestData.ToJsonContent());

	async Task TheExpectedDataIsReturned() =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var content = await Response.Content.ReadResultData<string>();
			content.Should().HaveLength(15);

			_offer = await dbContext.Set<Offer>().Where(x => x.InternalCode == content).FirstOrDefaultAsync();
			_offer.Should().NotBeNull();
		});

	Task OfferWasCreatedSuccessfully(string comments)
	{
		_offer.CommodityId.Should().Be(_commodity.Id);
		_offer.Comments.Should().Be(comments);
		_offer.LocationId.Should().Be(_myGrowerDestinationLocation.GroupedLocation.ContractLocationId.Value);
		_offer.RegionId.Should().Be(_myGrowerDestinationLocation.GroupedLocation.RegionId);
		return Task.CompletedTask;
	}

	async Task TheOfferIsCreatedWithExpectedData() =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var internalCode = await Response.Content.ReadResultData<string>();
			internalCode.Should().HaveLength(15);

			var offer = await dbContext.Offers.FirstOrDefaultAsync(x => x.InternalCode == internalCode);
			offer.Should().NotBeNull();

			var requestData = (OfferCreateDto)RequestData;
			offer!.Id.Should().NotBeEmpty();
			offer.CommodityId.Should().Be(requestData.CommodityId);
			offer.LocationId.Should().Be(requestData.LocationId);
			offer.ContractTypeId.Should().Be(requestData.ContractTypeId);
			offer.TransactionTypeId.Should().Be(requestData.TransactionTypeId);
			offer.DeliveryLocationId.Should().Be(requestData.DeliveryLocationId);
			offer.CustomerId.Should().Be(requestData.CustomerId);
			offer.EmployeeId.Should().Be(requestData.EmployeeId);
			offer.IsSell.Should().Be(requestData.IsSell);
			offer.CropYear.Should().Be(requestData.CropYear);
			offer.Quantity.Should().Be(requestData.Quantity);
			offer.Comments.Should().Be(requestData.Comments);
		});

	void RjoRespondsWithExpectedData(MarketStatus marketStatus, string tradeDate)
	{
		var monthNumber = TestData.FutureMonths[_bidsheet.FutureMonth[0]];
		var previousMonthNumber = monthNumber == 12 ? 1 : monthNumber - 1;

		var expectedRequestContentForValidInput = new DataRequest
		{
			Requestor = _tenantData.FakeUsers.Alice.Email,
			DisplayType = DisplayType.NonDisplay,
			DelayType = DelayType.Realtime,
			ContractList =
			[
				new()
				{
					SecurityType = SecurityType.Future,
					Symbol = _product.ExchangeSymbol,
					MaturityDate = $"{_bidsheet.CropYear}{monthNumber:00}",
					SpreadMaturityDate = null,
					FrontMonths = 0
				}
			]
		};
		var dataResponseItem1 = new DataResponseItem
		{
			AskPrice = 5.8125m,
			BidPrice = 5.81m,
			LastPrice = 5.8125m,
			HighPrice = 5.815m,
			LowPrice = 5.7825m,
			OpenPrice = 5.785m,
			PrevSettledPrice = 6.7775m,
			LastUpdatedTimestamp = DateTime.UtcNow.AddSeconds(-5),
			SettledPrice = 6.7775m,
			SettledDate = DateTime.UtcNow.Date,
			SettledTimestamp = DateTime.UtcNow.Date.AddHours(2),
			Symbol = _product.ExchangeSymbol,
			MaturityDate = $"{_bidsheet.CropYear}{previousMonthNumber:00}",
			FrontMonths = 2
		};
		var dataResponseItem2 = new DataResponseItem
		{
			AskPrice = 5.4325m,
			BidPrice = 5.41m,
			LastPrice = 5.4325m,
			HighPrice = 5.415m,
			LowPrice = 5.4325m,
			OpenPrice = 5.485m,
			PrevSettledPrice = 5.4775m,
			LastUpdatedTimestamp = DateTime.UtcNow.AddSeconds(-5),
			SettledPrice = 5.4775m,
			SettledDate = DateTime.UtcNow.Date,
			SettledTimestamp = DateTime.UtcNow.Date.AddHours(3),
			Symbol = _product.ExchangeSymbol,
			MaturityDate = $"{_bidsheet.CropYear}{monthNumber:00}",
			FrontMonths = 2
		};

		dataResponseItem1.MarketStatus = marketStatus;
		dataResponseItem2.MarketStatus = marketStatus;

		if (marketStatus == MarketStatus.ReadyToTrade)
		{
			dataResponseItem1.SettledDate = null;
			dataResponseItem2.SettledDate = null;

			dataResponseItem1.SettledPrice = null;
			dataResponseItem2.SettledPrice = null;
		}

		dataResponseItem1.TradeDate = tradeDate;
		dataResponseItem2.TradeDate = tradeDate;

		Fixture.OutboundHttp.RespondWithJsonFor(Fixture.RjoApi.PortalWebOeHrvystMarketData(expectedRequestContentForValidInput),
			new DataResponse
			{
				ResultCode = DataResultCode.OK,
				HttpStatusCode = 200,
				DelayType = DelayType.Delayed,
				ResponseList = [dataResponseItem1, dataResponseItem2]
			});
	}
}
