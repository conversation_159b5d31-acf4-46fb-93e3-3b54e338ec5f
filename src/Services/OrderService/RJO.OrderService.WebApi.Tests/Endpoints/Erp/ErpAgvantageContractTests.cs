using FluentAssertions;
using FluentAssertions.Extensions;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common.Http;
using RJO.BuildingBlocks.Testing;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.ERP;
using RJO.OrderService.Domain.Settings;
using RJO.OrderService.Services.DTO.Contract;
using RJO.OrderService.WebApi.Tests.Endpoints.Contracts;
using TestStack.BDDfy;
using TestStack.BDDfy.Xunit;
using Xunit;
using Xunit.Abstractions;

namespace RJO.OrderService.WebApi.Tests.Endpoints.Erp;

public class ErpAgvantageContractTests(OrderService fixture, ITestOutputHelper outputHelper) : EndpointOutboundHttpTests(fixture, outputHelper) 
{
	readonly TenantData _tenantData = TestData.AgvantageTenant;
	string _contractType;
	Commodity _commodity;
	Location _deliveryLocation;
	Bidsheet _bidsheet;
	Contract _contract;
	List<OrderMetadata> _customFields;
	Contract _originalContract;
	readonly decimal _pushBasisCreate = -0.10m, _pushBasisPricing = -0.16m;
	readonly decimal _postBasis = -0.35m;
	readonly decimal _futurePrice = 6.11m;

	[BddfyTheory]
	[InlineData(ContractTypeNames.FlatPrice)]
	[InlineData(ContractTypeNames.HTA)]
	[InlineData(ContractTypeNames.Basis)]
	void Scenario_CreateContract(string contractType)
	{
		_contractType = contractType;
		_deliveryLocation = _tenantData.Locations.Oakbrook;

		this
			.Given(x => x.InitData(_tenantData, "ErpAgvantageContract_CreateContract"))
			.And(x => x.ValidCreateContractInput(10))
			.And(x => x.Fixture.TheErpRespondsWithoutErrors(_tenantData))
			.When(x => x.SendingCreateContractRequest())
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.TheExpectedDataIsReturned())
			.And(x => x.TheErpIsNotifiedCorrectly())
			.And(x => x.ClearCommodity(_commodity, _tenantData))
			.BDDfy();
	}

	[BddfyTheory]
	[InlineData(ContractTypeNames.Basis)]
	void Scenario_UndoPricedContract(string contractType)
	{
		_contractType = contractType;
		_deliveryLocation = _tenantData.Locations.Oakbrook;
		this
			.Given(x => x.InitData(_tenantData, "ErpAgvantageContract_UndoPricedContract"))
			.And(x => x.ValidCreateContractInput(10000))
			.And(x => x.Fixture.TheErpRespondsWithoutErrors(_tenantData))
			.When(x => x.SendingCreateContractRequest())
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.TheExpectedDataIsReturned())
			.And(x => x.TheErpIsNotifiedCorrectly())
			.When(x => x.ValidPriceInputForQuantity(3000))
			.Then(x => x.SendingPriceRequest())
			.And(x => x.TheResponseStatusIsSuccess())
			.And(x => x.TheExpectedDataIsReturned())
			.And(x => x.TheErpIsNotifiedCorrectlyForPrice())
			.When(x => x.ValidInputForUndo(_contract.Id, _contract.Quantity))
			.Then(x => x.SendingCancelAndUndoPricedRequest())
			.And(x => x.TheResponseStatusIsSuccess())
			.And(x => x.TheErpIsNotifiedCorrectlyForUndo())
			.And(x => x.ClearCommodity(_commodity, _tenantData))
			.BDDfy();
	}

	[BddfyTheory]
	[InlineData(ContractTypeNames.Basis)]
	void Scenario_CancelPricedContract(string contractType)
	{
		_contractType = contractType;
		_deliveryLocation = _tenantData.Locations.Oakbrook;
		this
			.Given(x => x.InitData(_tenantData, "ErpAgvantageContract_CancelPricedContract"))
			.And(x => x.ValidCreateContractInput(10000))
			.And(x => x.Fixture.TheErpRespondsWithoutErrors(_tenantData))
			.When(x => x.SendingCreateContractRequest())
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.TheExpectedDataIsReturned())
			.And(x => x.TheErpIsNotifiedCorrectly())
			.When(x => x.ValidPriceInputForQuantity(3000))
			.Then(x => x.SendingPriceRequest())
			.And(x => x.TheResponseStatusIsSuccess())
			.And(x => x.TheExpectedDataIsReturned())
			.And(x => x.TheErpIsNotifiedCorrectlyForPrice())
			.When(x => x.ValidInputForCancel(_contract.Id, 200))
			.Then(x => x.SendingCancelAndUndoPricedRequest())
			.And(x => x.TheResponseStatusIsSuccess())
			.And(x => x.TheErpIsNotifiedCorrectlyForCancel())
			.And(x => x.ClearCommodity(_commodity, _tenantData))
			.BDDfy();
	}

	async Task InitData(TenantData tenantData, string commodityName) =>
		await Fixture.WithDbContextFor(tenantData, async dbContext =>
		{
			_commodity = await dbContext.CreateCommodity(tenantData, commodityName);
			await dbContext.CreateBidsheets(_commodity);
			_bidsheet = dbContext.SettingsBidsheets.First(x => x.CommodityId == _commodity.Id && x.DeliveryLocationId == _deliveryLocation.Id);
			await dbContext.CreateBucketBalance(_tenantData, _commodity);
			await dbContext.AddAgvantageIdMapping(_tenantData, _commodity, _deliveryLocation);
		});

	async Task ClearCommodity(Commodity commodity, TenantData tenantData) =>
		await Fixture.WithDbContextFor(tenantData, async dbContext =>
		{
			var mappings = await dbContext.ErpIdMapping.ToListAsync();
			dbContext.ErpIdMapping.RemoveRange(mappings);
			await dbContext.ClearCommodity(commodity);
		});

	void ValidCreateContractInput(int quantity)
	{
		var futurePrice = 0m;
		var pushBasis = 0m;
		var postBasis = 0m;
		_customFields = [];

		if (_contractType == ContractTypeNames.Basis)
		{
			pushBasis = _pushBasisCreate;
			postBasis = _postBasis;
		}
		else if (_contractType == ContractTypeNames.HTA)
		{
			futurePrice = _futurePrice;
		}
		else if (_contractType == ContractTypeNames.FlatPrice)
		{
			pushBasis = _pushBasisCreate;
			postBasis = _postBasis;
			futurePrice = _futurePrice;
		}

		RequestData = new ContractDto
		{
			TransactionTypeId = TestData.TransactionTypes.Cash.Id,
			ContractTypeId = ContractTypeNames.DataFor(_contractType).Id,
			IsSell = false,
			CommodityId = _commodity.Id,
			LocationId = _tenantData.Locations.LakeFalls.Id,
			DeliveryLocationId = _deliveryLocation.Id,
			RegionId = _tenantData.Regions.Default.Id,
			IsDeliveryDatesCustom = false,
			DeliveryStartDate = _bidsheet.DeliveryStart,
			DeliveryEndDate = _bidsheet.DeliveryEnd,
			CropYear = _bidsheet.CropYear,
			CustomerId = _tenantData.Customers.LarryAdams.Id,
			EmployeeId = _tenantData.Employees.Edith.Id,
			FuturesMonth = _bidsheet.FutureMonth,
			FuturesPrice = futurePrice,
			PassFill = false,
			PostedBasis = postBasis,
			PushBasis = pushBasis,
			NetBasis = pushBasis + postBasis,
			Fees1 = 0.05m,
			Fees2 = 0,
			Quantity = quantity,
			Comments = $"Integration test to Create Contract with {_tenantData.Name}",
			CustomFields = []
		};
	}

	async Task ValidPriceInputForQuantity(int quantity)
	{
		_originalContract = _contract;
		RequestData = new ContractPriceDto
		{
			DeliveryLocationId = _originalContract.DeliveryLocationId,
			DeliveryStartDate = _originalContract.DeliveryStartDate,
			DeliveryEndDate = _originalContract.DeliveryEndDate,
			ExpirationDate = _originalContract.Expiration,
			PushBasis = _pushBasisPricing,
			FreightPrice = _originalContract.FreightPrice,
			Fees1 = _originalContract.Fees1,
			Fees2 = _originalContract.Fees2,
			Quantity = quantity,
			EmployeeId = _originalContract.EmployeeId,
			Comments = _originalContract.Comments,
			IsDeliveryDatesCustom = _originalContract.IsDeliveryDatesCustom,
			FuturesPrice = _contractType == ContractTypeNames.Basis ? 5.55m : _originalContract.FuturesPrice,
			PassTheFill = _originalContract.PassFill,
			FuturesMonth = _originalContract.FuturesMonth
		};
	}

	async Task AContractHasBeenCreatedWithQuantity(int quantity) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			_originalContract = _contractType switch
			{
				ContractTypeNames.Basis => Create.BasisContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _deliveryLocation,
					_tenantData.Customers.LarryAdams, _tenantData.Employees.Edith, 5.55m, quantity, false, _tenantData.Regions.Default.Id),
				ContractTypeNames.HTA => Create.HtaContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _deliveryLocation,
					_tenantData.Customers.LarryAdams, _tenantData.Employees.Edith, 5.55m, quantity, false, _tenantData.Regions.Default.Id),
				ContractTypeNames.FlatPrice => Create.FlatPriceContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _deliveryLocation,
					_tenantData.Customers.LarryAdams, _tenantData.Employees.Edith, 5.55m, quantity, false, _tenantData.Regions.Default.Id),
				_ => throw new NotImplementedException()
			};
			_originalContract.ChangePassFill(false);
			_originalContract.AssignContractNumberWithOutInformIt("P000077880001");
			dbContext.Contracts.Add(_originalContract);
			await dbContext.SaveChangesAsync();
		});

	void ValidInputForUndo(Guid contractId, decimal quantityForCancel) => 
		RequestData = $"id={contractId}&quantity={quantityForCancel}&transferCancelQuantityToParent=true";

	void ValidInputForCancel(Guid contractId, decimal quantityForCancel) =>
		RequestData = $"id={contractId}&quantity={quantityForCancel}&transferCancelQuantityToParent=false";

	async Task SendingCreateContractRequest() => Response = await Client.AuthenticatedAs(_tenantData.FakeUsers.Alice).PostAsync("/api/contracts", RequestData.ToJsonContent());

	async Task SendingPriceRequest() => Response = await Client.AuthenticatedAs(_tenantData.FakeUsers.Alice).PutAsync($"/api/contracts/price/{_originalContract.Id}", RequestData.ToJsonContent());

	async Task SendingCancelAndUndoPricedRequest() => Response = await Client.AuthenticatedAs(_tenantData.FakeUsers.Alice).PutAsync($"/api/contracts/cancel/?{RequestData}", null);

	async Task TheExpectedDataIsReturned()
	{
		var content = await Response.Content.ReadResultData<string>();
		content.Should().HaveLength(15);

		await Fixture.WithDbContextFor(_tenantData, async dbContext => _contract = await dbContext.Set<Contract>().Where(x => x.InternalCode == content).FirstAsync());
	}

	async Task TheErpIsNotifiedCorrectly() =>
		await Fixture.WithDbContextFor(_tenantData, dbContext =>
			Retry.UntilTrueOrTimeout(async () =>
			{
				var contract = await dbContext.Contracts.AsNoTracking().FirstOrDefaultAsync(x => x.Id == _contract.Id);
				contract.Should().NotBeNull();
				contract.ErpStatus.Should().Be(ErpStatus.Success);
				contract.Number.Should().NotBeEmpty();

				var erpLog = await dbContext.Set<ErpLog>()
					.AsNoTracking()
					.OrderByDescending(x => x.CreatedOn)
					.FirstOrDefaultAsync(x => x.ContractId == _contract.Id);

				if (erpLog == null)
					return false;

				erpLog.Action.Should().Be(ErpAction.Create);
				erpLog.RequestPayload.Should().NotBeNullOrEmpty();
				erpLog.ResponsePayload.Should().NotBeNullOrEmpty();
				erpLog.Succeed.Should().BeTrue();
				return true;
			}, 50.Seconds()));

	async Task TheErpIsNotifiedCorrectlyForPrice() =>
		await Fixture.WithDbContextFor(_tenantData, dbContext =>
			Retry.UntilTrueOrTimeout(async () =>
			{
				var erpLog = await dbContext.Set<ErpLog>()
					.AsNoTracking()
					.OrderByDescending(x => x.CreatedOn)
					.FirstOrDefaultAsync(x => x.ContractId == _contract.Id && x.Action == ErpAction.Price);

				if (erpLog == null)
					return false;

				if (erpLog.RequestPayload == null)
					return false;

				return true;
			}, 50.Seconds()));

	async Task TheErpIsNotifiedCorrectlyForUndo() =>
		await Fixture.WithDbContextFor(_tenantData, dbContext =>
			Retry.UntilTrueOrTimeout(async () =>
			{
				var erpLog = await dbContext.Set<ErpLog>()
					.AsNoTracking()
					.OrderByDescending(x => x.CreatedOn)
					.FirstOrDefaultAsync(x => x.ContractId == _contract.Id && x.Action == ErpAction.Cancel);

				if (erpLog == null)
					return false;
				if (erpLog.RequestPayload == null)
					return false;

				return true;
			}, 50.Seconds()));

	async Task TheErpIsNotifiedCorrectlyForCancel() =>
		await Fixture.WithDbContextFor(_tenantData, dbContext =>
			Retry.UntilTrueOrTimeout(async () =>
			{
				var erpLogList = await dbContext.Set<ErpLog>()
					.AsNoTracking()
					.OrderByDescending(x => x.CreatedOn)
					.Where(x => x.ContractId == _contract.Id && x.RequestPayload != null && x.Action == ErpAction.Cancel)
					.ToListAsync();

				if (erpLogList == null)
					return false;
				erpLogList.Count.Should().Be(3);

				var erpLog = await dbContext.Set<ErpLog>()
					.AsNoTracking()
					.OrderByDescending(x => x.CreatedOn)
					.FirstOrDefaultAsync(x => x.ContractId == _contract.Id && x.Action == ErpAction.Price);

				if (erpLog == null)
					return false;
				if (erpLog.RequestPayload == null)
					return false;

				return true;
			}, 50.Seconds()));
}
