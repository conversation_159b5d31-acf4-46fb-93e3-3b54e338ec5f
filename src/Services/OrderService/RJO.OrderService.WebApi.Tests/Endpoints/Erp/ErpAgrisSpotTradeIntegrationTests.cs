using FluentAssertions;
using FluentAssertions.Extensions;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common.Http;
using RJO.BuildingBlocks.Testing;
using RJO.OrderService.Services.ErpIntegration.AgrisServices.Model;
using TestStack.BDDfy;
using TestStack.BDDfy.Xunit;
using Xunit.Abstractions;

namespace RJO.OrderService.WebApi.Tests.Endpoints.Erp;

public class ErpAgrisSpotTradeIntegrationTests : EndpointOutboundHttpTests
{
	readonly TenantData _tenantData;
	readonly FakeUser _fakeUser;
	readonly string _number;

	public ErpAgrisSpotTradeIntegrationTests(OrderService fixture, ITestOutputHelper outputHelper) : base(fixture, outputHelper)
	{
		_tenantData = TestData.PoinsettRice;
		_fakeUser = _tenantData.FakeUsers.Agris;
		_number = "41105017";
	}

	[BddfyFact]
	void Scenario1() => this
		.Given(x => x.ValidInput())
		.When(x => x.SendingRequest())
		.Then(x => x.TheResponseStatusIsSuccess())
		.And(x => x.ContractWasCreated())
		.BDDfy();

	[BddfyFact]
	void Scenario_InvalidPrice() => this
		.Given(x => x.InvalidInput())
		.When(x => x.SendingRequest())
		.Then(x => x.TheResponseStatusIsBadRequest())
		.BDDfy();

	[BddfyFact]
	void Scenario_ZeroContractPrice() => this
		.Given(x => x.ZeroContractPriceInput())
		.When(x => x.SendingRequest())
		.Then(x => x.TheResponseStatusIsSuccess())
		.BDDfy();

	[BddfyFact]
	void Scenario_SaleReversal() => this
		.Given(x => x.ValidInput_reversal())
		.When(x => x.SendingRequest())
		.Then(x => x.TheResponseStatusIsSuccess())
		.And(x => x.ContractWasCreated_Reversal())
		.BDDfy();

	[BddfyFact]
	void Scenario_InactiveLocation()
	{
		var location = _tenantData.Locations.LakeFalls;
		this
			.Given(x => x.InvalidInput_InactiveLocation())
			.When(x => x.SendingRequest())
			.Then(x => x.TheResponseStatusIsBadRequest())
			.BDDfy();
	}

	void InvalidInput() => RequestData = new AgrisSpotTradeDto
	{
		NameId = _tenantData.Customers.RichFranz.Number,
		Location = _tenantData.Locations.Deerfield.Number,
		Commodity = _tenantData.Commodities.Rice.Number,
		PurchaseSalesCode = "P",
		PositionDateTime = DateTime.Now.AddMonths(6).ToString("MM/dd/yyyy"),
		DocumentLocation = _tenantData.Locations.Deerfield.Number,
		DocumentNumber = $"S{_number}",
		ContractFuturesMonth = DateTime.Now.AddMonths(6).ToString("yyyy-MM"),
		ContractPrice = 12.5M,
		FuturesPrice = 12.72512M,
		StorageUOM = null,
		Units = 10000,
		BasisPrice = -0.3M,
		ContractType = "S",
		TransactionTypeId = "7",
		IsDelete = false,
	};

	void ValidInput() => RequestData = new AgrisSpotTradeDto
	{
		NameId = _tenantData.Customers.RichFranz.Number,
		Location = _tenantData.Locations.Deerfield.Number,
		Commodity = _tenantData.Commodities.Rice.Number,
		PurchaseSalesCode = "P",
		PositionDateTime = DateTime.Now.AddMonths(6).ToString("MM/dd/yyyy"),
		DocumentLocation = _tenantData.SpotToGroupedLocationMaps.First.DocumentLocation,
		DocumentNumber = $"S{_number}",
		ContractFuturesMonth = DateTime.Now.AddMonths(6).ToString("yyyy-MM"),
		ContractPrice = 12.5M,
		FuturesPrice = 12.725M,
		StorageUOM = null,
		Units = 10000,
		BasisPrice = -0.3M,
		ContractType = "S",
		TransactionTypeId = "7",
		IsDelete = false,
	};

	void ValidInput_reversal() => RequestData = new AgrisSpotTradeDto
	{
		NameId = _tenantData.Customers.RichFranz.Number,
		Location = _tenantData.Locations.Deerfield.Number,
		Commodity = _tenantData.Commodities.Rice.Number,
		PurchaseSalesCode = "S",
		PositionDateTime = DateTime.Now.AddMonths(6).ToString("MM/dd/yyyy"),
		DocumentLocation = _tenantData.SpotToGroupedLocationMaps.First.DocumentLocation,
		DocumentNumber = $"S{_number}",
		ContractFuturesMonth = DateTime.Now.AddMonths(6).ToString("yyyy-MM"),
		ContractPrice = 12.5M,
		FuturesPrice = 12.725M,
		StorageUOM = null,
		Units = 100,
		BasisPrice = -0.3M,
		ContractType = "S",
		TransactionTypeId = "7",
		IsDelete = false,
	};

	void InvalidInput_InactiveLocation() =>
		RequestData = new AgrisSpotTradeDto
		{
			NameId = _tenantData.Customers.RichFranz.Number,
			Location = _tenantData.Locations.InactiveLocation.Number,
			Commodity = _tenantData.Commodities.Rice.Number,
			PurchaseSalesCode = "P",
			PositionDateTime = DateTime.Now.AddMonths(6).ToString("MM/dd/yyyy"),
			DocumentLocation = _tenantData.SpotToGroupedLocationMaps.InactiveGroupLocation.DocumentLocation,
			DocumentNumber = $"S{_number}",
			ContractFuturesMonth = DateTime.Now.AddMonths(6).ToString("yyyy-MM"),
			ContractPrice = 12.5M,
			FuturesPrice = 12.725M,
			StorageUOM = null,
			Units = 10000,
			BasisPrice = -0.3M,
			ContractType = "S",
			TransactionTypeId = "7",
			IsDelete = false,
		};

	async Task SendingRequest() => Response = await Client.AuthenticatedAs(_fakeUser).PostAsync($"/api/agris/hedgeTransaction/orders", RequestData.ToJsonContent());

	async Task ContractWasCreated() =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			await Retry.UntilTrueOrTimeout(async () =>
			{
				var contract = await dbContext.Contracts.FirstOrDefaultAsync(x => x.Number.EndsWith(_number));
				if (contract == null)
				{
					return false;
				}
				if (_tenantData.SpotToGroupedLocationMaps.First.GroupedLocation.DestinationLocationId.HasValue)
				{
					contract.DeliveryLocationId.Should().Be(_tenantData.SpotToGroupedLocationMaps.First.GroupedLocation.DestinationLocationId.Value);
				}
				contract.LocationId.Should().Be(_tenantData.Locations.Deerfield.Id);
				return true;
			}, 150.Seconds());
		});

	async Task ContractWasCreated_Reversal() => await Fixture.WithDbContextFor(_tenantData, async dbContext =>
	{
		await Retry.UntilTrueOrTimeout(async () =>
		{
			var contract = await dbContext.Contracts.FirstOrDefaultAsync(x => x.Number.EndsWith(_number));
			if (contract == null)
			{
				return false;
			}
			if (_tenantData.SpotToGroupedLocationMaps.First.GroupedLocation.DestinationLocationId.HasValue)
			{
				contract.DeliveryLocationId.Should().Be(_tenantData.SpotToGroupedLocationMaps.First.GroupedLocation.DestinationLocationId.Value);
			}
			contract.LocationId.Should().Be(_tenantData.Locations.Deerfield.Id);
			contract.IsSell.Should().BeFalse();
			return true;
		}, 150.Seconds());
	});

	void ZeroContractPriceInput() => RequestData = new AgrisSpotTradeDto
	{
		NameId = _tenantData.Customers.RichFranz.Number,
		Location = _tenantData.Locations.Deerfield.Number,
		Commodity = _tenantData.Commodities.Rice.Number,
		PurchaseSalesCode = "P",
		PositionDateTime = DateTime.Now.AddMonths(6).ToString("MM/dd/yyyy"),
		DocumentLocation = _tenantData.Locations.Deerfield.Number,
		DocumentNumber = $"S{_number}",
		ContractFuturesMonth = DateTime.Now.AddMonths(6).ToString("yyyy-MM"),
		ContractPrice = 0M,
		FuturesPrice = 0M,
		StorageUOM = null,
		Units = 10000,
		BasisPrice = -0.3M,
		ContractType = "S",
		TransactionTypeId = "7",
		IsDelete = false,
	};
}
