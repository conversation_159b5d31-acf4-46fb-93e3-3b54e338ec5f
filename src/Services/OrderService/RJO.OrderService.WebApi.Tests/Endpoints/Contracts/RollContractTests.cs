using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common.Http;
using RJO.BuildingBlocks.Testing;
using RJO.OrderService.Domain;
using RJO.OrderService.Services.DTO.Contract;
using RJO.OrderService.WebApi.Testing;
using TestStack.BDDfy;
using TestStack.BDDfy.Xunit;
using Xunit;
using Xunit.Abstractions;

namespace RJO.OrderService.WebApi.Tests.Endpoints.Contracts;

public class RollContractTests(OrderService fixture, ITestOutputHelper outputHelper) : EndpointOutboundHttpTests(fixture, outputHelper)
{
	Contract _originalContract;

	string _contractType;
	TenantData _tenantData;
	Commodity _commodity;
	Product _product;

	[BddfyTheory]
	[InlineData(ContractTypeNames.HTA, TenantNames.MidIowa, CommodityNames.Corn)]
	// [InlineData(ContractTypeNames.Basis, TenantNames.MidIowa, CommodityNames.Wheat)]
	void Scenario1(string contractType, string tenant, string commodity)
	{
		_contractType = contractType;
		_tenantData = TestData.For(tenant);
		_commodity = _tenantData.Commodities.For(commodity);
		_product = _tenantData.Products.GetById(_commodity.ProductId);
		this
			.Given(x => x.AContractHasBeenCreatedWithQuantity(30000))
			.And(x => x.ValidInputForQuantity(3000))
			.When(x => x.SendingRequest())
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.AChildContractIsCreatedForQuantityAndGrossRemainingBalance(3000))
			.And(x => x.TheParentContractIsUpdatedToHaveGrossRemainingBalanceAndLastTransactionQuantity(27000, 30000))
			.BDDfy();
	}

	async Task AContractHasBeenCreatedWithQuantity(int quantity) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			_originalContract = _contractType switch
			{
				ContractTypeNames.Basis => Create.BasisContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _tenantData.Locations.Oakbrook,
					_tenantData.Customers.RichFranz, _tenantData.Employees.Bob, 5.55m, quantity, false, _tenantData.Regions.Default.Id),
				ContractTypeNames.HTA => Create.HtaContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _tenantData.Locations.Oakbrook,
					_tenantData.Customers.RichFranz, _tenantData.Employees.Bob, 5.55m, quantity, false, _tenantData.Regions.Default.Id),
				ContractTypeNames.FlatPrice => Create.FlatPriceContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _tenantData.Locations.Oakbrook,
					_tenantData.Customers.RichFranz, _tenantData.Employees.Bob, 5.55m, quantity, false, _tenantData.Regions.Default.Id),
				_ => throw new NotImplementedException()
			};
			_originalContract.ChangePassFill(false);
			dbContext.Set<Contract>().Add(_originalContract);
			await dbContext.SaveChangesAsync();
		});

	void ValidInputForQuantity(int quantity) =>
		RequestData = new ContractRollDto
		{
			DeliveryStartDate = _originalContract.DeliveryStartDate,
			DeliveryEndDate = _originalContract.DeliveryEndDate,
			PushBasis = _originalContract.PushBasis,
			PostedBasis = _originalContract.PostedBasis,
			FreightPrice = _originalContract.FreightPrice,
			Fees1 = _originalContract.Fees1,
			Fees2 = _originalContract.Fees2,
			Quantity = quantity,
			EmployeeId = _originalContract.EmployeeId,
			Comments = _originalContract.Comments,
			IsDeliveryDatesCustom = _originalContract.IsDeliveryDatesCustom,
			FuturesPrice = _originalContract.FuturesPrice,
			CropYear = _originalContract.CropYear,
			ExpirationDate = _originalContract.Expiration,
			FuturesMonth = _product.RandomFuturesMonth(TestData.CurrentYear),
		};

	async Task SendingRequest() => Response = await Client.AuthenticatedAs(_tenantData.FakeUsers.Bob).PutAsync($"/api/contracts/roll/{_originalContract.Id}", RequestData.ToJsonContent());

	async Task AChildContractIsCreatedForQuantityAndGrossRemainingBalance(int grossRemainingBalance) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var childContract = await dbContext.Contracts.AsNoTracking().FirstOrDefaultAsync(x => x.ParentId == _originalContract.Id);
			childContract.Should().NotBeNull();
			childContract.GrossRemainingBalance.Should().Be(grossRemainingBalance);
		});

	async Task TheParentContractIsUpdatedToHaveGrossRemainingBalanceAndLastTransactionQuantity(int grossRemainingBalance, int lastTransactionQuantity) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var parentContract = await dbContext.Contracts.FindAsync(_originalContract.Id);
			parentContract.Should().NotBeNull();
			parentContract!.GrossRemainingBalance.Should().Be(grossRemainingBalance);
			parentContract.LastTransactionQuantity.Should().Be(lastTransactionQuantity);
		});
}
