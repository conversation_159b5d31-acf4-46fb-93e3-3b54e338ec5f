using RJO.BuildingBlocks.Testing;
using RJO.OrderService.Services.DTO.Contract;
using TestStack.BDDfy.Xunit;
using TestStack.BDDfy;
using Xunit.Abstractions;
using FluentAssertions;
using RJO.OrderService.Domain;
using RJO.BuildingBlocks.Common.Http;
using Microsoft.EntityFrameworkCore;
using RJO.OrderService.WebApi.Testing;
using Xunit;
using RJO.OrderService.Services.DTO.RJO;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.ERP;
using FluentAssertions.Extensions;
using RJO.OrderService.WebApi.Tests.DatabaseInitialization;
using RJO.OrderService.Services.Services;

namespace RJO.OrderService.WebApi.Tests.Endpoints.Contracts;

public class PriceContractTests : EndpointOutboundHttpTests
{
	Contract _originalContract;
	string _contractType;
	TenantData _tenantData;
	Commodity _commodity;
	OrderMetadataConfigurations _orderMetadataConfigurations;
	ContractMetadata[] _originalContractMetadata;

	public PriceContractTests(OrderService fixture, ITestOutputHelper outputHelper) : base(fixture, outputHelper) { }

	[BddfyTheory]
	[InlineData(ContractTypeNames.Basis, TenantNames.PoinsettRice, CommodityNames.Corn)]
	[InlineData(ContractTypeNames.HTA, TenantNames.PoinsettRice, CommodityNames.Corn)]
	void Scenario1(string contractType, string tenant, string commodity)
	{
		_contractType = contractType;
		_tenantData = TestData.For(tenant);
		_commodity = _tenantData.Commodities.For(commodity);
		_orderMetadataConfigurations = _tenantData.OrderMetadataConfigurations;

		this
			.Given(x => x.AContractHasBeenCreatedWithQuantity1(30000))
			.And(x => x.ValidInputForQuantity(3000))
			.And(x => x.RjoRespondsWithExpectedData(_tenantData, commodity))
			.When(x => x.SendingRequest())
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.AChildContractIsCreatedForQuantityAndGrossRemainingBalance1(3000))
			.And(x => x.TheParentContractIsUpdatedToHaveGrossRemainingBalanceAndLastTransactionQuantity(27000, 30000))
			.BDDfy();
	}

	[BddfyTheory]
	[InlineData(ContractTypeNames.HTA, TenantNames.EnterpriseGrain, CommodityNames.Corn)]
	void Scenario2(string contractType, string tenant, string commodity)
	{
		_contractType = contractType;
		_tenantData = TestData.For(tenant);
		_commodity = _tenantData.Commodities.For(commodity);

		this
			.Given(x => x.AContractHasBeenCreatedWithQuantity2(30000, false))
			.And(x => x.ValidInputForQuantity(3000))
			.And(x => x.Fixture.TheErpRespondsWithoutErrors(_tenantData))
			.And(x => x.RjoRespondsWithExpectedData(_tenantData, commodity))
			.When(x => x.SendingRequest())
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.AChildContractIsCreatedForQuantityAndGrossRemainingBalance2(3000))
			.And(x => x.TheParentContractIsUpdatedToHaveGrossRemainingBalanceAndLastTransactionQuantity(27000, 30000))
			.And(x => x.TheErpIsNotifiedCorrectly())
			.BDDfy();
	}

	[BddfyTheory]
	[InlineData(ContractTypeNames.Basis, TenantNames.PoinsettRice, CommodityNames.Corn)]
	void Scenario3(string contractType, string tenant, string commodity)
	{
		_contractType = contractType;
		_tenantData = TestData.For(tenant);
		_commodity = _tenantData.Commodities.For(commodity);
		_orderMetadataConfigurations = _tenantData.OrderMetadataConfigurations;

		this
			.Given(x => x.PreCleanup())
			.And(x => x.AContractHasBeenCreatedWithQuantity2(11000, true))
			.And(x => x.BucketBalanceWasAlreadyCreated(2500m))
			.And(x => x.ValidInputForPricingEFP(11000, 15000))
			.When(x => x.SendingRequestPriceViaEFP())
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.TheBucketBalanceAndTransactionAreCorrect(1500m))
			.BDDfy();
	}

	[BddfyTheory]
	[InlineData(ContractTypeNames.Basis, TenantNames.PoinsettRice, CommodityNames.Corn)]
	void Scenario_EditParent(string contractType, string tenant, string commodity)
	{
		_contractType = contractType;
		_tenantData = TestData.For(tenant);
		_commodity = _tenantData.Commodities.For(commodity);
		_orderMetadataConfigurations = _tenantData.OrderMetadataConfigurations;

		this.Given(x => x.PreCleanup())
			.And(x => x.AContractHasBeenCreatedWithQuantity1(5000))
			.And(x => x.ValidInputForQuantity(5000))
			.And(x => x.RjoRespondsWithExpectedData(_tenantData, commodity))
			.When(x => x.SendingRequest())
			.Then(x => x.TheResponseStatusIsSuccess())
			.When(x => x.ValidInputForUpdate())
			.And(x=> x.SendingRequestEdit())
			.Then(x => x.TheParentContractIsUpdatedWithoutPriceCalculation())
			.BDDfy();
	}

	Task PreCleanup() =>
		Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			dbContext.BucketBalanceContract.RemoveRange(dbContext.BucketBalanceContract);
			await dbContext.SaveChangesAsync();

			dbContext.BucketBalance.RemoveRange(dbContext.BucketBalance);
			await dbContext.SaveChangesAsync();

			dbContext.MarketTransactions.RemoveRange(dbContext.MarketTransactions);
			await dbContext.SaveChangesAsync();
		});

	void ValidInputForQuantity(int quantity) => RequestData = new ContractPriceDto
	{
		DeliveryLocationId = _originalContract.DeliveryLocationId,
		DeliveryStartDate = _originalContract.DeliveryStartDate,
		DeliveryEndDate = _originalContract.DeliveryEndDate,
		ExpirationDate = _originalContract.Expiration,
		PushBasis = -0.3500m,
		FreightPrice = _originalContract.FreightPrice,
		Fees1 = _originalContract.Fees1,
		Fees2 = _originalContract.Fees2,
		Quantity = quantity,
		EmployeeId = _originalContract.EmployeeId,
		Comments = _originalContract.Comments,
		IsDeliveryDatesCustom = _originalContract.IsDeliveryDatesCustom,
		FuturesPrice = _contractType == ContractTypeNames.Basis ? 5.55m : _originalContract.FuturesPrice,
		PassTheFill = _originalContract.PassFill,
		FuturesMonth = _originalContract.FuturesMonth
	};

	void ValidInputForUpdate() => RequestData = new ContractUpdateDto
	{
		DeliveryLocationId = _originalContract.DeliveryLocationId,
		DeliveryStartDate = _originalContract.DeliveryStartDate,
		DeliveryEndDate = _originalContract.DeliveryEndDate,
		ExpirationDate = _originalContract.Expiration,
		PushBasis = -0.3500m,
		FreightPrice = _originalContract.FreightPrice,
		Fees1 = _originalContract.Fees1,
		Fees2 = _originalContract.Fees2,
		Quantity = _originalContract.Quantity,
		EmployeeId = _originalContract.EmployeeId,
		Comments = "new comments",
		IsDeliveryDatesCustom = _originalContract.IsDeliveryDatesCustom,	
		FuturesMonth = _originalContract.FuturesMonth
	};

	void ValidInputForPricingEFP(int quantity, int efpQuantity) => RequestData = new ContractViaEFPDto
	{
		DeliveryLocationId = _originalContract.DeliveryLocationId,
		DeliveryStartDate = _originalContract.DeliveryStartDate,
		DeliveryEndDate = _originalContract.DeliveryEndDate,
		ExpirationDate = _originalContract.Expiration,
		FreightPrice = _originalContract.FreightPrice,
		Fees1 = _originalContract.Fees1,
		Fees2 = _originalContract.Fees2,
		FuturesPrice = 6m,
		Quantity = quantity,
		EFPQuantity = efpQuantity,
		EmployeeId = _originalContract.EmployeeId,
		Comments = _originalContract.Comments,
		IsMinimumEFP = false,
		IsDeliveryDatesCustom = _originalContract.IsDeliveryDatesCustom,
	};

	void RjoRespondsWithExpectedData(TenantData tenantData, string commodity)
	{
		var maturityMonth = TestData.FutureMonths[_originalContract.FuturesMonth[0]].ToString("00");
		var dataResponseItem1 = new DataResponseItem
		{
			AskPrice = 5.8125m,
			BidPrice = 5.81m,
			LastPrice = 5.8125m,
			HighPrice = 5.815m,
			LowPrice = 5.7825m,
			OpenPrice = 5.785m,
			PrevSettledPrice = 6.7775m,
			LastUpdatedTimestamp = DateTime.UtcNow.AddSeconds(-5),
			SettledPrice = 6.7775m,
			SettledDate = DateTime.UtcNow.Date,
			SettledTimestamp = DateTime.UtcNow.Date.AddHours(2),
			Symbol = tenantData.Products.Wheat.ExchangeSymbol,
			MaturityDate = $"{TestData.CurrentYear}{maturityMonth}",
			FrontMonths = 2
		};
		var dataResponseItem2 = new DataResponseItem
		{
			AskPrice = 5.4325m,
			BidPrice = 5.41m,
			LastPrice = 5.4325m,
			HighPrice = 5.415m,
			LowPrice = 5.4325m,
			OpenPrice = 5.485m,
			PrevSettledPrice = 5.4775m,
			LastUpdatedTimestamp = DateTime.UtcNow.AddSeconds(-5),
			SettledPrice = 5.4775m,
			SettledDate = DateTime.UtcNow.Date,
			SettledTimestamp = DateTime.UtcNow.Date.AddHours(3),
			Symbol = tenantData.Products.Wheat.ExchangeSymbol,
			MaturityDate = $"{TestData.CurrentYear}{maturityMonth}",
			FrontMonths = 2
		};
		
		var expectedRequestContentForValidInput = new DataRequest()
		{
			Requestor = _tenantData.FakeUsers.Bob.Email,
			DisplayType = DisplayType.NonDisplay,
			DelayType = DelayType.Realtime,
			ContractList = new ContractKey[]
			{
				new()
				{
					SecurityType = SecurityType.Future,
					Symbol = _tenantData.Products.FirstOrDefault(a => a.Description == commodity).ExchangeSymbol,
					MaturityDate = $"{TestData.CurrentYear}{maturityMonth}",
					FrontMonths = 0
				}
			}
		};

		Fixture.OutboundHttp.RespondWithJsonFor(Fixture.RjoApi.PortalWebOeHrvystMarketData(expectedRequestContentForValidInput),
			new DataResponse
			{
				ResultCode = DataResultCode.OK,
				HttpStatusCode = 200,
				DelayType = DelayType.Delayed,
				ResponseList = new[] { dataResponseItem1, dataResponseItem2 }
			});
	}

	async Task AContractHasBeenCreatedWithQuantity1(int quantity) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			_originalContract = _contractType switch
			{
				ContractTypeNames.Basis => Create.BasisContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _tenantData.Locations.Oakbrook,
					_tenantData.Customers.RichFranz, _tenantData.Employees.Bob, 5.55m, quantity, false, _tenantData.Regions.Default.Id),
				ContractTypeNames.HTA => Create.HtaContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _tenantData.Locations.Oakbrook,
					_tenantData.Customers.RichFranz, _tenantData.Employees.Bob, 5.55m, quantity, false, _tenantData.Regions.Default.Id),
				ContractTypeNames.FlatPrice => Create.FlatPriceContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _tenantData.Locations.Oakbrook,
					_tenantData.Customers.RichFranz, _tenantData.Employees.Bob, 5.55m, quantity, false, _tenantData.Regions.Default.Id),
				_ => throw new NotImplementedException()
			};
			_originalContract.ChangePassFill(false);
			dbContext.Set<Contract>().Add(_originalContract);

			_originalContractMetadata = new[]
			{
				Create.ContractMetadata(_originalContract.Id, "6.18", _orderMetadataConfigurations.NumericProperty.Id),
				Create.ContractMetadata(_originalContract.Id, "Text618", _orderMetadataConfigurations.AlphanumericProperty.Id),
			};
			dbContext.Set<ContractMetadata>().AddRange(_originalContractMetadata);

			await dbContext.SaveChangesAsync();
		});

	async Task AContractHasBeenCreatedWithQuantity2(int quantity, bool isSell = false) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			_originalContract = _contractType switch
			{
				ContractTypeNames.Basis => Create.BasisContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _tenantData.Locations.Oakbrook,
					_tenantData.Customers.RichFranz, _tenantData.Employees.Bob, 5.55m, quantity, isSell, _tenantData.Regions.Default.Id),
				ContractTypeNames.HTA => Create.HtaContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _tenantData.Locations.Oakbrook,
					_tenantData.Customers.RichFranz, _tenantData.Employees.Bob, 5.55m, quantity, isSell, _tenantData.Regions.Default.Id),
				ContractTypeNames.FlatPrice => Create.FlatPriceContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _tenantData.Locations.Oakbrook,
					_tenantData.Customers.RichFranz, _tenantData.Employees.Bob, 5.55m, quantity, isSell, _tenantData.Regions.Default.Id),
				_ => throw new NotImplementedException()
			};

			_originalContract.ChangePassFill(false);
			dbContext.Set<Contract>().Add(_originalContract);

			await dbContext.SaveChangesAsync();
		});

	async Task BucketBalanceWasAlreadyCreated(decimal balance) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var bucketBalance = new BucketBalance(balance, 0, 0, 0, _commodity.Id, _tenantData.Regions.Default.Id, TestData.CurrentYear);
			dbContext.Set<BucketBalance>().Add(bucketBalance);
			await dbContext.SaveChangesAsync();
		});

	async Task SendingRequest() => Response = await Client.AuthenticatedAs(_tenantData.FakeUsers.Bob).PutAsync($"/api/contracts/price/{_originalContract.Id}", RequestData.ToJsonContent());
	async Task SendingRequestPriceViaEFP() => Response = await Client.AuthenticatedAs(_tenantData.FakeUsers.Bob).PutAsync($"/api/contracts/priceviaefp/{_originalContract.Id}", RequestData.ToJsonContent());
	async Task SendingRequestEdit() => Response = await Client.AuthenticatedAs(_tenantData.FakeUsers.Bob).PutAsync($"/api/contracts?id={_originalContract}", RequestData.ToJsonContent());

	async Task AChildContractIsCreatedForQuantityAndGrossRemainingBalance1(int grossRemainingBalance) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var childContract = await dbContext.Contracts.AsNoTracking().FirstOrDefaultAsync(x => x.ParentId == _originalContract.Id);
			childContract.Should().NotBeNull();
			childContract.GrossRemainingBalance.Should().Be(grossRemainingBalance);

			var metadataService = new MetadataDomainService(dbContext);
			var fields = metadataService.GetConTractFields(childContract.Id);
			fields.Should().NotBeNull();
			fields.Length.Should().Be(_originalContractMetadata.Length);

			var childContractMetadata = await dbContext.ContractMetadata.AsNoTracking().Where(x => x.ContractId == childContract.Id).ToListAsync();
			childContractMetadata.Should().NotBeNull();
			childContractMetadata.Count.Should().Be(_originalContractMetadata.Length);

			foreach(var item in _originalContractMetadata)
			{
				var field = fields.First(x => x.FieldId == item.FieldId);
				field.Value.Should().Be(item.Value);

				var metadata = childContractMetadata.First(x => x.FieldId == item.FieldId);
				metadata.Value.Should().Be(item.Value);
			}
		});

	async Task AChildContractIsCreatedForQuantityAndGrossRemainingBalance2(int grossRemainingBalance) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var childContract = await dbContext.Contracts.AsNoTracking().FirstOrDefaultAsync(x => x.ParentId == _originalContract.Id);
			childContract.Should().NotBeNull();
			childContract.GrossRemainingBalance.Should().Be(grossRemainingBalance);
		});

	async Task TheParentContractIsUpdatedToHaveGrossRemainingBalanceAndLastTransactionQuantity(int grossRemainingBalance, int lastTransactionQuantity) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var parentContract = await dbContext.Contracts.FindAsync(_originalContract.Id);
			parentContract.Should().NotBeNull();
			parentContract.GrossRemainingBalance.Should().Be(grossRemainingBalance);
			parentContract.LastTransactionQuantity.Should().Be(lastTransactionQuantity);
		});

	async Task TheParentContractIsUpdatedWithoutPriceCalculation() =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var parentContract = await dbContext.Contracts.FindAsync(_originalContract.Id);
			parentContract.Should().NotBeNull();
			parentContract.FuturesPrice.Should().BeNull();
			parentContract.Price.Should().Be(0);
		});

	async Task TheBucketBalanceAndTransactionAreCorrect(decimal newBalance) =>
		await Fixture.WithDbContextFor(_tenantData, dbContext =>
			Retry.UntilTrueOrTimeout(async () =>
			{
				var bucket = await dbContext.Set<BucketBalance>()
					.AsNoTracking()
					.OrderByDescending(x => x.CreatedOn)
					.FirstOrDefaultAsync(x => x.CommodityId == _commodity.Id && x.RegionId == _tenantData.Regions.Default.Id); 
				if (bucket == null)
					return false;
				bucket.Balance().Should().Be(newBalance);

				var marketTransaction = await dbContext.Set<MarketTransaction>()
					.AsNoTracking()
					.FirstOrDefaultAsync(x => x.Source == EMarketTransactionSource.Accumulation && x.CommodityId == _commodity.Id);
				if (marketTransaction == null)
					return false;
				marketTransaction.Quantity.Should().Be(_commodity.LotFactor);
				marketTransaction.IsSell.Should().BeTrue();
				return true;
			}, 50.Seconds()));

	async Task TheErpIsNotifiedCorrectly() =>
		await Fixture.WithDbContextFor(_tenantData, dbContext =>
			Retry.UntilTrueOrTimeout(async () =>
			{
				var contract = await dbContext.Contracts
					.AsNoTracking()
					.FirstOrDefaultAsync(x => x.ParentId == _originalContract.Id);

				contract.Should().NotBeNull();
				
				var erpLog = await dbContext.Set<ErpLog>()
					.AsNoTracking()
					.OrderByDescending(x => x.CreatedOn)
					.FirstOrDefaultAsync(x => x.ContractId == contract.Id);

				if (erpLog == null)
					return false;

				erpLog.Action.Should().Be(ErpAction.Price);

				if (contract.Number == null)
					return false;
				
				await Fixture.VerifyErpAction(dbContext, _tenantData, erpLog, contract);

				return true;
			}, 50.Seconds()));
}
