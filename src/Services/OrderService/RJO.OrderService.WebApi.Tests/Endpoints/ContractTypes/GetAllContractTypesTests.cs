using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Testing;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Services.DTO.ContractType;
using TestStack.BDDfy;
using TestStack.BDDfy.Xunit;
using Xunit.Abstractions;

namespace RJO.OrderService.WebApi.Tests.Endpoints.ContractTypes;

public class GetAllContractTypesTests : EndpointOutboundHttpTests
{
	readonly TenantData _tenantData = TestData.MidIowa;

	public GetAllContractTypesTests(OrderService fixture, ITestOutputHelper outputHelper) : base(fixture, outputHelper) { }

	[BddfyFact]
	void Scenario1() => this
		.Given(x => x.SendingRequest())
		.Then(x => x.TheResponseStatusIsSuccess())
		.And(x => x.TheExpectedDataIsReturned())
		.BDDfy();

	async Task SendingRequest() => Response = await Client.AuthenticatedAs(_tenantData.FakeUsers.Bob).GetAsync($"api/ContractTypes");

	async Task TheExpectedDataIsReturned() =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var contents = await Response.Content.ReadResultData<List<ContractTypeDto>>();

			contents.Count.Should().BeGreaterOrEqualTo(0);

			var traditionalContractTypesQuery = dbContext.ContractTypes
				.Where(x => x.Id != ContractTypeDictionary.NTC)
				.OrderBy(x => x.Name)
				.Select(x => new ContractTypeDto
				{
					Id = x.Id,
					Name = x.Name,
					Code = x.Code,
					IsActive = x.IsActive,
					TypeId = x.Id,
					ErpNumber = ""
				});

			var extendedContractTypesQuery = dbContext.ExtendedContractTypes
				.Where(x => x.TenantId == _tenantData.Id)
				.OrderBy(x => x.Name)
				.Select(x => new ContractTypeDto
				{
					Id = x.Id,
					Name = x.Name,
					Code = x.Code,
					IsActive = x.IsActive,
					TypeId = x.Type,
					ErpNumber = x.Number.ToString(),
				});

			var result = new List<ContractTypeDto>();
			result.AddRange(await traditionalContractTypesQuery.ToListAsync());
			result.AddRange(await extendedContractTypesQuery.ToListAsync());

			contents.Count.Should().Be(result.Count);
		});
}
