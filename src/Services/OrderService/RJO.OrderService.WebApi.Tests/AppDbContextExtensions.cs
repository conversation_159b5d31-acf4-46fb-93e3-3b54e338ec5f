using Microsoft.EntityFrameworkCore;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.ERP;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.ErpIntegration;

namespace RJO.OrderService.WebApi.Tests;

public static class AppDbContextExtensions
{
	public static async Task<Commodity> CreateCommodity(this AppDbContext dbContext, TenantData tenantData, string commodityName)
	{
		var commodity = Create.Commodity(commodityName, 0.1m, 0.2m, tenantData.Products.Corn.Id);		
		dbContext.Commodities.Add(commodity);
		await dbContext.SaveChangesAsync();
		return commodity;
	}

	public static async Task CreateHedgeMappings(this AppDbContext dbContext, IEnumerable<HedgeMapping> hedgeMappings)
	{
		dbContext.HedgeMappings.AddRange(hedgeMappings);
		await dbContext.SaveChangesAsync();
	}
	
	public static async Task CreateBidsheets(this AppDbContext dbContext, Commodity commodity, string month = null)
	{
		var deliveryStart = TestData.CurrentMonthPlus1Start;
		var deliveryEnd = TestData.CurrentMonthPlus3Start;
		var year = TestData.CurrentYear;
		foreach (var location in dbContext.Locations)
		{
			var product = await dbContext.Products.FirstOrDefaultAsync(a => a.Id == commodity.ProductId);
			if (month != null)
			{
				dbContext.SettingsBidsheets.Add(new(commodity.Id, location.Id, year, deliveryStart.ToString("MMM yy"), month, deliveryStart, deliveryEnd, -0.35m));
			}
			var futureMonths = product.GetAllFutureMonths(year);

			foreach (var futureMonth in futureMonths)
			{
				dbContext.SettingsBidsheets.Add(new(commodity.Id, location.Id, year, deliveryStart.ToString("MMM yy"), futureMonth, deliveryStart, deliveryEnd, -0.35m));
			}
		}
		await dbContext.SaveChangesAsync();
	}

	public static async Task CreateBucketBalance(this AppDbContext dbContext, TenantData tenantData, Commodity commodity)
	{
		var year = TestData.CurrentYear;
		var bucketBalance = new BucketBalance(commodity.Id, tenantData.Regions.Default.Id, year);
		dbContext.BucketBalance.Add(bucketBalance);
		await dbContext.SaveChangesAsync();
	}
	
	public static async Task CreateBucketBalance(this AppDbContext dbContext, TenantData tenantData, Commodity commodity, short year, decimal balance)
	{
		var bucketBalance = new BucketBalance(balance, 0, 0, 0, commodity.Id, tenantData.Regions.Default.Id, year);
		dbContext.BucketBalance.Add(bucketBalance);
		await dbContext.SaveChangesAsync();
	}

	public static async Task CreateBrokerMappings(this AppDbContext dbContext, TenantData tenantData, Commodity commodity)
	{
		foreach (var item in tenantData.BrokerMappings.Where(a => a.CommodityId == tenantData.Commodities.Corn.Id))
		{
			var brokerMapping = new BrokerMapping(commodity.Id, item.ContractTypeId, item.HedgeAccountId, item.CropYear, item.RegionId);
			dbContext.BrokerMappings.Add(brokerMapping);
		}
		await dbContext.SaveChangesAsync();
	}

	public static async Task<MarketTransaction> CreateMarketTransaction(this AppDbContext dbContext, TenantData tenantData, Commodity commodity, Offer offer)
	{
		var product = tenantData.Products.GetById(commodity.ProductId);
		var brokerMapping = await dbContext.BrokerMappings.FirstOrDefaultAsync(x => x.CommodityId == offer.CommodityId
									&& x.CropYear == offer.RealCropYear && x.ContractTypeId == offer.ContractTypeId);
		var hedgeAccount = await dbContext.HedgeAccounts.FirstOrDefaultAsync(x => x.Id == brokerMapping.HedgeAccountId);
		var cqgInstrument = product.LimitOrderPrefix + product.Code;
		return offer.CreateLimitOrder(commodity, cqgInstrument, hedgeAccount.Account);
	}
	
	public static async Task<MarketTransaction> CreateMarketTransaction(this AppDbContext dbContext, TenantData tenantData, Contract contract)
	{
		var commodity = await dbContext.Commodities.FirstOrDefaultAsync(x => x.Id == contract.CommodityId);
		var product = await dbContext.Products.FirstOrDefaultAsync(x => x.Id == commodity.ProductId);
		var brokerMapping = await dbContext.BrokerMappings.FirstOrDefaultAsync(x => x.CommodityId == contract.CommodityId 
																					&& x.CropYear == contract.RealCropYear && x.ContractTypeId == contract.ContractTypeId);
		var hedgeAccount = await dbContext.HedgeAccounts.FirstOrDefaultAsync(x => x.Id == brokerMapping.HedgeAccountId);
		var cqgInstrument = product.MarketOrderPrefix + product.Code;

		return new(contract.InternalCode, contract.Quantity, commodity.GetLots(contract.Quantity), commodity.Id, commodity.Name, cqgInstrument,
			contract.FuturesMonth, contract.FuturesPrice ?? 0, contract.SimulatePrice(), contract.Id, null, !contract.IsSell, commodity.LotFactor, true, null,
			EMarketTransactionType.Market, EMarketTransactionSource.Contract, contract.RealCropYear, hedgeAccount.Account, false);
	}

	public static async Task AddErpPositionType(this AppDbContext dbContext, TenantData tenantData, Commodity commodity)
	{
		foreach (var item in tenantData.AgtraxPositionTypes)
		{
			var positionType = new AgtraxPositionType(item.TenantId, item.PositionType, item.ContractTypeId,
				commodity.Id, item.CropYear, item.BuySellId, item.IsPriced, item.IsFreight, item.LocationExtension);
			dbContext.AgtraxPositionTypes.Add(positionType);
		}
		await dbContext.SaveChangesAsync();
	}

	public static async Task AddAgvantageIdMapping(this AppDbContext dbContext, TenantData tenantData, Commodity commodity, Location location)
	{
		await dbContext.ErpIdMapping.AddAsync(
			new()
			{
				ErpName = ErpConstants.Agvantage,
				MappingType = ErpIdMappingType.Commodity,
				SourceId = commodity.Id,
				DestinationValue = "1"
			});
		await dbContext.ErpIdMapping.AddAsync(
			new()
			{
				ErpName = ErpConstants.Agvantage,
				MappingType = ErpIdMappingType.Location,
				SourceId = location.Id,
				DestinationValue = "ELV"
			});
		await dbContext.SaveChangesAsync();
	}

	public static async Task ClearCommodity(this AppDbContext dbContext, Commodity commodity)
	{
		var contracts = dbContext.Contracts.Where(a => a.CommodityId == commodity.Id);
		if (contracts.Any())
		{
			var bucketBalanceContracts = dbContext.BucketBalanceContract
											.Where(a => contracts.Any(b => b.Id == a.ContractId));
			if (bucketBalanceContracts.Any())
			{
				dbContext.BucketBalanceContract.RemoveRange(bucketBalanceContracts);
			}
			var orderFills = dbContext.OrderFills.Where(a => contracts.Any(b => b.Id == a.ContractId));
			if (orderFills.Any())
			{
				dbContext.OrderFills.RemoveRange(orderFills);
			}

			dbContext.Contracts.RemoveRange(contracts);
			await dbContext.SaveChangesAsync();
		}

		var bucketBalance = dbContext.BucketBalance.Where(a => a.CommodityId == commodity.Id);
		if (bucketBalance.Any())
		{
			dbContext.BucketBalance.RemoveRange(bucketBalance);
			await dbContext.SaveChangesAsync();
		}

		var marketTransactions = dbContext.MarketTransactions.Where(a => a.CommodityId == commodity.Id);
		if (marketTransactions.Any())
		{
			dbContext.MarketTransactions.RemoveRange(marketTransactions);
			await dbContext.SaveChangesAsync();
		}

		var bidsheets = dbContext.SettingsBidsheets.Where(a => a.CommodityId == commodity.Id);
		if (bidsheets.Any())
		{
			dbContext.SettingsBidsheets.RemoveRange(bidsheets);
			await dbContext.SaveChangesAsync();
		}

		var offers = dbContext.Offers.Where(a => a.CommodityId == commodity.Id);
		if (offers.Any())
		{
			dbContext.Offers.RemoveRange(offers);
			await dbContext.SaveChangesAsync();
		}

		var transactions = dbContext.Transactions.Where(a => a.CommodityId == commodity.Id);
		if (transactions.Any())
		{
			dbContext.Transactions.RemoveRange(transactions);
			await dbContext.SaveChangesAsync();
		}

		var dnhLog = dbContext.DoNotHedgeLog.Where(a => a.CommodityId == commodity.Id);
		if (dnhLog.Any())
		{
			dbContext.DoNotHedgeLog.RemoveRange(dnhLog);
			await dbContext.SaveChangesAsync();
		}

		var hedgeMapping = dbContext.HedgeMappings.Where(a => a.CommodityId == commodity.Id);
		if (hedgeMapping.Any())
		{
			dbContext.HedgeMappings.RemoveRange(hedgeMapping);
			await dbContext.SaveChangesAsync();
		}

		dbContext.Commodities.Remove(commodity);
		await dbContext.SaveChangesAsync();
	}
}
