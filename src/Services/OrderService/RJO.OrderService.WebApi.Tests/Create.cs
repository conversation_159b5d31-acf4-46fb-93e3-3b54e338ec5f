using RJO.IntegrationEvents.Commons.Events;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.Metadata;
using RJO.OrderService.Domain.Notifications;
using RJO.OrderService.Domain.Settings;
using System.Text;

namespace RJO.OrderService.WebApi.Tests;

static class Create
{
	public static string ErpNumber(int length = 0)
	{
		if (length <= 2)
			return Random.Shared.Next(100, 100000).ToString();

		var stringBuilder = new StringBuilder();
		while (stringBuilder.Length < length)
			stringBuilder.Append(Random.Shared.Next(0, 10)); // Appends a single digit (0–9)
		return stringBuilder.ToString();
	}

	public static Offer HtaOffer(TenantData tenantData, Commodity commodity, Location location, Location deliveryLocation, Customer customer, Employee employee,
		decimal futuresPrice, int quantity, bool isSell, Guid regionId, bool gtc = true, DateTime? expiration = null)
	{
		var cropYear = TestData.CurrentYear;
		var product = tenantData.Products.GetById(commodity.ProductId);
		var futuresMonth = product.RandomFuturesMonth(cropYear);

		var offer = new Offer(TestData.TransactionTypes.Offer.Id, TestData.ContractTypes.HTA.Id, isSell, false, commodity.Id, location.Id, deliveryLocation.Id,
			TestData.CurrentMonthPlus1Start, TestData.CurrentMonthPlus3Start, cropYear, customer.Id, employee.Id,
			futuresMonth, futuresPrice, null, null, null, 0.03m, 0.02m, 0.01m, 0, quantity, string.Empty, gtc, expiration, false, regionId);

		return offer;
	}

	public static Offer FlatPriceOffer(TenantData tenantData, Commodity commodity, Location location, Location deliveryLocation, Customer customer, Employee employee,
		decimal futuresPrice, int quantity, bool isSell, Guid regionId, bool gtc = true, DateTime? expiration = null)
	{
		var cropYear = TestData.CurrentYear;
		var product = tenantData.Products.GetById(commodity.ProductId);
		var futuresMonth = product.RandomFuturesMonth(cropYear);
		const decimal netBasis = 0.15m;
		const decimal freightPrice = 0.03m;
		const decimal fees1 = 0.02m;
		const decimal fees2 = 0.01m;

		var offer = new Offer(TestData.TransactionTypes.Offer.Id, TestData.ContractTypes.FlatPrice.Id, isSell, false, commodity.Id, location.Id, deliveryLocation.Id,
			TestData.CurrentMonthPlus1Start, TestData.CurrentMonthPlus3Start, cropYear, customer.Id, employee.Id,
			futuresMonth, 0, 0.05m, 0.1m, netBasis, freightPrice, fees1, fees2, futuresPrice + netBasis + freightPrice + fees1 + fees2, quantity, string.Empty, gtc, expiration, false, regionId);

		return offer;
	}

	public static Offer BasisOffer(TenantData tenantData, Commodity commodity, Location location, Location deliveryLocation, Customer customer, Employee employee,
		decimal futuresPrice, int quantity, bool isSell, Guid regionId, bool gtc = true, DateTime? expiration = null)
	{
		var cropYear = TestData.CurrentYear;
		var product = tenantData.Products.GetById(commodity.ProductId);
		var futuresMonth = product.RandomFuturesMonth(cropYear);

		var offer = new Offer(TestData.TransactionTypes.Offer.Id, TestData.ContractTypes.Basis.Id, isSell, false, commodity.Id, location.Id, deliveryLocation.Id,
			TestData.CurrentMonthPlus1Start, TestData.CurrentMonthPlus3Start, cropYear, customer.Id, employee.Id,
			futuresMonth, futuresPrice, 0.05m, 0.1m, 0.15m, 0.03m, 0.02m, 0.01m, 0, quantity, string.Empty, gtc, expiration, false, regionId);

		return offer;
	}

	public static Contract HtaContract(TenantData tenantData, Commodity commodity, Location location, Location deliveryLocation, Customer customer, Employee employee,
		decimal? futuresPrice, int quantity, bool isSell, Guid regionId, DateTime? expiration = null, bool passFill = true)
	{
		var cropYear = TestData.CurrentYear;
		var product = tenantData.Products.GetById(commodity.ProductId);
		var futuresMonth = product.SelectFuturesMonth(cropYear);

		var contract = new Contract(TestData.TransactionTypes.Cash.Id, TestData.ContractTypes.HTA.Id, isSell, commodity.Id, location.Id, deliveryLocation.Id, false,
			TestData.CurrentMonthPlus1Start, TestData.CurrentMonthPlus3Start, cropYear, customer.Id, employee.Id,
			futuresMonth, futuresPrice, 0.05m, 0.1m, 0.03m, 0.02m, 0.01m, quantity, string.Empty, passFill, false, false, expiration, ContractSource.User, regionId);
		return contract;
	}

	public static Contract PricedHtaContract(TenantData tenantData, Commodity commodity, Location location, Location deliveryLocation, Customer customer, Employee employee,
		decimal? futuresPrice, int quantity, bool isSell, Guid regionId, DateTime? expiration = null, bool passFill = true)
	{
		var contract = HtaContract(tenantData, commodity, location, deliveryLocation, customer, employee, futuresPrice, quantity, isSell, regionId, expiration, passFill);
		contract.ChangePrice(contract.FuturesPrice.Value + (contract.NetBasis ?? 0) + contract.FreightPrice + contract.Fees1 + contract.Fees2);
		contract.Priced();
		contract.ChangeRemainingBalance(15000, false);
		return contract;
	}

	public static Contract PricedBasisContract(TenantData tenantData, Contract basisContrat, decimal futuresPrice, int quantity)
	{
		var contract = HtaContract(tenantData, basisContrat.Commodity, basisContrat.Location, basisContrat.DeliveryLocation, basisContrat.Customer, basisContrat.Employee, futuresPrice, quantity, basisContrat.IsSell, basisContrat.RegionId.Value);
		contract.ChangePrice(contract.FuturesPrice.Value + (contract.NetBasis ?? 0) + contract.FreightPrice + contract.Fees1 + contract.Fees2);
		contract.Priced();
		contract.ChangeRemainingBalance(0, false);
		return contract;
	}

	public static Contract SplittedContract(Contract parent, int quantity, Customer newCustomer)
	{
		var child = parent.CreateChild(quantity, EContractEvent.ApplyNameId);
		parent.ChangeRemainingBalance(quantity, false);
		child.ChangeCustomer(newCustomer.Id);
		return child;
	}

	public static Contract FlatPriceContract(TenantData tenantData, Commodity commodity, Location location, Location deliveryLocation, Customer customer, Employee employee,
		decimal? futuresPrice, int quantity, bool isSell, Guid regionId, DateTime? expiration = null)
	{
		var cropYear = TestData.CurrentYear;
		var product = tenantData.Products.GetById(commodity.ProductId);
		var futuresMonth = product.RandomFuturesMonth(cropYear);

		var contract = new Contract(TestData.TransactionTypes.Cash.Id, TestData.ContractTypes.FlatPrice.Id, isSell, commodity.Id, location.Id, deliveryLocation.Id, false,
			TestData.CurrentMonthPlus1Start, TestData.CurrentMonthPlus3Start, cropYear, customer.Id, employee.Id,
			futuresMonth, futuresPrice, 0.05m, 0.1m, 0.03m, 0.02m, 0.01m, quantity, string.Empty, true, false, false, expiration, ContractSource.User, regionId);

		return contract;
	}

	public static Contract BasisContract(TenantData tenantData, Commodity commodity, Location location, Location deliveryLocation, Customer customer, Employee employee,
		decimal? futuresPrice, int quantity, bool isSell, Guid regionId, DateTime? expiration = null)
	{
		var cropYear = TestData.CurrentYear;
		var product = tenantData.Products.GetById(commodity.ProductId);
		var futuresMonth = product.RandomFuturesMonth(cropYear);

		var contract = new Contract(TestData.TransactionTypes.Cash.Id, TestData.ContractTypes.Basis.Id, isSell, commodity.Id, location.Id, deliveryLocation.Id, false,
			TestData.CurrentMonthPlus1Start, TestData.CurrentMonthPlus3Start, cropYear, customer.Id, employee.Id,
			futuresMonth, futuresPrice, 0.05m, 0.1m, 0.03m, 0.02m, 0.01m, quantity, string.Empty, false, false, false, expiration, ContractSource.User, regionId);

		return contract;
	}

	public static Contract NtcContract(TenantData tenantData, Commodity commodity, Location location, Location deliveryLocation, Customer customer, Employee employee,
		decimal? futuresPrice, decimal? postBasis, int quantity, bool isSell, Guid regionId, DateTime? expiration = null)
	{
		var cropYear = TestData.CurrentYear;
		var product = tenantData.Products.GetById(commodity.ProductId);
		var futuresMonth = product.RandomFuturesMonth(cropYear);

		var contract = new Contract(TestData.TransactionTypes.BushelsOnly.Id, TestData.ContractTypes.NTC.Id, isSell, commodity.Id, location.Id, deliveryLocation.Id, false,
			TestData.CurrentMonthPlus1Start, TestData.CurrentMonthPlus3Start, cropYear, customer.Id, employee.Id,
			futuresMonth, futuresPrice, postBasis, 0.1m, 0.03m, 0.02m, 0.01m, quantity, string.Empty, false, false, false, expiration, ContractSource.User, regionId);

		return contract;
	}

	public static Commodity Commodity(string name, decimal priceCtrl, decimal basisCtrl, Guid productId, int lotFactor = 5000) =>
		new(name, TestData.RandomString(2, 4), priceCtrl, basisCtrl, productId, 1, 1, TestData.CurrentYear, 28, 2, TestData.CurrentYearPlus3, 4);

	public static Bidsheet Bidsheet(Guid commodityId, Guid deliveryLocationId, decimal basis, Product product)
	{
		var cropYear = TestData.CurrentYear;
		var futuresMonth = product.RandomFuturesMonth(cropYear);
		var deliveryStart = TestData.CurrentMonthPlus1Start;
		var deliveryEnd = TestData.CurrentMonthPlus3Start;
		return new(commodityId, deliveryLocationId, cropYear, deliveryStart.ToString("MMM yy"), futuresMonth, deliveryStart, deliveryEnd, basis);
	}
	
	public static Bidsheet Bidsheet(Guid commodityId, Guid deliveryLocationId, decimal basis, Product product, short cropYear)
	{
		var futuresMonth = product.RandomFuturesMonth(cropYear);
		var deliveryStart = TestData.CurrentMonthPlus1Start;
		var deliveryEnd = TestData.CurrentMonthPlus3Start;
		return new(commodityId, deliveryLocationId, cropYear, deliveryStart.ToString("MMM yy"), futuresMonth, deliveryStart, deliveryEnd, basis);
	}

	public static MarketTransaction MarketTransaction(TenantData tenantData, Contract contract)
	{
		var commodity = tenantData.Commodities.GetById(contract.CommodityId);
		var product = tenantData.Products.GetById(commodity.ProductId);
		var brokerMapping = tenantData.BrokerMappings.Get(contract.CommodityId, contract.RealCropYear, contract.ContractTypeId);
		var hedgeAccount = tenantData.HedgeAccounts.GetById(brokerMapping.HedgeAccountId);
		var cqgInstrument = product.MarketOrderPrefix + product.Code;

		return new(contract.InternalCode, contract.Quantity, commodity.GetLots(contract.Quantity), commodity.Id, commodity.Name, cqgInstrument,
			contract.FuturesMonth, contract.FuturesPrice ?? 0, contract.SimulatePrice(), contract.Id, null, !contract.IsSell, commodity.LotFactor, true, null,
			EMarketTransactionType.Market, EMarketTransactionSource.Contract, contract.RealCropYear, hedgeAccount.Account, false);
	}

	public static MarketTransaction MarketTransaction(TenantData tenantData, Offer offer)
	{
		var commodity = tenantData.Commodities.GetById(offer.CommodityId);
		var product = tenantData.Products.GetById(commodity.ProductId);
		var brokerMapping = tenantData.BrokerMappings.Get(offer.CommodityId, offer.RealCropYear, offer.ContractTypeId);
		var hedgeAccount = tenantData.HedgeAccounts.GetById(brokerMapping.HedgeAccountId);
		var cqgInstrument = product.LimitOrderPrefix + product.Code;

		return offer.CreateLimitOrder(commodity, cqgInstrument, hedgeAccount.Account);
	}

	public static OrderFill OrderFillWithErpNumber(MarketTransaction transaction, string transitionId, string erpNumber)
	{
		var orderFill = OrderFill.Create(transaction, transitionId, transaction.FuturesPrice, transaction.Lots);
		orderFill.UpdateErpStatus(ErpStatus.Success, "", erpNumber);
		return orderFill;
	}

	public static TenantNotificationSetting TenantSetting(EventType eventType, bool isInAppEnabled, bool isEmailEnabled, bool isSmsEnabled, bool isDailySummaryEnabled)
		=> new(eventType, isInAppEnabled, isEmailEnabled, isSmsEnabled, isDailySummaryEnabled);

	public static UserNotificationSubscription UserSubscription(EventType eventType, bool isInAppEnabled, bool isEmailEnabled, bool isSmsEnabled, bool isDailySummaryEnabled, Guid? employeeId = null, Guid? customerId = null)
		=> new(eventType, isInAppEnabled, isEmailEnabled, isSmsEnabled, isDailySummaryEnabled, employeeId, customerId);

	public static OrderStatusFill OrderStatusFill(Commodity commodity, decimal price, int amount) => OrderStatusFill(commodity, (double)price, amount);

	public static OrderStatusFill OrderStatusFill(Commodity commodity, decimal price, decimal amount) => OrderStatusFill(commodity, (double)price, (double)amount);

	public static OrderStatusFill OrderStatusFill(Commodity commodity, double price, double amount)
	{
		commodity.AmountShouldBeExactMultipleOfLotFactor(amount, nameof(amount));

		var lots = amount / commodity.LotFactor;

		return new()
		{
			TransactionId = Guid.NewGuid().ToString(),
			Price = price,
			Amount = lots,
			UtcTimestamp = DateTime.UtcNow
		};
	}

	public static WorkingOrderEvent WorkingOrderEvent(
		TenantData tenantData, Commodity commodity, string marketOrderId, string clientOrderId, params OrderStatusFill[] orderStatusFills) =>
		OrderStatusEvent<WorkingOrderEvent>(tenantData, commodity, marketOrderId, clientOrderId, false, null, "", "", orderStatusFills);

	public static WorkingOrderEvent RejectedEditEvent(TenantData tenantData, Commodity commodity, string marketOrderId, string clientOrderId, string rejectReason, string accountId, params OrderStatusFill[] orderStatusFills)
		=> OrderStatusEvent<WorkingOrderEvent>(tenantData, commodity, marketOrderId, clientOrderId, true, rejectReason, accountId, "", orderStatusFills);

	public static RejectedOrderEvent RejectedEditEvent(TenantData tenantData, Commodity commodity, string marketOrderId, string clientOrderId, string rejectReason)
		=> OrderStatusEvent<RejectedOrderEvent>(tenantData, commodity, marketOrderId, clientOrderId, true, rejectReason, "", "");

	public static FilledOrderEvent FilledOrderEvent(
		TenantData tenantData, Commodity commodity, string marketOrderId, string clientOrderId, string status, params OrderStatusFill[] orderStatusFills) =>
		OrderStatusEvent<FilledOrderEvent>(tenantData, commodity, marketOrderId, clientOrderId, false, null, "", status, orderStatusFills);

	public static RejectedOrderEvent RejectedOrderEvent(
		TenantData tenantData, Commodity commodity, string marketOrderId, string clientOrderId, string rejectReason) =>
		OrderStatusEvent<RejectedOrderEvent>(tenantData, commodity, marketOrderId, clientOrderId, true, rejectReason, "", "");

	static T OrderStatusEvent<T>(TenantData tenantData, Commodity commodity, string marketOrderId, string clientOrderId, bool isRejected, string rejectReason, string accountId, string status, params OrderStatusFill[] orderStatusFills)
		where T : OrderStatusEvent, new()
	{
		var lastFillPrice = !isRejected && orderStatusFills.Length != 0 ? orderStatusFills.Last().Price : 0d;

		var filledAmount = orderStatusFills.Sum(x => x.Amount);
		var averageFillPrice = !isRejected ? filledAmount == 0d ? 0d : orderStatusFills.Sum(x => x.Price * x.Amount) / filledAmount : 0d;
		var productCode = tenantData.Products.GetById(commodity.ProductId).Code;
		var priceScale = ProductCodes.PriceScaleFor(productCode);
		var conversionFactor = ProductCodes.ConversionFactorFor(productCode);

		return new()
		{
			AccountId = accountId,
			TenantId = tenantData.Id,
			ClOrderId = clientOrderId.Remove(clientOrderId.LastIndexOf("_", StringComparison.OrdinalIgnoreCase)),
			MarketOrderId = marketOrderId,
			MarketClientOrderId = clientOrderId,
			FillPriceCorrect = lastFillPrice,
			MarketAvgFillPriceCorrect = averageFillPrice,
			MarketScaledAvgFillPrice = (long)(averageFillPrice / priceScale / conversionFactor),
			FillQty = (int)filledAmount,
			FillCnt = (uint)orderStatusFills.Length,
			Fills = orderStatusFills,
			MarketIsRejected = isRejected,
			MarketRejectedReason = rejectReason,
			Status = string.IsNullOrEmpty(status) ? (isRejected ? "Rejected" : "Working") : status
			// TODO: the rest
		};
	}

	public static TimerConfiguration TimerConfigurationForOaklandCustomers(string name, string cron)
		=> new(name, cron);

	public static OrderMetadataConfiguration OrderMetadataConfiguration(EOrderMetadataFieldType type, string label, short order, bool isActive, string erpField = "", string defaultValue = "")
		=> new(type, label, order, isActive, erpField, defaultValue);

	public static ContractMetadata ContractMetadata(Guid contractId, string value, Guid fieldId)
		=> new(contractId, value, fieldId);

	public static OfferMetadata OfferMetadata(Guid offerId, string value, Guid fieldId)
		=> new(offerId, value, fieldId);
}
