using RJO.BuildingBlocks.Testing;
using RJO.OrderService.Domain.ERP;

namespace RJO.OrderService.WebApi.Tests.DatabaseInitialization;

class SpotToGroupedLocationMaps(GroupedLocations groupedLocations) : TestEntities<SpotToGroupedLocationMap>
{
	public SpotToGroupedLocationMap First { get; set; } = new("123", groupedLocations.PairFour.Id);
	public SpotToGroupedLocationMap InactiveGroupLocation { get; set; } = new("126", groupedLocations.PairInactive.Id);

	protected override IReadOnlyList<SpotToGroupedLocationMap> AllItems =>
	[
		First, InactiveGroupLocation
	];
}
