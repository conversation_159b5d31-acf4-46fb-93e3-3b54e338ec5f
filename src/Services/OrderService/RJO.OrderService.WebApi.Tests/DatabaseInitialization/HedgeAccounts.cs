using RJO.BuildingBlocks.Testing;
using RJO.OrderService.Domain;

namespace RJO.OrderService.WebApi.Tests.DatabaseInitialization;

class HedgeAccounts : TestEntities<HedgeAccount>
{
	public HedgeAccount One { get; init; }
	public HedgeAccount Two { get; init;  }

	public HedgeAccounts(Customers customers, Locations locations)
	{
		One = new("HedgeAct001", 1888001, customers.BradySmith.Number, locations.Oakbrook.Number);
		Two = new("HedgeAct002", 1888002, customers.RichFranz.Number, locations.Lisle.Number);
	}

	protected override IReadOnlyList<HedgeAccount> AllItems => new[]
	{
		One,
		Two
	};
}
