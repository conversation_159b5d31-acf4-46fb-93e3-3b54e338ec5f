using RJO.BuildingBlocks.Testing;
using RJO.OrderService.Domain.Settings;

namespace RJO.OrderService.WebApi.Tests.DatabaseInitialization;

class GroupedLocations : TestEntities<GroupedLocation>
{
	public GroupedLocations(Locations locations, Regions regions)
	{
		PairOne = new(locations.Oakbrook.Id, locations.Lisle.Id, null);
		PairTwo = new(locations.Lisle.Id, locations.Oakbrook.Id, null);
		PairThree = new(locations.LakeFalls.Id, locations.Oakbrook.Id, regions.Default.Id);
		PairFour = new(locations.Deerfield.Id, locations.Oakbrook.Id, regions.Default.Id);
		PairFive = new(locations.Deerfield.Id, locations.Lisle.Id, regions.Default.Id);
		PairInactive = new(locations.Lisle.Id, locations.LakeFalls.Id, false, regions.Default.Id);
	}

	public GroupedLocation PairOne { get; }
	public GroupedLocation PairTwo { get; }
	public GroupedLocation PairThree { get; }
	public GroupedLocation PairFour { get; }
	public GroupedLocation PairFive { get; }
	public GroupedLocation PairInactive { get; }

	protected override IReadOnlyList<GroupedLocation> AllItems =>new[]
	{
		PairOne, PairTwo, PairThree, PairFour, PairFive, PairInactive
	};
}
