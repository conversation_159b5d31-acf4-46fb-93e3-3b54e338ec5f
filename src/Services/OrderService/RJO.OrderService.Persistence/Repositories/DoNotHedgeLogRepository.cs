using RJO.OrderService.Domain;
using RJO.OrderService.Persistence.Database;

namespace RJO.OrderService.Persistence.Repositories;

public class DoNotHedgeLogRepository : Repository<DoNotHedgeLog>
{
	public DoNotHedgeLogRepository(AppDbContext context) : base(context)
	{
	}

	public async Task<DoNotHedgeLog> GetActiveLog(Guid commodityId, short cropYear, Guid? regionId)
	{
		IEnumerable<DoNotHedgeLog> query = await GetAllEntities();
		query = query
			.OrderByDescending(y => y.UpdatedOn ?? y.CreatedOn)
			.Where(x => x.CommodityId == commodityId && x.CropYear == cropYear && (regionId != null ? x.RegionId == regionId : true));
		return query.FirstOrDefault();
	}

	public async Task<bool> IsActiveLog(Guid commodityId, short cropYear, Guid? regionId)
	{
		IEnumerable<DoNotHedgeLog> query = await GetAllEntities();
		query = query
			.OrderByDescending(y => y.UpdatedOn ?? y.CreatedOn)
			.Where(x => x.CommodityId == commodityId && x.CropYear == cropYear && (regionId != null ? x.RegionId == regionId : true));
		return query.FirstOrDefault()?.IsActive ?? false;
	}

	public async Task Activate(Guid commodityId, short cropYear, Guid contractId, decimal quantity, bool isSell, Guid? regionId) => await InsertAsync(new(true, commodityId, cropYear, contractId, quantity, isSell, regionId));

	public async Task Activate(Guid commodityId, short cropYear, Guid? regionId) => await InsertAsync(new(true, commodityId, cropYear, Guid.Empty, 0, true, regionId));

	public async Task Inactivate(Guid commodityId, short cropYear, Guid contractId, decimal quantity, bool isSell, Guid? regionId) => await InsertAsync(new(false, commodityId, cropYear, contractId, quantity, isSell, regionId));

	public async Task Inactivate(Guid commodityId, short cropYear, Guid? regionId) => await InsertAsync(new(false, commodityId, cropYear, Guid.Empty, 0, true, regionId));
}
