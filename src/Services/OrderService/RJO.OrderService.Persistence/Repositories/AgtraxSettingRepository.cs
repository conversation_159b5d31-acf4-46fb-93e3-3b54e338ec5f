using RJO.OrderService.Domain.ERP;
using RJO.OrderService.Persistence.Database;

namespace RJO.OrderService.Persistence.Repositories;

public class AgtraxSettingRepository : Repository<AgtraxSetting>
{
	public AgtraxSettingRepository(AppDbContext context) : base(context)
	{
	}

	public async Task<AgtraxSetting> CreateOrUpdateSetting(bool isActive, string systemUrl, string user, string userPassword, string provider, string providerPassword, string defaultControl, int commentsLength)
	{
		var setting = await GetSingleOrDefault();

		if (setting is null)
		{
			setting = new(
				isActive,
				systemUrl,
				user,
				userPassword,
				string.Empty,
				string.Empty,
				string.Empty,
				commentsLength
			);

			await InsertAsync(setting);
		}
		else
		{
			setting.Update(
				isActive,
				systemUrl,
				user,
				userPassword,
				setting.Provider,
				setting.ProviderPassword,
				setting.DefaultControl,
				commentsLength
			);
		}

		return setting;
	}
}
