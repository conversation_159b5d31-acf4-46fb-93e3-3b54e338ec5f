using RJO.OrderService.Domain.ERP;
using RJO.OrderService.Persistence.Database;

namespace RJO.OrderService.Persistence.Repositories;

public class OaklandSettingRepository : Repository<OaklandSetting>
{
	public OaklandSettingRepository(AppDbContext context) : base(context)
	{
	}

	public async Task<OaklandSetting> CreateOrUpdateSetting(bool isActive)
	{
		var setting = await GetSingleOrDefault(x => x.Name == "Erp");

		// more to be implemented with Frontend settings
		if (setting is null)
			throw new NotSupportedException("Please contact Hrvyst to set up connection.");
		else
		{
			setting.Update(isActive);
		}

		return setting;
	}
}
