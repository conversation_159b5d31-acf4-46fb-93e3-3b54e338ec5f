using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Contracts;
using RJO.OrderService.Persistence.Database;

namespace RJO.OrderService.Persistence;

static class MediatorExtension
{
	public static IList<EntityEntry<AggregateRoot>> GetEntities<TContext>(this TContext ctx) where TContext : DbContext, IDisposable
	{
		var domainEntities = ctx.ChangeTracker
			.Entries<AggregateRoot>()
			.Where(x => x.Entity.DomainEvents != null && x.Entity.DomainEvents.Any());

		return domainEntities.ToList();
	}

	public static async Task<bool> DispatchDomainEventsPreAsync(this IMediator mediator, IList<EntityEntry<AggregateRoot>> domainEntities)
	{
		var domainEvents = domainEntities.SelectMany(x => x.Entity.DomainEvents).Where(x => x is IEventPre).ToList();
		var result = false;
		foreach (var domainEvent in domainEvents.OfType<INotification>())
		{
			await mediator.Publish(domainEvent);
			result = true;
		}

		return result;
	}

	public static async Task<bool> DispatchDomainEventsPosAsync(this IMediator mediator, IList<EntityEntry<AggregateRoot>> domainEntities)
	{
		var domainEvents = domainEntities.SelectMany(x => x.Entity.DomainEvents).Where(x => x is IEventPos).ToList();
		var result = false;
		foreach (var domainEvent in domainEvents.OfType<INotification>())
		{
			await mediator.Publish(domainEvent);
			result = true;
		}

		return result;
	}

	public static async Task<bool> DispatchDomainEventsPosWithBackgroundTaskQueueAsync(this IMediator mediator, IEnumerable<EntityEntry<AggregateRoot>> domainEntities, BackgroundTaskChannel taskChannel, AppDbContext context)
	{
		var domainEvents = domainEntities.SelectMany(x => x.Entity.DomainEvents).Where(x => x is IBackgroundTask).ToList();

		if (domainEvents.Count == 0)
		{
			return false;
		}
		
		foreach (var domainEvent in domainEvents.OfType<IBackgroundTask>())
		{
			domainEvent.ApplicationTenant = context.TenancyContext.Tenant;
			await taskChannel.EnqueueAsync(domainEvent);
		}

		return true;
	}
	
	public static void ClearEvents(this IList<EntityEntry<AggregateRoot>> domainEntities) => domainEntities.ToList().ForEach(x => x.Entity.ClearDomainEvents());
}
