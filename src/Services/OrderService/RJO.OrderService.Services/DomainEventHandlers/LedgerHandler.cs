using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.Resources;
using RJO.OrderService.Common;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Events;
using RJO.OrderService.Persistence;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Services.Helper;
using System.Globalization;

namespace RJO.OrderService.Services.DomainEventHandlers;

public class LedgerHandler : INotificationHandler<BalanceChanged>, INotificationHandler<MarketTransactionOnLiveLedgerNotification>, INotificationHandler<ContractLedgerNotification>,
	INotificationHandler<ContractLedgerTransactionNotification>
{
	readonly UnitOfWork _unitOfWork;
	readonly ContractRepository _contractRepository;
	readonly ContractTypeRepository _contractTypeRepository;
	readonly CustomerRepository _customerRepository;
	readonly EmployeeRepository _employeeRepository;
	readonly LocationRepository _locationRepository;
	readonly BucketBalanceRepository _bucketBalanceRepository;
	readonly LiveLedgerRepository _liveLedgerRepository;
	readonly BalanceSnapshotRepository _balanceSnapshotRepository;
	readonly DoNotHedgeLogRepository _doNotHedgeLogRepository;
	readonly CommodityRepository _commodityRepository;
	readonly AppDbContext _dbContext;

	public LedgerHandler(UnitOfWork unitOfWork, ContractRepository contractRepository, ContractTypeRepository contractTypeRepository, CustomerRepository customerRepository,
		LocationRepository locationRepository, BucketBalanceRepository bucketBalanceRepository, LiveLedgerRepository liveLedgerRepository,
		BalanceSnapshotRepository balanceSnapshotRepository, EmployeeRepository employeeRepository, DoNotHedgeLogRepository doNotHedgeLogRepository,
		CommodityRepository commodityRepository, AppDbContext dbContext)
	{
		_unitOfWork = unitOfWork;
		_contractRepository = contractRepository;
		_contractTypeRepository = contractTypeRepository;
		_customerRepository = customerRepository;
		_locationRepository = locationRepository;
		_bucketBalanceRepository = bucketBalanceRepository;
		_liveLedgerRepository = liveLedgerRepository;
		_balanceSnapshotRepository = balanceSnapshotRepository;
		_employeeRepository = employeeRepository;
		_doNotHedgeLogRepository = doNotHedgeLogRepository;
		_commodityRepository = commodityRepository;
		_dbContext = dbContext;
	}

	public async Task Handle(BalanceChanged notification, CancellationToken cancellationToken)
	{
		var liveLedger = await GenerateTransaction(notification);
		_dbContext.Transactions.Add(liveLedger);
		await _dbContext.SaveChangesAsync(cancellationToken);
	}

	public async Task Handle(MarketTransactionOnLiveLedgerNotification notification, CancellationToken cancellationToken)
	{
		var liveLedger = await GenerateTransaction(notification);
		_dbContext.Transactions.Add(liveLedger);
		await _dbContext.SaveChangesAsync(cancellationToken);
	}

	public async Task Handle(ContractLedgerNotification notification, CancellationToken cancellationToken)
	{
		var liveLedger = await GenerateTransaction(notification);
		await _dbContext.Transactions.AddAsync(liveLedger, cancellationToken);
		await _dbContext.SaveChangesAsync(cancellationToken);

	}

	public async Task Handle(ContractLedgerTransactionNotification notification, CancellationToken cancellationToken)
	{
		string[] contractTransactionTypes = { "Basis", "HTA", "Flat Price", "NTC" };
		AssertionConcern.ArgumentIsNotNull(notification.ContractId, GeneralResources.RequiredValueIsNotPresent);
		var contract = await _contractRepository.GetByIdLocallyFirst(notification.ContractId);

		if (notification.ContractNumber != contract.Number)
		{
			var transaction = (await _liveLedgerRepository.GetAllEntities())
				.Where(x => x.ContractId == contract.Id && x.ContractNumber == notification.ContractNumber && contractTransactionTypes.Contains(x.TransactionType))
				.FirstOrDefault();
			if (transaction != null)
			{
				transaction.ChangeContractNumber(contract.Number);
				_liveLedgerRepository.Update(transaction);
			}
		}
	}

	public async Task<Transaction> GenerateTransaction(BalanceChanged notification)
	{
		var contract = await _dbContext.Contracts
			.AsNoTracking()
			.Include(a => a.Commodity)
			.Include(a => a.ContractType)
			.Include(a => a.Customer)
			.Include(a => a.Employee)
			.Include(a => a.DeliveryLocation)
			.FirstOrDefaultAsync(a => a.Id == notification.ContractId);
		if (contract != null)
		{
			var helper = new BalanceSnapshotHelper(_bucketBalanceRepository, _balanceSnapshotRepository, _unitOfWork);

			await helper.TakeSnapshotIfThereIsNot(contract.CommodityId, notification.RegionId, notification.CropYear);
			var deliveryMonth = GetDeliveryMonth(notification.DeliveryStartDate);

			var balances = await GetBalances(notification.CropYear, contract.Commodity, notification.RegionId, notification.BalanceValue);

			return new(contract.Employee.Id, contract.Employee.Name(), contract.Id, contract.Number, contract.InternalCode, contract.CustomerId,
				contract.Customer.FullName(), contract.Customer.Number, contract.Customer.Address(), contract.Customer.PhoneNumber, contract.Customer.WorkPhoneNumber, contract.Customer.Email,
				contract.ContractTypeId, contract.ContractType?.Name, contract.DeliveryLocationId, contract.DeliveryLocation?.Name, deliveryMonth, notification.CropYear,
				notification.IsSell, notification.FuturesMonth, notification.Quantity, contract.Status.ToString(), notification.EventName, contract.CommodityId,
				balances[0], balances[1], balances[2], balances[3], balances[4],
				notification.InDNH, notification.IsDNHBegin, notification.IsDNHEnd, notification.IsAffectingBalance, notification.CreatedOn, notification.UpdatedOn, false, null, balances[5], balances[6], notification.RegionId);
		}
		else
		{
			contract = await _contractRepository.GetById(notification.ContractId);
			var commodity = await _commodityRepository.GetById(contract.CommodityId);
			AssertionConcern.ArgumentIsNotNull(contract, ContractResources.ContractTypeValuesAreWrong);
			var helper = new BalanceSnapshotHelper(_bucketBalanceRepository, _balanceSnapshotRepository, _unitOfWork);

			await helper.TakeSnapshotIfThereIsNot(contract.CommodityId, notification.RegionId, notification.CropYear);
			var deliveryMonth = GetDeliveryMonth(notification.DeliveryStartDate);

			//Obtaining information from Contract Type
			var contractTypeName = (await _contractTypeRepository.GetAllEntities()).Where(x => x.Id == contract.ContractTypeId).Select(x => x.Name).FirstOrDefault();
			AssertionConcern.ArgumentIsNotNull(contractTypeName, ContractResources.ContractTypeValuesAreWrong);

			//Obtaining information from Customer
			var customer = await _customerRepository.GetSingleOrDefault(x => x.Id == contract.CustomerId);
			AssertionConcern.ArgumentIsNotNull(customer, ContractResources.ContractTypeValuesAreWrong);

			//Obtaining information from Employee
			var employee = await _employeeRepository.GetSingleOrDefault(x => x.Id == contract.EmployeeId);
			AssertionConcern.ArgumentIsNotNull(employee, ContractResources.ContractTypeValuesAreWrong);

			//Obtaining information from Location
			var locationName = (await _locationRepository.GetAllEntities()).Where(x => x.Id == contract.DeliveryLocationId).Select(x => x.Name).FirstOrDefault();
			AssertionConcern.ArgumentIsNotNull(locationName, ContractResources.ContractTypeValuesAreWrong);

			var balances = await GetBalances(notification.CropYear, commodity, notification.RegionId, notification.BalanceValue);

			return new(employee.Id, employee.Name(), contract.Id, contract.Number, contract.InternalCode, contract.CustomerId,
				customer.FullName(), customer.Number, customer.Address(), customer.PhoneNumber, customer.WorkPhoneNumber, customer.Email,
				contract.ContractTypeId, contractTypeName, contract.DeliveryLocationId, locationName, deliveryMonth, notification.CropYear,
				notification.IsSell, notification.FuturesMonth, notification.Quantity, contract.Status.ToString(), notification.EventName, contract.CommodityId,
				balances[0], balances[1], balances[2], balances[3], balances[4],
				notification.InDNH, notification.IsDNHBegin, notification.IsDNHEnd, notification.IsAffectingBalance, notification.CreatedOn, notification.UpdatedOn, false, null, balances[5], balances[6], notification.RegionId);
		}
	}

	async Task<Transaction> GenerateTransaction(MarketTransactionOnLiveLedgerNotification notification)
	{
		var commodity = await _commodityRepository.GetById(notification.CommodityId);
		var balances = await GetBalances(notification.CropYear, commodity, notification.RegionId, notification.BalanceValue);
		
		return new(notification.CropYear, notification.IsSell, notification.FuturesMonth,
			notification.Quantity, notification.TransactionStatus, notification.EventName, notification.CommodityId,
			balances[0], balances[1], balances[2], balances[3], balances[4],
			notification.TransactionType, notification.CreatedOn, notification.UpdatedOn,
			notification.IsReverseTransaction, notification.Description, notification.ContractNumber, notification.InternalCode, notification.MarketTransactionId, balances[5], balances[6], notification.RegionId);
	}

	async Task<Transaction> GenerateTransaction(ContractLedgerNotification notification)
	{
		var contract = await _contractRepository.GetById(notification.ContractId);
		var commodity = await _commodityRepository.GetById(contract.CommodityId);

		AssertionConcern.ArgumentIsNotNull(contract, ContractResources.ContractTypeValuesAreWrong);
		var helper = new BalanceSnapshotHelper(_bucketBalanceRepository, _balanceSnapshotRepository, _unitOfWork);
		await helper.TakeSnapshotIfThereIsNot(contract.CommodityId, contract.RegionId, contract.RealCropYear);
		var deliveryMonth = GetDeliveryMonth(notification.DeliveryStartDate);

		//Obtaining information from Contract Type
		var contractTypeName = (await _contractTypeRepository.GetAllEntities()).Where(x => x.Id == contract.ContractTypeId).Select(x => x.Name).FirstOrDefault();
		AssertionConcern.ArgumentIsNotNull(contractTypeName, ContractResources.ContractTypeValuesAreWrong);

		//Obtaining information from Customer
		var customer = await _customerRepository.GetSingleOrDefault(x => x.Id == contract.CustomerId);
		if (TransactionTypeDictionary.Adjustment != contract.TransactionTypeId)
		{
			AssertionConcern.ArgumentIsNotNull(customer, ContractResources.ContractTypeValuesAreWrong);
		}

		//Obtaining information from Employee
		var employee = await _employeeRepository.GetSingleOrDefault(x => x.Id == contract.EmployeeId);
		AssertionConcern.ArgumentIsNotNull(employee, ContractResources.ContractTypeValuesAreWrong);

		//Obtaining information from Location
		var locationName = (await _locationRepository.GetAllEntities()).Where(x => x.Id == contract.DeliveryLocationId).Select(x => x.Name).FirstOrDefault();
		AssertionConcern.ArgumentIsNotNull(locationName, ContractResources.ContractTypeValuesAreWrong);

		var balances = await GetBalances(commodity, notification.CropYear, notification.RegionId);

		var inDnh = await _doNotHedgeLogRepository.IsActiveLog(contract.CommodityId, contract.RealCropYear, notification.RegionId);
		return new(employee.Id, employee.Name(), notification.ContractId, notification.ContractNumber ?? contract.Number, contract.InternalCode, contract.CustomerId,
			customer.FullName(), customer.Number, customer.Address(), customer.PhoneNumber, customer.WorkPhoneNumber, customer.Email,
			contract.ContractTypeId, contractTypeName, contract.DeliveryLocationId, locationName, deliveryMonth, notification.CropYear,
			notification.IsSell, string.IsNullOrEmpty(notification.FuturesMonth) ? contract.FuturesMonth : notification.FuturesMonth, notification.Quantity ?? contract.Quantity, contract.Status.ToString(), notification.EventName,
			contract.CommodityId, balances[0], balances[1], balances[2], balances[3], balances[4],
			inDnh, false, false, notification.IsAffectingBalance, notification.CreatedOn, notification.UpdatedOn, false, null, balances[5], balances[6], notification.RegionId);
	}

	#region Helpers

	static string GetDeliveryMonth(DateTime dateTime) => dateTime.ToString("MMM y", DateTimeFormatInfo.InvariantInfo).ToUpper(CultureInfo.InvariantCulture).Trim();

	async Task<decimal[]> GetBalances(short cropYear, Commodity commodity, Guid? regionId, decimal balanceValue)
	{
		ContractAssertionConcern.CropYearIsValid(cropYear);
		var data = await GetBalances(commodity, cropYear, regionId);
		var cropYearIndex = cropYear - commodity.CropStartYear;
		if (cropYearIndex < 0) { return data; }

		data[cropYearIndex] = balanceValue;
		return data;
	}

	async Task<decimal[]> GetBalances(Commodity commodity, short cropYear, Guid? regionId)
	{
		var years = commodity.AvailableCropYears();
		var data = new decimal[7];
		var query = await _bucketBalanceRepository.GetBalanceByCommodity(commodity.Id, years, regionId);
		var lst = query.Select(x => new
		{
			x.CropYear,
			Balance = x.Balance(),
			RejectedBalance = x.RejectedBalance(),
			OrphanedBuyBalance = x.OrphanedBuyBalanceValue.Value,
			OrphanedSellBalance = x.OrphanedSellBalanceValue.Value
		}).ToList();
		for (var i = 0; i < data.Length; i++)
		{
			decimal total = 0;
			if (years.Count <= i)
			{
				total = 0;
			}
			else
			{
				total = lst.FirstOrDefault(x => x.CropYear == years[i])?.Balance ?? 0;
			}

			data[i] = total;
		}

		data[4] = lst.FirstOrDefault(x => x.CropYear == cropYear)?.RejectedBalance ?? 0;
		data[5] = lst.FirstOrDefault(x => x.CropYear == cropYear)?.OrphanedBuyBalance ?? 0;
		data[6] = lst.FirstOrDefault(x => x.CropYear == cropYear)?.OrphanedSellBalance ?? 0;
		return data;
	}

	#endregion
}
