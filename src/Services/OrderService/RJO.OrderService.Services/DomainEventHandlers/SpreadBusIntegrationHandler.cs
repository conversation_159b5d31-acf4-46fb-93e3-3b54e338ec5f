using Hangfire;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.Events;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Persistence.Repositories.Logs;
using RJO.OrderService.Services.DTO;
using RJO.OrderService.Services.Services;

namespace RJO.OrderService.Services.DomainEventHandlers;

public class SpreadBusIntegrationHandler : INotificationHandler<ProcessSpreadTransaction>
{
	readonly IMarketTransactionRepository _marketTransactionRepository;
	readonly IBackgroundJobClient _backgroundJobClient;
	readonly ILogger<SpreadBusIntegrationHandler> _logger;
	readonly MarketStatusService _marketStatusService;
	readonly ILogWorkflowRepository _logWorkflowRepository;

	public SpreadBusIntegrationHandler(
		IMarketTransactionRepository marketTransactionRepository,
		IBackgroundJobClient backgroundJobClient,
		ILogger<SpreadBusIntegrationHandler> logger,
		MarketStatusService marketStatusService,
		ILogWorkflowRepository logWorkflowRepository)
	{
		_marketTransactionRepository = marketTransactionRepository;
		_backgroundJobClient = backgroundJobClient;
		_logger = logger;
		_marketStatusService = marketStatusService;
		_logWorkflowRepository = logWorkflowRepository;
	}

	public async Task Handle(ProcessSpreadTransaction notification, CancellationToken cancellationToken)
	{
		var transaction = await _marketTransactionRepository.GetById(notification.TransactionId);
		AssertionConcern.ArgumentIsNotNull(transaction, GeneralResources.RequiredValueIsNotPresent);

		if (!transaction.IsSpreadOrder)
		{
			_logger.LogError("Transaction {TransactionId} is not a spread order but was processed by SpreadBusIntegrationHandler", notification.TransactionId);
			return;
		}

		if (transaction.IsApproved && transaction.IsOnStatus(EMarketTransactionState.Pending))
		{
			var createSpreadOrderRequestJobOptions = new CreateSpreadOrderRequestJobOptions
			{
				TransactionId = transaction.Id,
				InternalNumber = transaction.InternalNumber,
				SpreadSymbol = transaction.SpreadSymbol,
				Email = transaction.CreatedBy,
				TenantId = notification.ApplicationTenant?.Id,
				IsBypassOn = false
			};

			var marketStatus = await _marketStatusService.GetStatusForSymbol(transaction.SpreadSymbol, "spread");
			string log;

			if (marketStatus != null && marketStatus.IsScheduled)
			{
				var date = DateTime.SpecifyKind(marketStatus.Offset.DateTime, DateTimeKind.Utc);
				_backgroundJobClient.Schedule<CreateSpreadOrderRequestJob>(x => x.Perform(createSpreadOrderRequestJobOptions, null), date.ToLocalTime());
				log = $"SpreadBusIntegrationHandler-CreateSpreadOrder: Schedule {transaction.InternalCode} - {date.ToLocalTime()} - {marketStatus}";
			}
			else // no schedule or immediately
			{
				if (marketStatus == null)
					_logger.LogError(GeneralResources.SessionInformationNotAvail + $" for spread transaction {transaction.InternalCode}");
				_backgroundJobClient.Enqueue<CreateSpreadOrderRequestJob>(x => x.Perform(createSpreadOrderRequestJobOptions, null));
				log = $"SpreadBusIntegrationHandler-CreateSpreadOrder: Queue {transaction.InternalCode} - {marketStatus}";
			}

			if (transaction.ContractId.HasValue)
			{
				_logWorkflowRepository.SetContract(transaction.ContractId.Value);
				await _logWorkflowRepository.AddLog("Spread Integration", log);
			}

			_logger.LogInformation("{Log}", log);
		}
	}
}
