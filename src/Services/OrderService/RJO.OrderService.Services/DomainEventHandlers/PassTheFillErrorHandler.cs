using MediatR;
using Microsoft.Extensions.Logging;
using RJO.OrderService.Domain.Events;

namespace RJO.OrderService.Services.DomainEventHandlers;

public class PassTheFillErrorHandler : INotificationHandler<ContractCannotBeProcessedByPassTheFill>
{
	readonly ILogger<PassTheFillErrorHandler> _logger;

	public PassTheFillErrorHandler(ILogger<PassTheFillErrorHandler> logger) => _logger = logger;

	public Task Handle(ContractCannotBeProcessedByPassTheFill notification, CancellationToken cancellationToken)
	{
		_logger.LogInformation("This contract wasn't processed by pass the fill {ContractId}", notification.ContractId);
		return Task.CompletedTask;
	}
}
