<?xml version="1.0" encoding="utf-8"?>

<WSCACONTRACTS xmlns="http://www.agtrax.com" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.agtrax.com cacontracts.xsd">
	<Cacontracts control="priced" function="Add">
		<Cacontract reference="123" number="0">
			<type value="Purchase" />
			<customer value="100" />
			<commodity value="1" />
			<branch value="1" />
			<position value="900" />
			<theirContract value="ABC123" />
			<status value="Open" />
			<issueDate value="2013-08-29" />
			<deliveryStart value="2014-07-01" />
			<deliveryEnd value="2014-07-31" />
			<dueDate value="2015-01-01" />
			<interestStartDate value="2013-09-01" />
			<interestRate value="2.5" />
			<priceBoard value="5.7500" />
			<priceBasis value="-0.2500" />
			<priceFreight value="0.0000" />
			<priceFee value="0.0000" />
			<price value="5.5000" />
			<futuresMonth value="2014-07" />
			<broker value="1" />
			<brokerAccount value="1" />
			<freight value="No" />
			<signed value="No" />
			<printed value="No" />
			<profit value="No" />
			<grade value="No. 1" />
			<offerExpiration value="2013-12-31" />
			<offerToPosition value="900" />
			<issueLbs value="600000" />
			<issueUnits value="0.00" />
			<appliedLbs value="0" />
			<appliedUnits value="0.00" />
			<adjustedLbs value="0" />
			<adjustedUnits value="0.00" />
			<balanceLbs value="0" />
			<balanceUnits value="0.00" />
			<remarks>
				<remark value="This is my remark." />
			</remarks>
		</Cacontract>
		<Cacontract reference="124" number="0">
			<type value="Purchase" />
			<customer value="100" />
			<commodity value="1" />
			<branch value="1" />
			<issueDate value="2013-08-29" />
			<deliveryStart value="2014-07-01" />
			<deliveryEnd value="2014-07-31" />
			<issueLbs value="600000" />
			<issueUnits value="0.00" />
		</Cacontract>
	</Cacontracts>
	<Cacontracts control="priced" function="Adjust">
		<Cacontract reference="123" number="123">
			<type value="Purchase" />
			<customer value="100" />
			<adjustedLbs value="-60000" />
			<adjustedUnits value="0.00" />
		</Cacontract>
	</Cacontracts>
</WSCACONTRACTS>
<?xml version="1.0" encoding="utf-8"?>

<WSCACONTRACTS xmlns="http://www.agtrax.com" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.agtrax.com cacontracts.xsd">
	<Cacontracts control="priced" function="Get" customer="50" />
</WSCACONTRACTS>
<?xml version="1.0" encoding="utf-8"?>

<WSCACONTRACTS xmlns="http://www.agtrax.com" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.agtrax.com cacontracts.xsd">
	<Cacontracts control="priced" function="Adjust">
		<Cacontract reference="123" number="123">
			<type value="Purchase" />
			<customer value="100" />
			<adjustedLbs value="-60000" />
			<adjustedUnits value="0.00" />
		</Cacontract>
		<Cacontract reference="124" number="124">
			<type value="Purchase" />
			<customer value="100" />
			<adjustedLbs value="0" />
			<adjustedUnits value="-1000.00" uom="60BU" />
		</Cacontract>
	</Cacontracts>
</WSCACONTRACTS>