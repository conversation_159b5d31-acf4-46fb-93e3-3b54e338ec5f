<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>AgTrax Schema Documentation</title>
<meta http-equiv="Content-Type" content="text/xml; charset=iso-8859-1"/>
<style type="text/css">
/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */
/* More-configurable styles */

/******** General ********/

/* Document body */
	body {
		color: Black;
		background-color: White;
		font-family: Arial, sans-serif;
		font-size: 10pt;
	}

	/* Horizontal rules */

	hr { color: black; }

	/* Document title */

	h1 {
		font-size: 18pt;
		letter-spacing: 2px;
		border-bottom: 1px #ccc solid;
		padding-top: 5px;
		padding-bottom: 5px;
	}

	/* Main section headers */

	h2 {
		font-size: 14pt;
		letter-spacing: 1px;
	}

	/* Sub-section headers */

	h3, h3 a, h3 span {
		font-size: 12pt;
		font-weight: bold;
		color: black;
	}

	/* Table displaying the properties of the schema components or the
   schema document itself */

	table.properties th, table.properties th a {
		color: black;
		background-color: #F99; /* Pink */
	}

	table.properties td {
		background-color: #eee; /* Gray */
	}


	/******** Table of Contents Section ********/

	/* Controls for switching between printing and viewing modes */

	div#printerControls {
		color: #963; /* Orange-brown */
	}

	/* Controls that can collapse or expand all XML Instance
   Representation and Schema Component Representation boxes */

	div#globalControls { border: 2px solid #999; }


	/******** Schema Document Properties Section ********/

	/* Table displaying the namespaces declared in the schema */

	table.namespaces th { background-color: #ccc; }

	table.namespaces td { background-color: #eee; }

	/* Target namespace of the schema */

	span.targetNS {
		color: #06C;
		font-weight: bold;
	}


	/******** Schema Components' Sections ********/

	/* Name of schema component */

	.name {
		color: #F93; /* Orange */
	}

	/* Hierarchy table */

	table.hierarchy {
		border: 2px solid #999; /* Gray */
	}

	/* XML Instance Representation table */

	div.sample div.contents { border: 2px dashed black; }

	/* Schema Component Representation table */

	div.schemaComponent div.contents { border: 2px black solid; }


	/******** Glossary Section ********/

	/* Glossary Terms */

	.glossaryTerm {
		color: #036; /* Blue */
	}


	/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */

	/* Printer-version styles */

	@media print {
		/* Ensures that controls are hidden when printing */
		div#printerControls { visibility: hidden; }

		div#globalControls { visibility: hidden; }

		#legend { display: none; }

		#legendTOC { display: none; }

		#glossary { display: none; }

		#glossaryTOC { display: none; }
	}

	/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */

	/* Base styles */

	/******** General ********/

	/* Unordered lists */

	ul {
		margin-left: 1.5em;
		margin-bottom: 0em;
	}

	/* Tables */

	table {
		margin-top: 10px;
		margin-bottom: 10px;
		margin-left: 2px;
		margin-right: 2px;
	}

	table th, table td {
		font-size: 10pt;
		vertical-align: top;
		padding-top: 3px;
		padding-bottom: 3px;
		padding-left: 10px;
		padding-right: 10px;
	}

	table th {
		font-weight: bold;
		text-align: left;
	}

	/* Table displaying the properties of the schema components or the
   schema document itself */

	table.properties { width: 90%; }

	table.properties th { width: 30%; }

	/* Boxes that can make its content appear and disappear*/

	div.box { margin: 1em; }

	/* Box caption */

	div.box span.caption { font-weight: bold; }

	/* Button to open and close the box */

	div.box input.control {
		width: 1.4em;
		height: 1.4em;
		text-align: center;
		vertical-align: middle;
		font-size: 11pt;
	}

	/* Box contents */

	div.box div.contents { margin-top: 3px; }


	/******** Table of Contents Section ********/

	/* Controls for switching between printing and viewing modes */

	div#printerControls {
		white-space: nowrap;
		font-weight: bold;
		padding: 5px;
		margin: 5px;
	}

	/* Controls that can collapse or expand all XML Instance
   Representation and Schema Component Representation boxes */

	div#globalControls {
		padding: 10px;
		margin: 5px;
	}


	/******** Schema Document Properties Section ********/

	/* Table displaying the namespaces declared in the schema */

	table.namespaces th { }

	table.namespaces td { }

	/* Target namespace of the schema */

	span.targetNS { }


	/******** Schema Components' Sections ********/

	/* Name of schema component */

	.name { }

	/* Hierarchy table */

	table.hierarchy { width: 90%; }

	table.hierarchy th {
		font-weight: normal;
		font-style: italic;
		width: 20%;
	}

	table.hierarchy th, table.hierarchy td { padding: 5px; }

	/* XML Instance Representation table */

	div.sample { width: 90%; }

	div.sample div.contents {
		padding: 5px;
		font-family: Courier New, sans-serif;
		font-size: 10pt;
	}

	/* Normal elements and attributes */

	div.sample div.contents, div.sample div.contents a { color: black; }

	/* Group Headers */

	div.sample div.contents .group, div.sample div.contents .group a {
		color: #999; /* Light gray */
	}

	/* Type Information */

	div.sample div.contents .type, div.sample div.contents .type a {
		color: #999; /* Light gray */
	}

	/* Occurrence Information */

	div.sample div.contents .occurs, div.sample div.contents .occurs a {
		color: #999; /* Light gray */
	}

	/* Fixed values */

	div.sample div.contents .fixed {
		color: #063; /* Green */
		font-weight: bold;
	}

	/* Simple type constraints */

	div.sample div.contents .constraint, div.sample div.contents .constraint a {
		color: #999; /* Light gray */
	}

	/* Elements and attributes inherited from base type */

	div.sample div.contents .inherited, div.sample div.contents .inherited a {
		color: #666; /* Dark gray */
	}

	/* Elements and attributes added to or changed from base type */

	div.sample div.contents .newFields { font-weight: bold; }

	/* Other type of information */

	div.sample div.contents .other, div.sample div.contents .other a {
		color: #369; /* Blue */
		font-style: italic;
	}

	/* Link to open up window displaying documentation */

	div.sample div.contents a.documentation {
		text-decoration: none;
		padding-left: 3px;
		padding-right: 3px;
		padding-top: 0px;
		padding-bottom: 0px;
		font-weight: bold;
		font-size: 11pt;
		background-color: #FFD;
		color: #069;
	}

	/* Invert colors when hovering over link to open up window
      displaying documentation */

	div.sample div.contents a.documentation:hover {
		color: #FFD;
		background-color: #069;
	}

	/* Schema Component Representation table */

	div.schemaComponent { width: 90%; }

	div.schemaComponent div.contents {
		font-family: Courier New, sans-serif;
		font-size: 10pt;
		padding: 5px;
	}

	/* Syntax characters */

	div.schemaComponent div.contents {
		color: #00f; /* blue */
	}

	/* Element and attribute tags */

	div.schemaComponent div.contents .scTag {
		color: #933; /* maroon */
	}

	/* Element and attribute content */

	div.schemaComponent div.contents .scContent, div.schemaComponent div.contents .scContent a {
		color: black;
		font-weight: bold;
	}

	/* Comments */

	div.schemaComponent div.contents .comment {
		color: #999; /* Light gray */
	}

	/******** Legend Section ********/

	div#legend table, div#legend div { margin-bottom: 3px; }

	div#legend div.hint {
		color: #999; /* Light gray */
		width: 90%;
		margin-left: 1em;
		margin-bottom: 2em;
	}


	/******** Glossary Section ********/

	/* Glossary Terms */

	.glossaryTerm { font-weight: bold; }


	/******** Footer ********/

	.footer { font-size: 8pt; }
</style>
<script type="text/javascript">
	<!--
/* IDs of XML Instance Representation boxes */
	var xiBoxes = new Array('element_WSCACONTRACTSResponse_xibox', 'type_contract_xibox', 'type_number_xibox', 'type_control_xibox', 'type_function_xibox', 'type_trueFalse_xibox', 'type_customer_xibox', 'type_commodity_xibox', 'type_contractType_xibox', 'type_branch_xibox', 'type_position_xibox', 'type_status_xibox', 'type_YesNo_xibox', 'type_d5_xibox', 'type_d6_4_xibox', 'type_d8_4_xibox', 'type_n8_4_xibox', 'type_n8_2_xibox', 'type_n8_xibox', 'type_d10_xibox', 'type_str6_xibox', 'type_str10_xibox', 'type_str20_xibox', 'type_str25_xibox', 'type_str30_xibox', 'type_str50_xibox');
/* IDs of Schema Component Representation boxes */
	var scBoxes = new Array('schema_scbox', 'element_WSCACONTRACTSResponse_scbox', 'type_contract_scbox', 'type_number_scbox', 'type_control_scbox', 'type_function_scbox', 'type_trueFalse_scbox', 'type_customer_scbox', 'type_commodity_scbox', 'type_contractType_scbox', 'type_branch_scbox', 'type_position_scbox', 'type_status_scbox', 'type_YesNo_scbox', 'type_d5_scbox', 'type_d6_4_scbox', 'type_d8_4_scbox', 'type_n8_4_scbox', 'type_n8_2_scbox', 'type_n8_scbox', 'type_d10_scbox', 'type_str6_scbox', 'type_str10_scbox', 'type_str20_scbox', 'type_str25_scbox', 'type_str30_scbox', 'type_str50_scbox');

/**
 * Can get the ID of the button controlling
 * a collapseable box by concatenating
 * this string onto the ID of the box itself.
 */
	var B_SFIX = "_button";

/**
 * Counter of documentation windows
 * Used to give each window a unique name
 */
	var windowCount = 0;

/**
 * Returns an element in the current HTML document.
 *
 * @param elementID Identifier of HTML element
 * @return               HTML element object
 */
	function getElementObject(elementID) {
		var elemObj = null;
		if (document.getElementById) {
			elemObj = document.getElementById(elementID);
		}
		return elemObj;
	}

/**
 * Closes a collapseable box.
 *
 * @param boxObj       Collapseable box
 * @param buttonObj Button controlling box
 */
	function closeBox(boxObj, buttonObj) {
		if (boxObj == null || buttonObj == null) {
			// Box or button not found
		} else {
			// Change 'display' CSS property of box
			boxObj.style.display = "none";

			// Change text of button
			if (boxObj.style.display == "none") {
				buttonObj.value = " + ";
			}
		}
	}

/**
 * Opens a collapseable box.
 *
 * @param boxObj       Collapseable box
 * @param buttonObj Button controlling box
 */
	function openBox(boxObj, buttonObj) {
		if (boxObj == null || buttonObj == null) {
			// Box or button not found
		} else {
			// Change 'display' CSS property of box
			boxObj.style.display = "block";

			// Change text of button
			if (boxObj.style.display == "block") {
				buttonObj.value = " - ";
			}
		}
	}

/**
 * Sets the state of a collapseable box.
 *
 * @param boxID Identifier of box
 * @param open If true, box is "opened",
 *             Otherwise, box is "closed".
 */
	function setState(boxID, open) {
		var boxObj = getElementObject(boxID);
		var buttonObj = getElementObject(boxID + B_SFIX);
		if (boxObj == null || buttonObj == null) {
			// Box or button not found
		} else if (open) {
			openBox(boxObj, buttonObj);
			// Make button visible
			buttonObj.style.display = "inline";
		} else {
			closeBox(boxObj, buttonObj);
			// Make button visible
			buttonObj.style.display = "inline";
		}
	}

/**
 * Switches the state of a collapseable box, e.g.
 * if it's opened, it'll be closed, and vice versa.
 *
 * @param boxID Identifier of box
 */
	function switchState(boxID) {
		var boxObj = getElementObject(boxID);
		var buttonObj = getElementObject(boxID + B_SFIX);
		if (boxObj == null || buttonObj == null) {
			// Box or button not found
		} else if (boxObj.style.display == "none") {
			// Box is closed, so open it
			openBox(boxObj, buttonObj);
		} else if (boxObj.style.display == "block") {
			// Box is opened, so close it
			closeBox(boxObj, buttonObj);
		}
	}

/**
 * Closes all boxes in a given list.
 *
 * @param boxList Array of box IDs
 */
	function collapseAll(boxList) {
		var idx;
		for (idx = 0; idx < boxList.length; idx++) {
			var boxObj = getElementObject(boxList[idx]);
			var buttonObj = getElementObject(boxList[idx] + B_SFIX);
			closeBox(boxObj, buttonObj);
		}
	}

/**
 * Open all boxes in a given list.
 *
 * @param boxList Array of box IDs
 */
	function expandAll(boxList) {
		var idx;
		for (idx = 0; idx < boxList.length; idx++) {
			var boxObj = getElementObject(boxList[idx]);
			var buttonObj = getElementObject(boxList[idx] + B_SFIX);
			openBox(boxObj, buttonObj);
		}
	}

/**
 * Makes all the control buttons of boxes appear.
 *
 * @param boxList Array of box IDs
 */
	function viewControlButtons(boxList) {
		var idx;
		for (idx = 0; idx < boxList.length; idx++) {
			buttonObj = getElementObject(boxList[idx] + B_SFIX);
			if (buttonObj != null) {
				buttonObj.style.display = "inline";
			}
		}
	}

/**
 * Makes all the control buttons of boxes disappear.
 *
 * @param boxList Array of box IDs
 */
	function hideControlButtons(boxList) {
		var idx;
		for (idx = 0; idx < boxList.length; idx++) {
			buttonObj = getElementObject(boxList[idx] + B_SFIX);
			if (buttonObj != null) {
				buttonObj.style.display = "none";
			}
		}
	}

/**
 * Sets the page for either printing mode
 * or viewing mode. In printing mode, the page
 * is made to be more readable when printing it out.
 * In viewing mode, the page is more browsable.
 *
 * @param isPrinterVersion If true, display in
 *                                 printing mode; otherwise,
 *                                 in viewing mode
 */
	function displayMode(isPrinterVersion) {
		var obj;
		if (isPrinterVersion) {
			// Hide global control buttons
			obj = getElementObject("globalControls");
			if (obj != null) {
				obj.style.visibility = "hidden";
			}
			// Hide Legend
			obj = getElementObject("legend");
			if (obj != null) {
				obj.style.display = "none";
			}
			obj = getElementObject("legendTOC");
			if (obj != null) {
				obj.style.display = "none";
			}
			// Hide Glossary
			obj = getElementObject("glossary");
			if (obj != null) {
				obj.style.display = "none";
			}
			obj = getElementObject("glossaryTOC");
			if (obj != null) {
				obj.style.display = "none";
			}

			// Expand all XML Instance Representation tables
			expandAll(xiBoxes);
			// Expand all Schema Component Representation tables
			expandAll(scBoxes);

			// Hide Control buttons
			hideControlButtons(xiBoxes);
			hideControlButtons(scBoxes);
		} else {
			// View global control buttons
			obj = getElementObject("globalControls");
			if (obj != null) {
				obj.style.visibility = "visible";
			}
			// View Legend
			obj = getElementObject("legend");
			if (obj != null) {
				obj.style.display = "block";
			}
			obj = getElementObject("legendTOC");
			if (obj != null) {
				obj.style.display = "block";
			}
			// View Glossary
			obj = getElementObject("glossary");
			if (obj != null) {
				obj.style.display = "block";
			}
			obj = getElementObject("glossaryTOC");
			if (obj != null) {
				obj.style.display = "block";
			}

			// Expand all XML Instance Representation tables
			expandAll(xiBoxes);
			// Collapse all Schema Component Representation tables
			collapseAll(scBoxes);

			// View Control buttons
			viewControlButtons(xiBoxes);
			viewControlButtons(scBoxes);
		}
	}

/**
 * Opens up a window displaying the documentation
 * of a schema component in the XML Instance
 * Representation table.
 *
 * @param compDesc      Description of schema component
 * @param compName      Name of schema component
 * @param docTextArray Array containing the paragraphs
 *                           of the new document
 */
	function viewDocumentation(compDesc, compName, docTextArray) {
		var width = 400;
		var height = 200;
		var locX = 100;
		var locY = 200;

		/* Generate content */
		var actualText = "<html>";
		actualText += "<head><title>";
		actualText += compDesc;
		if (compName != '') {
			actualText += ": " + compName;
		}
		actualText += "</title></head>";
		actualText += "<body bgcolor=\"#FFFFEE\">";
		// Title
		actualText += "<p style=\"font-family: Arial, sans-serif; font-size: 12pt; font-weight: bold; letter-spacing:1px;\">";
		actualText += compDesc;
		if (compName != '') {
			actualText += ": <span style=\"color:#006699\">" + compName + "</span>";
		}
		actualText += "</p>";
		// Documentation
		var idx;
		for (idx = 0; idx < docTextArray.length; idx++) {
			actualText += "<p style=\"font-family: Arial, sans-serif; font-size: 10pt;\">" + docTextArray[idx] + "</p>";
		}
		// Link to close window
		actualText += "<a href=\"javascript:void(0)\" onclick=\"window.close();\" style=\"font-family: Arial, sans-serif; font-size: 8pt;\">Close</a>";
		actualText += "</body></html>";

		/* Display window */
		windowCount++;
		var docWindow = window.open("", "documentation" + windowCount, "toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable,alwaysRaised,dependent,titlebar=no,width=" + width + ",height=" + height + ",screenX=" + locX + ",left=" + locX + ",screenY=" + locY + ",top=" + locY);
		docWindow.document.write(actualText);
	}

// -->
</script>
</head>
<body>
<h1>
	<a name="top">AgTrax Schema Documentation</a>
</h1>
<div style="float: right;">
	<div id="printerControls" style="display: none;">
		<input type="checkbox" onclick="displayMode(this.checked)"/>Printer-friendly Version
	</div>
	<script type="text/javascript">
		<!--

		var pc = getElementObject("printerControls");
		if (pc != null) {
			pc.style.display = "block";
		}

// -->
	</script>
	<div id="globalControls" style="display: none">
		<strong>XML Instance Representation:</strong><br/>
		<span style="margin-left: 1em; white-space: nowrap">[ <a href="javascript:void(0)" onclick="expandAll(xiBoxes)">Expand All</a> | <a href="javascript:void(0)" onclick="collapseAll(xiBoxes)">Collapse All</a> ]</span><br/><br/>
		<strong>Schema Component Representation:</strong><br/>
		<span style="margin-left: 1em; white-space: nowrap">[ <a href="javascript:void(0)" onclick="expandAll(scBoxes)">Expand All</a> | <a href="javascript:void(0)" onclick="collapseAll(scBoxes)">Collapse All</a> ]</span>
	</div>
	<script type="text/javascript">
		<!--

		var gc = getElementObject("globalControls");
		if (gc != null) {
			gc.style.display = "block";
		}

// -->
	</script>
</div>
<h2>Table of Contents</h2>
<ul>
	<li>
		<a href="#SchemaProperties">Schema Document Properties</a>
	</li>
	<li>
		<a href="#SchemaDeclarations">Global Declarations</a>
		<ul>
			<li>
				<a href="#element_WSCACONTRACTSResponse">
					Element: <strong>WSCACONTRACTSResponse</strong>
				</a>
			</li>
		</ul>
	</li>
	<li>
		<a href="#SchemaDefinitions">Global Definitions</a>
		<ul>
			<li>
				<a href="#type_branch">
					Simple Type: <strong>branch</strong>
				</a>
			</li>
			<li>
				<a href="#type_commodity">
					Simple Type: <strong>commodity</strong>
				</a>
			</li>
			<li>
				<a href="#type_contract">
					Simple Type: <strong>contract</strong>
				</a>
			</li>
			<li>
				<a href="#type_contractType">
					Simple Type: <strong>contractType</strong>
				</a>
			</li>
			<li>
				<a href="#type_control">
					Simple Type: <strong>control</strong>
				</a>
			</li>
			<li>
				<a href="#type_customer">
					Simple Type: <strong>customer</strong>
				</a>
			</li>
			<li>
				<a href="#type_d10">
					Simple Type: <strong>d10</strong>
				</a>
			</li>
			<li>
				<a href="#type_d5">
					Simple Type: <strong>d5</strong>
				</a>
			</li>
			<li>
				<a href="#type_d6_4">
					Simple Type: <strong>d6_4</strong>
				</a>
			</li>
			<li>
				<a href="#type_d8_4">
					Simple Type: <strong>d8_4</strong>
				</a>
			</li>
			<li>
				<a href="#type_function">
					Simple Type: <strong>function</strong>
				</a>
			</li>
			<li>
				<a href="#type_n8">
					Simple Type: <strong>n8</strong>
				</a>
			</li>
			<li>
				<a href="#type_n8_2">
					Simple Type: <strong>n8_2</strong>
				</a>
			</li>
			<li>
				<a href="#type_n8_4">
					Simple Type: <strong>n8_4</strong>
				</a>
			</li>
			<li>
				<a href="#type_number">
					Simple Type: <strong>number</strong>
				</a>
			</li>
			<li>
				<a href="#type_position">
					Simple Type: <strong>position</strong>
				</a>
			</li>
			<li>
				<a href="#type_status">
					Simple Type: <strong>status</strong>
				</a>
			</li>
			<li>
				<a href="#type_str10">
					Simple Type: <strong>str10</strong>
				</a>
			</li>
			<li>
				<a href="#type_str20">
					Simple Type: <strong>str20</strong>
				</a>
			</li>
			<li>
				<a href="#type_str25">
					Simple Type: <strong>str25</strong>
				</a>
			</li>
			<li>
				<a href="#type_str30">
					Simple Type: <strong>str30</strong>
				</a>
			</li>
			<li>
				<a href="#type_str50">
					Simple Type: <strong>str50</strong>
				</a>
			</li>
			<li>
				<a href="#type_str6">
					Simple Type: <strong>str6</strong>
				</a>
			</li>
			<li>
				<a href="#type_trueFalse">
					Simple Type: <strong>trueFalse</strong>
				</a>
			</li>
			<li>
				<a href="#type_YesNo">
					Simple Type: <strong>YesNo</strong>
				</a>
			</li>
		</ul>
	</li>
</ul>
<ul id="legendTOC" style="margin-top: 0em">
	<li>
		<a href="#Legend">Legend</a>
	</li>
</ul>
<ul id="glossaryTOC" style="margin-top: 0em">
	<li>
		<a href="#Glossary">Glossary</a>
	</li>
</ul>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h2>
	<a name="SchemaProperties">Schema Document Properties</a>
</h2>
<table class="properties">
	<tr>
		<th>
			<a title="Look up 'Target Namespace' in glossary" href="#term_TargetNS">Target Namespace</a>
		</th>
		<td>
			<span class="targetNS">http://www.agtrax.com</span>
		</td>
	</tr>
	<tr>
		<th>Element and Attribute Namespaces</th>
		<td>
			<ul>
				<li>Global element and attribute declarations belong to this schema's target namespace.</li>
				<li>By default, local element declarations belong to this schema's target namespace.</li>
				<li>By default, local attribute declarations have no namespace.</li>
			</ul>
		</td>
	</tr>
</table>
<h3>Declared Namespaces</h3>
<table class="namespaces">
	<tr>
		<th>Prefix</th>
		<th>Namespace</th>
	</tr>
	<tr>
		<td>
			<a name="ns_">Default namespace</a>
		</td>
		<td>
			<span class="targetNS">http://www.agtrax.com</span>
		</td>
	</tr>
	<tr>
		<td>
			<a name="ns_xml">xml</a>
		</td>
		<td>http://www.w3.org/XML/1998/namespace</td>
	</tr>
	<tr>
		<td>
			<a name="ns_xsd">xsd</a>
		</td>
		<td>http://www.w3.org/2001/XMLSchema</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="schema_scbox_button" class="control" onclick="switchState('schema_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="schema_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:schema</span> <span class="scTag">targetNamespace</span>="<span class="scContent">http://www.agtrax.com</span>" <span class="scTag">elementFormDefault</span>="<span class="scContent">qualified</span>"&gt;
			<div class="scContent" style="margin-left: 1.5em">...</div>&lt;/<span class="scTag">xsd:schema</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('schema_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h2>
	<a name="SchemaDeclarations">Global Declarations</a>
</h2>
<h3>
	Element: <a name="element_WSCACONTRACTSResponse" class="name">WSCACONTRACTSResponse</a>
</h3>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>WSCACONTRACTSResponse</td>
	</tr>
	<tr>
		<th>Type</th>
		<td>Locally-defined complex type</td>
	</tr>
	<tr>
		<th>
			<a title="Look up 'Nillable' in glossary" href="#term_Nillable">Nillable</a>
		</th>
		<td>no</td>
	</tr>
	<tr>
		<th>
			<a title="Look up 'Abstract' in glossary" href="#term_Abstract">Abstract</a>
		</th>
		<td>no</td>
	</tr>
</table>
<div class="sample box">
<div>
	<input type="button" id="element_WSCACONTRACTSResponse_xibox_button" class="control" onclick="switchState('element_WSCACONTRACTSResponse_xibox');return false;" style="display: none"/> <span class="caption">XML Instance Representation</span>
</div>
<div id="element_WSCACONTRACTSResponse_xibox" class="contents">
<div style="margin-left: 0em">
&lt;WSCACONTRACTSResponse&gt; <br/>
<div style="margin-left: 1.5em">
&lt;Cacontracts<br/>
<span style="margin-left: 0.5em">
	control="
	<span class="type">
		<a title="Jump to &quot;control&quot; type definition." href="#type_control">control</a>
	</span> <span class="occurs">[1]</span>"
</span><br/>
<span style="margin-left: 0.5em">
	function="
	<span class="type">
		<a title="Jump to &quot;function&quot; type definition." href="#type_function">function</a>
	</span> <span class="occurs">[1]</span>"
</span><br/>
<span style="margin-left: 0.5em">
	customer="
	<span class="type">
		<a title="Jump to &quot;customer&quot; type definition." href="#type_customer">customer</a>
	</span> <span class="occurs">[0..1]</span>"
</span><br/>
<span style="margin-left: 0.5em">
	commodity="
	<span class="type">
		<a title="Jump to &quot;commodity&quot; type definition." href="#type_commodity">commodity</a>
	</span> <span class="occurs">[0..1]</span>"
</span>&gt; <span class="occurs">[0..*]</span> <br/>
<div style="margin-left: 1.5em">
&lt;Cacontract<br/>
<span style="margin-left: 0.5em">
	reference="
	<span class="type">
		<a title="Jump to &quot;str30&quot; type definition." href="#type_str30">str30</a>
	</span> <span class="occurs">[1]</span>"
</span><br/>
<span style="margin-left: 0.5em">
	number="
	<span class="type">
		<a title="Jump to &quot;contract&quot; type definition." href="#type_contract">contract</a>
	</span> <span class="occurs">[1]</span>"
</span><br/>
<span style="margin-left: 0.5em">
	returnCode="
	<span class="type">
		<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
	</span> <span class="occurs">[1]</span>"
</span>&gt; <span class="occurs">[0..*]</span> <br/>
<div style="margin-left: 1.5em">
	&lt;type<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;contractType&quot; type definition." href="#type_contractType">contractType</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;customer<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;customer&quot; type definition." href="#type_customer">customer</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		name="
		<span class="type">
			<a title="Jump to &quot;str30&quot; type definition." href="#type_str30">str30</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;commodity<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;commodity&quot; type definition." href="#type_commodity">commodity</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		description="
		<span class="type">
			<a title="Jump to &quot;str20&quot; type definition." href="#type_str20">str20</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;branch<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;branch&quot; type definition." href="#type_branch">branch</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		name="
		<span class="type">
			<a title="Jump to &quot;str20&quot; type definition." href="#type_str20">str20</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;position<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;position&quot; type definition." href="#type_position">position</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		description="
		<span class="type">
			<a title="Jump to &quot;str25&quot; type definition." href="#type_str25">str25</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		subContract="
		<span class="type">
			<a title="Jump to &quot;trueFalse&quot; type definition." href="#type_trueFalse">trueFalse</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		moveLoads="
		<span class="type">
			<a title="Jump to &quot;trueFalse&quot; type definition." href="#type_trueFalse">trueFalse</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		autoClose="
		<span class="type">
			<a title="Jump to &quot;trueFalse&quot; type definition." href="#type_trueFalse">trueFalse</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		copyRemarks="
		<span class="type">
			<a title="Jump to &quot;trueFalse&quot; type definition." href="#type_trueFalse">trueFalse</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;theirContract<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;str10&quot; type definition." href="#type_str10">str10</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;status<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;status&quot; type definition." href="#type_status">status</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">&lt;issueDate<br/><span style="margin-left: 0.5em"> value="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:date</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[0..1]</span></div>
<div style="margin-left: 1.5em">&lt;deliveryStart<br/><span style="margin-left: 0.5em"> value="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:date</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[0..1]</span></div>
<div style="margin-left: 1.5em">&lt;deliveryEnd<br/><span style="margin-left: 0.5em"> value="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:date</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[0..1]</span></div>
<div style="margin-left: 1.5em">&lt;dueDate<br/><span style="margin-left: 0.5em"> value="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:date</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[0..1]</span></div>
<div style="margin-left: 1.5em">&lt;interestStartDate<br/><span style="margin-left: 0.5em"> value="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:date</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[0..1]</span></div>
<div style="margin-left: 1.5em">
	&lt;interestRate<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;d6_4&quot; type definition." href="#type_d6_4">d6_4</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;priceBoard<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;d8_4&quot; type definition." href="#type_d8_4">d8_4</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;priceBasis<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;n8_4&quot; type definition." href="#type_n8_4">n8_4</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;priceFreight<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;n8_4&quot; type definition." href="#type_n8_4">n8_4</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;priceFee<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;n8_4&quot; type definition." href="#type_n8_4">n8_4</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;priceBasisTotal<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;n8_4&quot; type definition." href="#type_n8_4">n8_4</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;price<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;d8_4&quot; type definition." href="#type_d8_4">d8_4</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">&lt;futuresMonth<br/><span style="margin-left: 0.5em"> value="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:gYearMonth</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[0..1]</span></div>
<div style="margin-left: 1.5em">
	&lt;broker<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;d5&quot; type definition." href="#type_d5">d5</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;brokerAccount<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;d10&quot; type definition." href="#type_d10">d10</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;freight<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;YesNo&quot; type definition." href="#type_YesNo">YesNo</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;signed<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;YesNo&quot; type definition." href="#type_YesNo">YesNo</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;printed<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;YesNo&quot; type definition." href="#type_YesNo">YesNo</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;profit<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;YesNo&quot; type definition." href="#type_YesNo">YesNo</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;grade<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;str6&quot; type definition." href="#type_str6">str6</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">&lt;offerExpiration<br/><span style="margin-left: 0.5em"> value="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:date</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[0..1]</span></div>
<div style="margin-left: 1.5em">
	&lt;offerToPosition<br/>
	<span style="margin-left: 0.5em">
		value="
		<span class="type">
			<a title="Jump to &quot;position&quot; type definition." href="#type_position">position</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;issued<br/>
	<span style="margin-left: 0.5em">
		pounds="
		<span class="type">
			<a title="Jump to &quot;n8&quot; type definition." href="#type_n8">n8</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		bushels="
		<span class="type">
			<a title="Jump to &quot;n8_2&quot; type definition." href="#type_n8_2">n8_2</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;applied<br/>
	<span style="margin-left: 0.5em">
		pounds="
		<span class="type">
			<a title="Jump to &quot;n8&quot; type definition." href="#type_n8">n8</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		bushels="
		<span class="type">
			<a title="Jump to &quot;n8_2&quot; type definition." href="#type_n8_2">n8_2</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;adjusted<br/>
	<span style="margin-left: 0.5em">
		pounds="
		<span class="type">
			<a title="Jump to &quot;n8&quot; type definition." href="#type_n8">n8</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		bushels="
		<span class="type">
			<a title="Jump to &quot;n8_2&quot; type definition." href="#type_n8_2">n8_2</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;balance<br/>
	<span style="margin-left: 0.5em">
		pounds="
		<span class="type">
			<a title="Jump to &quot;n8&quot; type definition." href="#type_n8">n8</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		bushels="
		<span class="type">
			<a title="Jump to &quot;n8_2&quot; type definition." href="#type_n8_2">n8_2</a>
		</span> <span class="occurs">[1]</span>"
	</span>/&gt; <span class="occurs">[0..1]</span>
</div>
<div style="margin-left: 1.5em">
	&lt;remarks&gt; <span class="occurs">[0..1]</span> <br/>
	<div style="margin-left: 1.5em">
		&lt;remark<br/>
		<span style="margin-left: 0.5em">
			value="
			<span class="type">
				<a title="Jump to &quot;str50&quot; type definition." href="#type_str50">str50</a>
			</span> <span class="occurs">[1]</span>"
		</span>/&gt; <span class="occurs">[1..10]</span>
	</div>&lt;/remarks&gt;
</div>
<div style="margin-left: 1.5em">
	&lt;Errors&gt; <span class="occurs">[0..1]</span> <br/>
	<div style="margin-left: 1.5em">&lt;Error<br/><span style="margin-left: 0.5em"> description="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[0..*]</span></div>&lt;/Errors&gt;
</div>
<div style="margin-left: 1.5em">
	&lt;sysErrors<br/>
	<span style="margin-left: 0.5em">
		numErrs="
		<span class="type">
			<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		errNum="
		<span class="type">
			<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/><span style="margin-left: 0.5em"> errTitle="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> errText="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span>&gt; <span class="occurs">[0..1]</span> <br/>
	<div style="margin-left: 1.5em">
		&lt;sysError<br/>
		<span style="margin-left: 0.5em">
			seq="
			<span class="type">
				<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
			</span> <span class="occurs">[1]</span>"
		</span><br/>
		<span style="margin-left: 0.5em">
			errNum="
			<span class="type">
				<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
			</span> <span class="occurs">[1]</span>"
		</span><br/><span style="margin-left: 0.5em"> errTitle="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> errText="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> dateTime="<span class="type">anySimpleType</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> routine="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> line="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:integer</span> <span class="occurs">[1]</span>"</span>&gt; <span class="occurs">[1..*]</span> <br/>
		<div style="margin-left: 1.5em">
			&lt;synergy<br/>
			<span style="margin-left: 0.5em">
				errNum="
				<span class="type">
					<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
				</span> <span class="occurs">[1]</span>"
			</span><br/><span style="margin-left: 0.5em"> errText="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[1]</span>
		</div>
		<div style="margin-left: 1.5em">
			&lt;fileErr<br/>
			<span style="margin-left: 0.5em">
				errNum="
				<span class="type">
					<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
				</span> <span class="occurs">[1]</span>"
			</span><br/><span style="margin-left: 0.5em"> errText="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[1]</span>
		</div>&lt;/sysError&gt;
	</div>&lt;/sysErrors&gt;
</div>&lt;/Cacontract&gt;
</div>
<div style="margin-left: 1.5em">
	&lt;Errors&gt; <span class="occurs">[0..1]</span> <br/>
	<div style="margin-left: 1.5em">&lt;Error<br/><span style="margin-left: 0.5em"> description="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[0..*]</span></div>&lt;/Errors&gt;
</div>
<div style="margin-left: 1.5em">
	&lt;sysErrors<br/>
	<span style="margin-left: 0.5em">
		numErrs="
		<span class="type">
			<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		errNum="
		<span class="type">
			<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/><span style="margin-left: 0.5em"> errTitle="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> errText="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span>&gt; <span class="occurs">[0..1]</span> <br/>
	<div style="margin-left: 1.5em">
		&lt;sysError<br/>
		<span style="margin-left: 0.5em">
			seq="
			<span class="type">
				<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
			</span> <span class="occurs">[1]</span>"
		</span><br/>
		<span style="margin-left: 0.5em">
			errNum="
			<span class="type">
				<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
			</span> <span class="occurs">[1]</span>"
		</span><br/><span style="margin-left: 0.5em"> errTitle="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> errText="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> dateTime="<span class="type">anySimpleType</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> routine="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> line="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:integer</span> <span class="occurs">[1]</span>"</span>&gt; <span class="occurs">[1..*]</span> <br/>
		<div style="margin-left: 1.5em">
			&lt;synergy<br/>
			<span style="margin-left: 0.5em">
				errNum="
				<span class="type">
					<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
				</span> <span class="occurs">[1]</span>"
			</span><br/><span style="margin-left: 0.5em"> errText="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[1]</span>
		</div>
		<div style="margin-left: 1.5em">
			&lt;fileErr<br/>
			<span style="margin-left: 0.5em">
				errNum="
				<span class="type">
					<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
				</span> <span class="occurs">[1]</span>"
			</span><br/><span style="margin-left: 0.5em"> errText="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[1]</span>
		</div>&lt;/sysError&gt;
	</div>&lt;/sysErrors&gt;
</div>&lt;/Cacontracts&gt;
</div>
<div style="margin-left: 1.5em">
	&lt;sysErrors<br/>
	<span style="margin-left: 0.5em">
		numErrs="
		<span class="type">
			<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/>
	<span style="margin-left: 0.5em">
		errNum="
		<span class="type">
			<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
		</span> <span class="occurs">[1]</span>"
	</span><br/><span style="margin-left: 0.5em"> errTitle="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> errText="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span>&gt; <span class="occurs">[0..1]</span> <br/>
	<div style="margin-left: 1.5em">
		&lt;sysError<br/>
		<span style="margin-left: 0.5em">
			seq="
			<span class="type">
				<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
			</span> <span class="occurs">[1]</span>"
		</span><br/>
		<span style="margin-left: 0.5em">
			errNum="
			<span class="type">
				<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
			</span> <span class="occurs">[1]</span>"
		</span><br/><span style="margin-left: 0.5em"> errTitle="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> errText="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> dateTime="<span class="type">anySimpleType</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> routine="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span><br/><span style="margin-left: 0.5em"> line="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:integer</span> <span class="occurs">[1]</span>"</span>&gt; <span class="occurs">[1..*]</span> <br/>
		<div style="margin-left: 1.5em">
			&lt;synergy<br/>
			<span style="margin-left: 0.5em">
				errNum="
				<span class="type">
					<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
				</span> <span class="occurs">[1]</span>"
			</span><br/><span style="margin-left: 0.5em"> errText="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[1]</span>
		</div>
		<div style="margin-left: 1.5em">
			&lt;fileErr<br/>
			<span style="margin-left: 0.5em">
				errNum="
				<span class="type">
					<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
				</span> <span class="occurs">[1]</span>"
			</span><br/><span style="margin-left: 0.5em"> errText="<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span> <span class="occurs">[1]</span>"</span>/&gt; <span class="occurs">[1]</span>
		</div>&lt;/sysError&gt;
	</div>&lt;/sysErrors&gt;
</div>&lt;/WSCACONTRACTSResponse&gt;
</div>
</div>
<script type="text/javascript">
	<!--
	setState('element_WSCACONTRACTSResponse_xibox', true);
// -->
</script>
</div>
<div class="schemaComponent box">
<div>
	<input type="button" id="element_WSCACONTRACTSResponse_scbox_button" class="control" onclick="switchState('element_WSCACONTRACTSResponse_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
</div>
<div id="element_WSCACONTRACTSResponse_scbox" class="contents">
<div style="margin-left: 0em">
&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">WSCACONTRACTSResponse</span>"&gt;
<div style="margin-left: 1.5em">
&lt;<span class="scTag">xsd:complexType</span>&gt;
<div style="margin-left: 1.5em">
&lt;<span class="scTag">xsd:sequence</span>&gt;
<div class="comment" style="margin-left: 1.5em">&lt;-- Cacontracts --&gt;</div>
<div style="margin-left: 1.5em">
&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">Cacontracts</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">unbounded</span>"&gt;
<div style="margin-left: 1.5em">
&lt;<span class="scTag">xsd:complexType</span>&gt;
<div style="margin-left: 1.5em">
&lt;<span class="scTag">xsd:sequence</span>&gt;
<div class="comment" style="margin-left: 1.5em">&lt;-- Cacontract --&gt;</div>
<div style="margin-left: 1.5em">
&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">Cacontract</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">unbounded</span>"&gt;
<div style="margin-left: 1.5em">
&lt;<span class="scTag">xsd:complexType</span>&gt;
<div style="margin-left: 1.5em">
&lt;<span class="scTag">xsd:sequence</span>&gt;
<div class="comment" style="margin-left: 1.5em">&lt;-- Contract --&gt;</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Type --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">type</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;contractType&quot; type definition." href="#type_contractType">contractType</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Customer --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">customer</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;customer&quot; type definition." href="#type_customer">customer</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">name</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;str30&quot; type definition." href="#type_str30">str30</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Commodity Code --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">commodity</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;commodity&quot; type definition." href="#type_commodity">commodity</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">description</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;str20&quot; type definition." href="#type_str20">str20</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Branch --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">branch</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;branch&quot; type definition." href="#type_branch">branch</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">name</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;str20&quot; type definition." href="#type_str20">str20</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Position --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">position</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;position&quot; type definition." href="#type_position">position</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">description</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;str25&quot; type definition." href="#type_str25">str25</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">subContract</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;trueFalse&quot; type definition." href="#type_trueFalse">trueFalse</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">moveLoads</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;trueFalse&quot; type definition." href="#type_trueFalse">trueFalse</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">autoClose</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;trueFalse&quot; type definition." href="#type_trueFalse">trueFalse</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">copyRemarks</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;trueFalse&quot; type definition." href="#type_trueFalse">trueFalse</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Their Contract --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">theirContract</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;str10&quot; type definition." href="#type_str10">str10</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Status --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">status</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;status&quot; type definition." href="#type_status">status</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Issue Date --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">issueDate</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:date</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Delivery Start --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">deliveryStart</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:date</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Delivery End --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">deliveryEnd</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:date</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Due Date --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">dueDate</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:date</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Interest Start Date --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">interestStartDate</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:date</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Interest Rate --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">interestRate</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;d6_4&quot; type definition." href="#type_d6_4">d6_4</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Board Price --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">priceBoard</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;d8_4&quot; type definition." href="#type_d8_4">d8_4</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Basis --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">priceBasis</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;n8_4&quot; type definition." href="#type_n8_4">n8_4</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Freight --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">priceFreight</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;n8_4&quot; type definition." href="#type_n8_4">n8_4</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Fee --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">priceFee</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;n8_4&quot; type definition." href="#type_n8_4">n8_4</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Total Basis --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">priceBasisTotal</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;n8_4&quot; type definition." href="#type_n8_4">n8_4</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Price --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">price</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;d8_4&quot; type definition." href="#type_d8_4">d8_4</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Futures Month --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">futuresMonth</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:gYearMonth</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Broker --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">broker</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;d5&quot; type definition." href="#type_d5">d5</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Broker Account --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">brokerAccount</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;d10&quot; type definition." href="#type_d10">d10</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Freight --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">freight</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;YesNo&quot; type definition." href="#type_YesNo">YesNo</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Signed --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">signed</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;YesNo&quot; type definition." href="#type_YesNo">YesNo</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Printed --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">printed</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;YesNo&quot; type definition." href="#type_YesNo">YesNo</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Profit --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">profit</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;YesNo&quot; type definition." href="#type_YesNo">YesNo</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Grade --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">grade</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;str6&quot; type definition." href="#type_str6">str6</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Offer Expiration --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">offerExpiration</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:date</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Offer To Position --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">offerToPosition</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;position&quot; type definition." href="#type_position">position</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Issue LBS --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">issued</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">pounds</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;n8&quot; type definition." href="#type_n8">n8</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">bushels</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;n8_2&quot; type definition." href="#type_n8_2">n8_2</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Applied LBS --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">applied</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">pounds</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;n8&quot; type definition." href="#type_n8">n8</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">bushels</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;n8_2&quot; type definition." href="#type_n8_2">n8_2</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Adjusted LBS --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">adjusted</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">pounds</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;n8&quot; type definition." href="#type_n8">n8</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">bushels</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;n8_2&quot; type definition." href="#type_n8_2">n8_2</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Balance LBS --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">balance</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">pounds</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;n8&quot; type definition." href="#type_n8">n8</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">bushels</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;n8_2&quot; type definition." href="#type_n8_2">n8_2</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Remarks --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">remarks</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:sequence</span>&gt;
			<div class="comment" style="margin-left: 1.5em">&lt;-- Remark --&gt;</div>
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">remark</span>" <span class="scTag">minOccurs</span>="<span class="scContent">1</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">10</span>"&gt;
				<div style="margin-left: 1.5em">
					&lt;<span class="scTag">xsd:complexType</span>&gt;
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">value</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type">
								<a title="Jump to &quot;str50&quot; type definition." href="#type_str50">str50</a>
							</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
				</div>&lt;/<span class="scTag">xsd:element</span>&gt;
			</div>&lt;/<span class="scTag">xsd:sequence</span>&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Errors --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">Errors</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:sequence</span>&gt;
			<div class="comment" style="margin-left: 1.5em">&lt;-- Error --&gt;</div>
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">Error</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">unbounded</span>"&gt;
				<div style="margin-left: 1.5em">
					&lt;<span class="scTag">xsd:complexType</span>&gt;
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">description</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
				</div>&lt;/<span class="scTag">xsd:element</span>&gt;
			</div>&lt;/<span class="scTag">xsd:sequence</span>&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- sysErrors --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">sysErrors</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:sequence</span>&gt;
			<div class="comment" style="margin-left: 1.5em">&lt;-- sysError --&gt;</div>
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">sysError</span>" <span class="scTag">minOccurs</span>="<span class="scContent">1</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">unbounded</span>"&gt;
				<div style="margin-left: 1.5em">
					&lt;<span class="scTag">xsd:complexType</span>&gt;
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:sequence</span>&gt;
						<div class="comment" style="margin-left: 1.5em">&lt;-- synergy --&gt;</div>
						<div style="margin-left: 1.5em">
							&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">synergy</span>" <span class="scTag">minOccurs</span>="<span class="scContent">1</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
							<div style="margin-left: 1.5em">
								&lt;<span class="scTag">xsd:complexType</span>&gt;
								<div style="margin-left: 1.5em">
									&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errNum</span>" <span class="scTag">type</span>="
									<span class="scContent">
										<span class="type">
											<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
										</span>
									</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
								</div>
								<div style="margin-left: 1.5em">
									&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errText</span>" <span class="scTag">type</span>="
									<span class="scContent">
										<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
									</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
								</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
							</div>&lt;/<span class="scTag">xsd:element</span>&gt;
						</div>
						<div class="comment" style="margin-left: 1.5em">&lt;-- fileErr --&gt;</div>
						<div style="margin-left: 1.5em">
							&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">fileErr</span>" <span class="scTag">minOccurs</span>="<span class="scContent">1</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
							<div style="margin-left: 1.5em">
								&lt;<span class="scTag">xsd:complexType</span>&gt;
								<div style="margin-left: 1.5em">
									&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errNum</span>" <span class="scTag">type</span>="
									<span class="scContent">
										<span class="type">
											<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
										</span>
									</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
								</div>
								<div style="margin-left: 1.5em">
									&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errText</span>" <span class="scTag">type</span>="
									<span class="scContent">
										<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
									</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
								</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
							</div>&lt;/<span class="scTag">xsd:element</span>&gt;
						</div>&lt;/<span class="scTag">xsd:sequence</span>&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">seq</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type">
								<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
							</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errNum</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type">
								<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
							</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errTitle</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errText</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">dateTime</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">routine</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">line</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:integer</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
				</div>&lt;/<span class="scTag">xsd:element</span>&gt;
			</div>&lt;/<span class="scTag">xsd:sequence</span>&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">numErrs</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errNum</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errTitle</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errText</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>&lt;/<span class="scTag">xsd:sequence</span>&gt;
</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">reference</span>" <span class="scTag">type</span>="
	<span class="scContent">
		<span class="type">
			<a title="Jump to &quot;str30&quot; type definition." href="#type_str30">str30</a>
		</span>
	</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">number</span>" <span class="scTag">type</span>="
	<span class="scContent">
		<span class="type">
			<a title="Jump to &quot;contract&quot; type definition." href="#type_contract">contract</a>
		</span>
	</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">returnCode</span>" <span class="scTag">type</span>="
	<span class="scContent">
		<span class="type">
			<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
		</span>
	</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- Errors --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">Errors</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:sequence</span>&gt;
			<div class="comment" style="margin-left: 1.5em">&lt;-- Error --&gt;</div>
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">Error</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">unbounded</span>"&gt;
				<div style="margin-left: 1.5em">
					&lt;<span class="scTag">xsd:complexType</span>&gt;
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">description</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
				</div>&lt;/<span class="scTag">xsd:element</span>&gt;
			</div>&lt;/<span class="scTag">xsd:sequence</span>&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- sysErrors --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">sysErrors</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:sequence</span>&gt;
			<div class="comment" style="margin-left: 1.5em">&lt;-- sysError --&gt;</div>
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">sysError</span>" <span class="scTag">minOccurs</span>="<span class="scContent">1</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">unbounded</span>"&gt;
				<div style="margin-left: 1.5em">
					&lt;<span class="scTag">xsd:complexType</span>&gt;
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:sequence</span>&gt;
						<div class="comment" style="margin-left: 1.5em">&lt;-- synergy --&gt;</div>
						<div style="margin-left: 1.5em">
							&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">synergy</span>" <span class="scTag">minOccurs</span>="<span class="scContent">1</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
							<div style="margin-left: 1.5em">
								&lt;<span class="scTag">xsd:complexType</span>&gt;
								<div style="margin-left: 1.5em">
									&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errNum</span>" <span class="scTag">type</span>="
									<span class="scContent">
										<span class="type">
											<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
										</span>
									</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
								</div>
								<div style="margin-left: 1.5em">
									&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errText</span>" <span class="scTag">type</span>="
									<span class="scContent">
										<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
									</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
								</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
							</div>&lt;/<span class="scTag">xsd:element</span>&gt;
						</div>
						<div class="comment" style="margin-left: 1.5em">&lt;-- fileErr --&gt;</div>
						<div style="margin-left: 1.5em">
							&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">fileErr</span>" <span class="scTag">minOccurs</span>="<span class="scContent">1</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
							<div style="margin-left: 1.5em">
								&lt;<span class="scTag">xsd:complexType</span>&gt;
								<div style="margin-left: 1.5em">
									&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errNum</span>" <span class="scTag">type</span>="
									<span class="scContent">
										<span class="type">
											<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
										</span>
									</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
								</div>
								<div style="margin-left: 1.5em">
									&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errText</span>" <span class="scTag">type</span>="
									<span class="scContent">
										<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
									</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
								</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
							</div>&lt;/<span class="scTag">xsd:element</span>&gt;
						</div>&lt;/<span class="scTag">xsd:sequence</span>&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">seq</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type">
								<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
							</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errNum</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type">
								<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
							</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errTitle</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errText</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">dateTime</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">routine</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">line</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:integer</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
				</div>&lt;/<span class="scTag">xsd:element</span>&gt;
			</div>&lt;/<span class="scTag">xsd:sequence</span>&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">numErrs</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errNum</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errTitle</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errText</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>&lt;/<span class="scTag">xsd:sequence</span>&gt;
</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">control</span>" <span class="scTag">type</span>="
	<span class="scContent">
		<span class="type">
			<a title="Jump to &quot;control&quot; type definition." href="#type_control">control</a>
		</span>
	</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">function</span>" <span class="scTag">type</span>="
	<span class="scContent">
		<span class="type">
			<a title="Jump to &quot;function&quot; type definition." href="#type_function">function</a>
		</span>
	</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">customer</span>" <span class="scTag">type</span>="
	<span class="scContent">
		<span class="type">
			<a title="Jump to &quot;customer&quot; type definition." href="#type_customer">customer</a>
		</span>
	</span>"/&gt;
</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">commodity</span>" <span class="scTag">type</span>="
	<span class="scContent">
		<span class="type">
			<a title="Jump to &quot;commodity&quot; type definition." href="#type_commodity">commodity</a>
		</span>
	</span>"/&gt;
</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
<div class="comment" style="margin-left: 1.5em">&lt;-- sysErrors --&gt;</div>
<div style="margin-left: 1.5em">
	&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">sysErrors</span>" <span class="scTag">minOccurs</span>="<span class="scContent">0</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
	<div style="margin-left: 1.5em">
		&lt;<span class="scTag">xsd:complexType</span>&gt;
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:sequence</span>&gt;
			<div class="comment" style="margin-left: 1.5em">&lt;-- sysError --&gt;</div>
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">sysError</span>" <span class="scTag">minOccurs</span>="<span class="scContent">1</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">unbounded</span>"&gt;
				<div style="margin-left: 1.5em">
					&lt;<span class="scTag">xsd:complexType</span>&gt;
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:sequence</span>&gt;
						<div class="comment" style="margin-left: 1.5em">&lt;-- synergy --&gt;</div>
						<div style="margin-left: 1.5em">
							&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">synergy</span>" <span class="scTag">minOccurs</span>="<span class="scContent">1</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
							<div style="margin-left: 1.5em">
								&lt;<span class="scTag">xsd:complexType</span>&gt;
								<div style="margin-left: 1.5em">
									&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errNum</span>" <span class="scTag">type</span>="
									<span class="scContent">
										<span class="type">
											<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
										</span>
									</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
								</div>
								<div style="margin-left: 1.5em">
									&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errText</span>" <span class="scTag">type</span>="
									<span class="scContent">
										<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
									</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
								</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
							</div>&lt;/<span class="scTag">xsd:element</span>&gt;
						</div>
						<div class="comment" style="margin-left: 1.5em">&lt;-- fileErr --&gt;</div>
						<div style="margin-left: 1.5em">
							&lt;<span class="scTag">xsd:element</span> <span class="scTag">name</span>="<span class="scContent">fileErr</span>" <span class="scTag">minOccurs</span>="<span class="scContent">1</span>" <span class="scTag">maxOccurs</span>="<span class="scContent">1</span>"&gt;
							<div style="margin-left: 1.5em">
								&lt;<span class="scTag">xsd:complexType</span>&gt;
								<div style="margin-left: 1.5em">
									&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errNum</span>" <span class="scTag">type</span>="
									<span class="scContent">
										<span class="type">
											<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
										</span>
									</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
								</div>
								<div style="margin-left: 1.5em">
									&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errText</span>" <span class="scTag">type</span>="
									<span class="scContent">
										<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
									</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
								</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
							</div>&lt;/<span class="scTag">xsd:element</span>&gt;
						</div>&lt;/<span class="scTag">xsd:sequence</span>&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">seq</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type">
								<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
							</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errNum</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type">
								<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
							</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errTitle</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errText</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">dateTime</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">routine</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>
					<div style="margin-left: 1.5em">
						&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">line</span>" <span class="scTag">type</span>="
						<span class="scContent">
							<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:integer</span>
						</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
					</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
				</div>&lt;/<span class="scTag">xsd:element</span>&gt;
			</div>&lt;/<span class="scTag">xsd:sequence</span>&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">numErrs</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errNum</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type">
					<a title="Jump to &quot;number&quot; type definition." href="#type_number">number</a>
				</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errTitle</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>
		<div style="margin-left: 1.5em">
			&lt;<span class="scTag">xsd:attribute</span> <span class="scTag">name</span>="<span class="scContent">errText</span>" <span class="scTag">type</span>="
			<span class="scContent">
				<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:normalizedString</span>
			</span>" <span class="scTag">use</span>="<span class="scContent">required</span>"/&gt;
		</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
	</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>&lt;/<span class="scTag">xsd:sequence</span>&gt;
</div>&lt;/<span class="scTag">xsd:complexType</span>&gt;
</div>&lt;/<span class="scTag">xsd:element</span>&gt;
</div>
</div>
<script type="text/javascript">
	<!--
	setState('element_WSCACONTRACTSResponse_scbox', false);
// -->
</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h2>
	<a name="SchemaDefinitions">Global Definitions</a>
</h2>
<h3>
	Simple Type: <a name="type_branch" class="name">branch</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:integer
			</span> &lt; <strong>branch</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>branch</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: integer</li>
			</ul>
			<ul>
				<li>1 &lt;= <em>value</em> &lt;= 999</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_branch_scbox_button" class="control" onclick="switchState('type_branch_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_branch_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">branch</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:integer</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minInclusive</span> <span class="scTag">value</span>="<span class="scContent">1</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxInclusive</span> <span class="scTag">value</span>="<span class="scContent">999</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_branch_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_commodity" class="name">commodity</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>commodity</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>commodity</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>length</em> &gt;= 1
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'collapse' in glossary" href="#term_CollapseWS">collapse</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_commodity_scbox_button" class="control" onclick="switchState('type_commodity_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_commodity_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">commodity</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minLength</span> <span class="scTag">value</span>="<span class="scContent">1</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxLength</span> <span class="scTag">value</span>="<span class="scContent">2</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">collapse</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_commodity_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_contract" class="name">contract</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal
			</span> &lt; <strong>contract</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>contract</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: decimal</li>
			</ul>
			<ul>
				<li>0.00 &lt;= <em>value</em> &lt;= 9999999999.99</li>
				<li>
					<em>no. of fraction digits</em> = 2
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_contract_scbox_button" class="control" onclick="switchState('type_contract_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_contract_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">contract</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:fractionDigits</span> <span class="scTag">value</span>="<span class="scContent">2</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minInclusive</span> <span class="scTag">value</span>="<span class="scContent">0.00</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxInclusive</span> <span class="scTag">value</span>="<span class="scContent">9999999999.99</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_contract_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_contractType" class="name">contractType</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>contractType</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>contractType</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>value</em> comes from list: {'Purchase'|'Sales'}
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'collapse' in glossary" href="#term_CollapseWS">collapse</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_contractType_scbox_button" class="control" onclick="switchState('type_contractType_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_contractType_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">contractType</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:enumeration</span> <span class="scTag">value</span>="<span class="scContent">Purchase</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:enumeration</span> <span class="scTag">value</span>="<span class="scContent">Sales</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">collapse</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_contractType_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_control" class="name">control</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>control</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>control</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>length</em> &gt;= 1
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'collapse' in glossary" href="#term_CollapseWS">collapse</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_control_scbox_button" class="control" onclick="switchState('type_control_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_control_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">control</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minLength</span> <span class="scTag">value</span>="<span class="scContent">1</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxLength</span> <span class="scTag">value</span>="<span class="scContent">20</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">collapse</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_control_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_customer" class="name">customer</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>customer</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>customer</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>length</em> &gt;= 1
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'collapse' in glossary" href="#term_CollapseWS">collapse</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_customer_scbox_button" class="control" onclick="switchState('type_customer_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_customer_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">customer</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minLength</span> <span class="scTag">value</span>="<span class="scContent">1</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxLength</span> <span class="scTag">value</span>="<span class="scContent">7</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">collapse</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_customer_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_d10" class="name">d10</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal
			</span> &lt; <strong>d10</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>d10</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: decimal</li>
			</ul>
			<ul>
				<li>0 &lt;= <em>value</em> &lt;= 9999999999</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_d10_scbox_button" class="control" onclick="switchState('type_d10_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_d10_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">d10</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minInclusive</span> <span class="scTag">value</span>="<span class="scContent">0</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxInclusive</span> <span class="scTag">value</span>="<span class="scContent">9999999999</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_d10_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_d5" class="name">d5</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal
			</span> &lt; <strong>d5</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>d5</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: decimal</li>
			</ul>
			<ul>
				<li>0 &lt;= <em>value</em> &lt;= 99999</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_d5_scbox_button" class="control" onclick="switchState('type_d5_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_d5_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">d5</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minInclusive</span> <span class="scTag">value</span>="<span class="scContent">0</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxInclusive</span> <span class="scTag">value</span>="<span class="scContent">99999</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_d5_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_d6_4" class="name">d6_4</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal
			</span> &lt; <strong>d6_4</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>d6_4</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: decimal</li>
			</ul>
			<ul>
				<li>0.0000 &lt;= <em>value</em> &lt;= 99.9999</li>
				<li>
					<em>no. of fraction digits</em> = 4
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_d6_4_scbox_button" class="control" onclick="switchState('type_d6_4_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_d6_4_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">d6_4</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:fractionDigits</span> <span class="scTag">value</span>="<span class="scContent">4</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minInclusive</span> <span class="scTag">value</span>="<span class="scContent">0.0000</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxInclusive</span> <span class="scTag">value</span>="<span class="scContent">99.9999</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_d6_4_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_d8_4" class="name">d8_4</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal
			</span> &lt; <strong>d8_4</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>d8_4</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: decimal</li>
			</ul>
			<ul>
				<li>0.0000 &lt;= <em>value</em> &lt;= 9999.9999</li>
				<li>
					<em>no. of fraction digits</em> = 4
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_d8_4_scbox_button" class="control" onclick="switchState('type_d8_4_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_d8_4_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">d8_4</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:fractionDigits</span> <span class="scTag">value</span>="<span class="scContent">4</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minInclusive</span> <span class="scTag">value</span>="<span class="scContent">0.0000</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxInclusive</span> <span class="scTag">value</span>="<span class="scContent">9999.9999</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_d8_4_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_function" class="name">function</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>function</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>function</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>value</em> comes from list: {'Get'|'Add'|'Adjust'}
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'collapse' in glossary" href="#term_CollapseWS">collapse</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_function_scbox_button" class="control" onclick="switchState('type_function_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_function_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">function</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:enumeration</span> <span class="scTag">value</span>="<span class="scContent">Get</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:enumeration</span> <span class="scTag">value</span>="<span class="scContent">Add</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:enumeration</span> <span class="scTag">value</span>="<span class="scContent">Adjust</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">collapse</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_function_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_n8" class="name">n8</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal
			</span> &lt; <strong>n8</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>n8</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: decimal</li>
			</ul>
			<ul>
				<li>-99999999 &lt;= <em>value</em> &lt;= 99999999</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_n8_scbox_button" class="control" onclick="switchState('type_n8_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_n8_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">n8</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minInclusive</span> <span class="scTag">value</span>="<span class="scContent">-99999999</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxInclusive</span> <span class="scTag">value</span>="<span class="scContent">99999999</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_n8_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_n8_2" class="name">n8_2</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal
			</span> &lt; <strong>n8_2</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>n8_2</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: decimal</li>
			</ul>
			<ul>
				<li>
					<em>no. of fraction digits</em> = 2
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_n8_2_scbox_button" class="control" onclick="switchState('type_n8_2_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_n8_2_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">n8_2</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:fractionDigits</span> <span class="scTag">value</span>="<span class="scContent">2</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_n8_2_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_n8_4" class="name">n8_4</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal
			</span> &lt; <strong>n8_4</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>n8_4</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: decimal</li>
			</ul>
			<ul>
				<li>
					<em>no. of fraction digits</em> = 4
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_n8_4_scbox_button" class="control" onclick="switchState('type_n8_4_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_n8_4_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">n8_4</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:decimal</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:fractionDigits</span> <span class="scTag">value</span>="<span class="scContent">4</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_n8_4_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_number" class="name">number</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:integer
			</span> &lt; <strong>number</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>number</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: integer</li>
			</ul>
			<ul>
				<li>0 &lt;= <em>value</em> &lt;= 999999</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_number_scbox_button" class="control" onclick="switchState('type_number_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_number_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">number</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:integer</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minInclusive</span> <span class="scTag">value</span>="<span class="scContent">0</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxInclusive</span> <span class="scTag">value</span>="<span class="scContent">999999</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_number_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_position" class="name">position</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:integer
			</span> &lt; <strong>position</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>position</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: integer</li>
			</ul>
			<ul>
				<li>1 &lt;= <em>value</em> &lt;= 9999</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_position_scbox_button" class="control" onclick="switchState('type_position_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_position_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">position</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:integer</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minInclusive</span> <span class="scTag">value</span>="<span class="scContent">1</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxInclusive</span> <span class="scTag">value</span>="<span class="scContent">9999</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_position_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_status" class="name">status</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>status</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>status</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>value</em> comes from list: {'Open'|'Closed'}
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'collapse' in glossary" href="#term_CollapseWS">collapse</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_status_scbox_button" class="control" onclick="switchState('type_status_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_status_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">status</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:enumeration</span> <span class="scTag">value</span>="<span class="scContent">Open</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:enumeration</span> <span class="scTag">value</span>="<span class="scContent">Closed</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">collapse</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_status_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_str10" class="name">str10</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>str10</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>str10</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>length</em> &gt;= 0
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'replace' in glossary" href="#term_ReplaceWS">replace</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_str10_scbox_button" class="control" onclick="switchState('type_str10_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_str10_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">str10</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minLength</span> <span class="scTag">value</span>="<span class="scContent">0</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxLength</span> <span class="scTag">value</span>="<span class="scContent">10</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">replace</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_str10_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_str20" class="name">str20</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>str20</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>str20</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>length</em> &gt;= 1
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'replace' in glossary" href="#term_ReplaceWS">replace</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_str20_scbox_button" class="control" onclick="switchState('type_str20_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_str20_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">str20</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minLength</span> <span class="scTag">value</span>="<span class="scContent">1</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxLength</span> <span class="scTag">value</span>="<span class="scContent">20</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">replace</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_str20_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_str25" class="name">str25</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>str25</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>str25</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>length</em> &gt;= 1
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'replace' in glossary" href="#term_ReplaceWS">replace</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_str25_scbox_button" class="control" onclick="switchState('type_str25_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_str25_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">str25</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minLength</span> <span class="scTag">value</span>="<span class="scContent">1</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxLength</span> <span class="scTag">value</span>="<span class="scContent">25</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">replace</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_str25_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_str30" class="name">str30</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>str30</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>str30</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>length</em> &gt;= 1
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'replace' in glossary" href="#term_ReplaceWS">replace</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_str30_scbox_button" class="control" onclick="switchState('type_str30_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_str30_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">str30</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minLength</span> <span class="scTag">value</span>="<span class="scContent">1</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxLength</span> <span class="scTag">value</span>="<span class="scContent">30</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">replace</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_str30_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_str50" class="name">str50</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>str50</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>str50</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>length</em> &gt;= 0
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'replace' in glossary" href="#term_ReplaceWS">replace</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_str50_scbox_button" class="control" onclick="switchState('type_str50_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_str50_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">str50</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minLength</span> <span class="scTag">value</span>="<span class="scContent">0</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxLength</span> <span class="scTag">value</span>="<span class="scContent">50</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">replace</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_str50_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_str6" class="name">str6</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>str6</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>str6</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>length</em> &gt;= 0
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'replace' in glossary" href="#term_ReplaceWS">replace</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_str6_scbox_button" class="control" onclick="switchState('type_str6_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_str6_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">str6</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:minLength</span> <span class="scTag">value</span>="<span class="scContent">0</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:maxLength</span> <span class="scTag">value</span>="<span class="scContent">6</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">replace</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_str6_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_trueFalse" class="name">trueFalse</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>trueFalse</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>trueFalse</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>value</em> comes from list: {'false'|'true'}
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'collapse' in glossary" href="#term_CollapseWS">collapse</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_trueFalse_scbox_button" class="control" onclick="switchState('type_trueFalse_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_trueFalse_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">trueFalse</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:enumeration</span> <span class="scTag">value</span>="<span class="scContent">false</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:enumeration</span> <span class="scTag">value</span>="<span class="scContent">true</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">collapse</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_trueFalse_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<h3>
	Simple Type: <a name="type_YesNo" class="name">YesNo</a>
</h3>
<table class="hierarchy">
	<tr>
		<th>Super-types:</th>
		<td>
			<span class="type">
				<a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string
			</span> &lt; <strong>YesNo</strong> (by restriction)
		</td>
	</tr>
	<tr>
		<th>Sub-types:</th>
		<td>None</td>
	</tr>
</table>
<table class="properties">
	<tr>
		<th>Name</th>
		<td>YesNo</td>
	</tr>
	<tr>
		<th>Content</th>
		<td>
			<ul>
				<li>Base XSD Type: string</li>
			</ul>
			<ul>
				<li>
					<em>value</em> comes from list: {'Yes'|'No'}
				</li>
				<li>
					<em>Whitespace policy: </em><a title="Look up 'collapse' in glossary" href="#term_CollapseWS">collapse</a>
				</li>
			</ul>
		</td>
	</tr>
</table>
<div class="schemaComponent box">
	<div>
		<input type="button" id="type_YesNo_scbox_button" class="control" onclick="switchState('type_YesNo_scbox');return false;" style="display: none"/> <span class="caption">Schema Component Representation</span>
	</div>
	<div id="type_YesNo_scbox" class="contents">
		<div style="margin-left: 0em">
			&lt;<span class="scTag">xsd:simpleType</span> <span class="scTag">name</span>="<span class="scContent">YesNo</span>"&gt;
			<div style="margin-left: 1.5em">
				&lt;<span class="scTag">xsd:restriction</span> <span class="scTag">base</span>="
				<span class="scContent">
					<span class="type"><a href="#ns_xsd" title="Find out namespace of 'xsd' prefix">xsd</a>:string</span>
				</span>"&gt;
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:enumeration</span> <span class="scTag">value</span>="<span class="scContent">Yes</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:enumeration</span> <span class="scTag">value</span>="<span class="scContent">No</span>"/&gt;</div>
				<div style="margin-left: 1.5em">&lt;<span class="scTag">xsd:whiteSpace</span> <span class="scTag">value</span>="<span class="scContent">collapse</span>"/&gt;</div>&lt;/<span class="scTag">xsd:restriction</span>&gt;
			</div>&lt;/<span class="scTag">xsd:simpleType</span>&gt;
		</div>
	</div>
	<script type="text/javascript">
		<!--
		setState('type_YesNo_scbox', false);
// -->
	</script>
</div>
<div style="text-align: right; clear: both;">
	<a href="#top">top</a>
</div>
<hr/>
<div id="legend">
	<h2>
		<a name="Legend">Legend</a>
	</h2>
	<div style="float: left; width: 15em;">
		<h3 style="margin-bottom: 0px;">Complex Type:</h3>
		<div class="hint" style="margin-left: 0em;">Schema Component Type</div>
	</div>
	<div style="float: left; width: 15em;">
		<h3 style="margin-bottom: 0px;">
			<span class="name">AusAddress</span>
		</h3>
		<div class="hint" style="margin-left: 0em;">Schema Component Name</div>
	</div>
	<table class="hierarchy" style="clear: both">
		<tr>
			<th>Super-types:</th>
			<td>
				<span class="type" style="color: #0000FF; text-decoration: underline;">Address</span> &lt; <span class="current">AusAddress</span> (by extension)
			</td>
		</tr>
		<tr>
			<th>Sub-types:</th>
			<td>
				<ul>
					<li>
						<span class="type" style="color: #0000FF; text-decoration: underline;">QLDAddress</span> (by restriction)
					</li>
				</ul>
			</td>
		</tr>
	</table>
	<div class="hint">If this schema component is a type definition, its type hierarchy is shown in a gray-bordered box.</div>
	<table class="properties">
		<tr>
			<th>Name</th>
			<td>AusAddress</td>
		</tr>
		<tr>
			<th>
				<a title="Look up 'Abstract' in glossary" href="#term_Abstract">Abstract</a>
			</th>
			<td>no</td>
		</tr>
	</table>
	<div class="hint">The table above displays the properties of this schema component.</div>
	<div class="sample box">
		<div>
			<span class="caption">XML Instance Representation</span>
		</div>
		<div class="contents">
			<span style="margin-left: 0em">&lt;...</span>
			<span class="newFields">
				<span> country="<span class="fixed">Australia</span>"</span>
			</span>&gt; <br/>
			<span style="margin-left: 1.5em" class="inherited">&lt;unitNo&gt; <span class="type">string</span> &lt;/unitNo&gt; <span class="occurs">[0..1]</span></span><br/>
			<span style="margin-left: 1.5em" class="inherited">&lt;houseNo&gt; <span class="type">string</span> &lt;/houseNo&gt; <span class="occurs">[1]</span></span><br/>
			<span style="margin-left: 1.5em" class="inherited">&lt;street&gt; <span class="type">string</span> &lt;/street&gt; <span class="occurs">[1]</span></span><br/>
			<span class="group" style="margin-left: 1.5em">
				Start <a title="Look up 'Choice' in glossary" href="#term_Choice">Choice</a>
				<span class="occurs">[1]</span>
			</span><br/>
			<span style="margin-left: 3em" class="inherited">&lt;city&gt; <span class="type">string</span> &lt;/city&gt; <span class="occurs">[1]</span></span><br/>
			<span style="margin-left: 3em" class="inherited">&lt;town&gt; <span class="type">string</span> &lt;/town&gt; <span class="occurs">[1]</span></span><br/>
			<span class="group" style="margin-left: 1.5em">End Choice</span><br/>
			<span class="newFields">
				<span style="margin-left: 1.5em">&lt;state&gt; <span class="type" style="text-decoration: underline;">AusStates</span> &lt;/state&gt; <span class="occurs">[1]</span></span><br/>
				<span style="margin-left: 1.5em">
					&lt;postcode&gt; <span class="constraint">string &lt;&lt;<em>pattern</em> = [1-9][0-9]{3}&gt;&gt;</span> &lt;/postcode&gt; <span class="occurs">[1]</span>
					<a href="javascript:void(0)" title="View Documentation" class="documentation" onclick="docArray = new Array('Post code must be a four-digit number.');viewDocumentation('Element', 'postcode', docArray);">?</a>
				</span><br/>
			</span>
			<span style="margin-left: 0em">&lt;/...&gt;</span><br/>
		</div>
	</div>
	<div class="hint">
		<p>The XML Instance Representation table above shows the schema component's content as an XML instance.</p>
		<ul>
			<li>The minimum and maximum occurrence of elements and attributes are provided in square brackets, e.g. [0..1].</li>
			<li>Model group information are shown in gray, e.g. Start Choice ... End Choice.</li>
			<li>For type derivations, the elements and attributes that have been added to or changed from the base type's content are shown in <span style="font-weight: bold">bold</span>.</li>
			<li>If an element/attribute has a fixed value, the fixed value is shown in green, e.g. country="Australia".</li>
			<li>
				Otherwise, the type of the element/attribute is displayed.
				<ul>
					<li>If the element/attribute's type is in the schema, a link is provided to it.</li>
					<li>For local simple type definitions, the constraints are displayed in angle brackets, e.g. &lt;&lt;<em>pattern</em> = [1-9][0-9]{3}&gt;&gt;.</li>
				</ul>
			</li>
			<li>If a local element/attribute has documentation, it will be displayed in a window that pops up when the question mark inside the attribute or next to the element is clicked, e.g. &lt;postcode&gt;.</li>
		</ul>
	</div>
	<div class="schemaComponent box">
		<div>
			<span class="caption">Schema Component Representation</span>
		</div>
		<div class="contents">
			<span style="margin-left: 0em">
				&lt;<span class="scTag">complexType</span>
				<span class="scTag">name</span>="<span class="scContent">AusAddress</span>"&gt;
			</span><br/>
			<span style="margin-left: 1.5em">&lt;<span class="scTag">complexContent</span>&gt;</span><br/>
			<span style="margin-left: 3em">
				&lt;<span class="scTag">extension</span>
				<span class="scTag">base</span>="
				<span class="scContent">
					<span class="type" style="text-decoration: underline;">Address</span>
				</span>"&gt;
			</span><br/>
			<span style="margin-left: 4.5em">&lt;<span class="scTag">sequence</span>&gt;</span><br/>
			<span style="margin-left: 6em">
				&lt;<span class="scTag">element</span>
				<span class="scTag">name</span>="<span class="scContent">state</span>" <span class="scTag">type</span>="
				<span class="scContent">
					<span class="type" style="text-decoration: underline;">AusStates</span>
				</span>"/&gt;
			</span><br/>
			<span style="margin-left: 6em">
				&lt;<span class="scTag">element</span>
				<span class="scTag">name</span>="<span class="scContent">postcode</span>"&gt;
			</span><br/>
			<span style="margin-left: 7.5em">&lt;<span class="scTag">simpleType</span>&gt;</span><br/>
			<span style="margin-left: 9em">
				&lt;<span class="scTag">restriction</span>
				<span class="scTag">base</span>="
				<span class="scContent">
					<span class="type">string</span>
				</span>"&gt;
			</span><br/>
			<span style="margin-left: 10.5em">
				&lt;<span class="scTag">pattern</span>
				<span class="scTag">value</span>="<span class="scContent">[1-9][0-9]{3}</span>"/&gt;
			</span><br/>
			<span style="margin-left: 9em">&lt;/<span class="scTag">restriction</span>&gt;</span><br/>
			<span style="margin-left: 7.5em">&lt;/<span class="scTag">simpleType</span>&gt;</span><br/>
			<span style="margin-left: 6em">&lt;/<span class="scTag">element</span>&gt;</span><br/>
			<span style="margin-left: 4.5em">&lt;/<span class="scTag">sequence</span>&gt;</span><br/>
			<span style="margin-left: 4.5em">
				&lt;<span class="scTag">attribute</span>
				<span class="scTag">name</span>="<span class="scContent">country</span>" <span class="scTag">type</span>="
				<span class="scContent">
					<span class="type">string</span>
				</span>" <span class="scTag">fixed</span>="<span class="scContent">Australia</span>"/&gt;
			</span><br/>
			<span style="margin-left: 3em">&lt;/<span class="scTag">extension</span>&gt;</span><br/>
			<span style="margin-left: 1.5em">&lt;/<span class="scTag">complexContent</span>&gt;</span><br/>
			<span style="margin-left: 0em">&lt;/<span class="scTag">complexType</span>&gt;</span><br/>
		</div>
	</div>
	<div class="hint">The Schema Component Representation table above displays the underlying XML representation of the schema component. (Annotations are not shown.)</div>
	<div style="text-align: right; clear: both;">
		<a href="#top">top</a>
	</div>
	<hr/>
</div>
<div id="glossary">
	<h2>
		<a name="Glossary">Glossary</a>
	</h2>
	<p>
		<span class="glossaryTerm">
			<a name="term_Abstract">Abstract</a>
		</span>(Applies to complex type definitions and element declarations). An abstract element or complex type cannot used to validate an element instance. If there is a reference to an abstract element, only element declarations that can substitute the abstract element can be used to validate the instance. For references to abstract type definitions, only derived types can be used.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_All">All Model Group</a>
		</span>Child elements can be provided <em>in any order</em> in instances. See: <a title="http://www.w3.org/TR/xmlschema-1/#element-all" href="http://www.w3.org/TR/xmlschema-1/#element-all">http://www.w3.org/TR/xmlschema-1/#element-all</a>.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_Choice">Choice Model Group</a>
		</span>
		<em>Only one</em> from the list of child elements and model groups can be provided in instances. See: <a title="http://www.w3.org/TR/xmlschema-1/#element-choice" href="http://www.w3.org/TR/xmlschema-1/#element-choice">http://www.w3.org/TR/xmlschema-1/#element-choice</a>.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_CollapseWS">Collapse Whitespace Policy</a>
		</span>Replace tab, line feed, and carriage return characters with space character (Unicode character 32). Then, collapse contiguous sequences of space characters into single space character, and remove leading and trailing space characters.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_ElemBlock">Disallowed Substitutions</a>
		</span>(Applies to element declarations). If <em>substitution</em> is specified, then <a title="Look up 'substitution group' in glossary" href="#term_SubGroup">substitution group</a> members cannot be used in place of the given element declaration to validate element instances. If <em>derivation methods</em>, e.g. extension, restriction, are specified, then the given element declaration will not validate element instances that have types derived from the element declaration's type using the specified derivation methods. Normally, element instances can override their declaration's type by specifying an <code>xsi:type</code> attribute.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_Key">Key Constraint</a>
		</span>Like <a title="Look up 'Uniqueness Constraint' in glossary" href="#term_Unique">Uniqueness Constraint</a>, but additionally requires that the specified value(s) must be provided. See: <a title="http://www.w3.org/TR/xmlschema-1/#cIdentity-constraint_Definitions" href="http://www.w3.org/TR/xmlschema-1/#cIdentity-constraint_Definitions">http://www.w3.org/TR/xmlschema-1/#cIdentity-constraint_Definitions</a>.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_KeyRef">Key Reference Constraint</a>
		</span>Ensures that the specified value(s) must match value(s) from a <a title="Look up 'Key Constraint' in glossary" href="#term_Key">Key Constraint</a> or <a title="Look up 'Uniqueness Constraint' in glossary" href="#term_Unique">Uniqueness Constraint</a>. See: <a title="http://www.w3.org/TR/xmlschema-1/#cIdentity-constraint_Definitions" href="http://www.w3.org/TR/xmlschema-1/#cIdentity-constraint_Definitions">http://www.w3.org/TR/xmlschema-1/#cIdentity-constraint_Definitions</a>.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_ModelGroup">Model Group</a>
		</span>Groups together element content, specifying the order in which the element content can occur and the number of times the group of element content may be repeated. See: <a title="http://www.w3.org/TR/xmlschema-1/#Model_Groups" href="http://www.w3.org/TR/xmlschema-1/#Model_Groups">http://www.w3.org/TR/xmlschema-1/#Model_Groups</a>.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_Nillable">Nillable</a>
		</span>(Applies to element declarations). If an element declaration is nillable, instances can use the <code>xsi:nil</code> attribute. The <code>xsi:nil</code> attribute is the boolean attribute, <em>nil</em>, from the <em>http://www.w3.org/2001/XMLSchema-instance</em> namespace. If an element instance has an <code>xsi:nil</code> attribute set to true, it can be left empty, even though its element declaration may have required content.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_Notation">Notation</a>
		</span>A notation is used to identify the format of a piece of data. Values of elements and attributes that are of type, NOTATION, must come from the names of declared notations. See: <a title="http://www.w3.org/TR/xmlschema-1/#cNotation_Declarations" href="http://www.w3.org/TR/xmlschema-1/#cNotation_Declarations">http://www.w3.org/TR/xmlschema-1/#cNotation_Declarations</a>.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_PreserveWS">Preserve Whitespace Policy</a>
		</span>Preserve whitespaces exactly as they appear in instances.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_TypeFinal">Prohibited Derivations</a>
		</span>(Applies to type definitions). Derivation methods that cannot be used to create sub-types from a given type definition.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_TypeBlock">Prohibited Substitutions</a>
		</span>(Applies to complex type definitions). Prevents sub-types that have been derived using the specified derivation methods from validating element instances in place of the given type definition.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_ReplaceWS">Replace Whitespace Policy</a>
		</span>Replace tab, line feed, and carriage return characters with space character (Unicode character 32).
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_Sequence">Sequence Model Group</a>
		</span>Child elements and model groups must be provided <em>in the specified order</em> in instances. See: <a title="http://www.w3.org/TR/xmlschema-1/#element-sequence" href="http://www.w3.org/TR/xmlschema-1/#element-sequence">http://www.w3.org/TR/xmlschema-1/#element-sequence</a>.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_SubGroup">Substitution Group</a>
		</span>Elements that are <em>members</em> of a substitution group can be used wherever the <em>head</em> element of the substitution group is referenced.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_ElemFinal">Substitution Group Exclusions</a>
		</span>(Applies to element declarations). Prohibits element declarations from nominating themselves as being able to substitute a given element declaration, if they have types that are derived from the original element's type using the specified derivation methods.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_TargetNS">Target Namespace</a>
		</span>The target namespace identifies the namespace that components in this schema belongs to. If no target namespace is provided, then the schema components do not belong to any namespace.
	</p>
	<p>
		<span class="glossaryTerm">
			<a name="term_Unique">Uniqueness Constraint</a>
		</span>Ensures uniqueness of an element/attribute value, or a combination of values, within a specified scope. See: <a title="http://www.w3.org/TR/xmlschema-1/#cIdentity-constraint_Definitions" href="http://www.w3.org/TR/xmlschema-1/#cIdentity-constraint_Definitions">http://www.w3.org/TR/xmlschema-1/#cIdentity-constraint_Definitions</a>.
	</p>
	<div style="text-align: right; clear: both;">
		<a href="#top">top</a>
	</div>
	<hr/>
</div>
<p class="footer">
	Generated by <a href="http://xml.fiforms.org/xs3p/">xs3p</a> (<a href="http://titanium.dstc.edu.au/xml/xs3p">old link</a>)
	. Last modified: <script type="text/javascript">
	                    <!--
	                    document.write(document.lastModified);
// -->
                    </script>
</p>
</body>
</html>