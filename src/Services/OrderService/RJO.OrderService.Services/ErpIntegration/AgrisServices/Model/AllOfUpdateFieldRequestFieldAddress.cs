/* 
 * Agris API
 *
 * **[Postman Examples](https://documenter.getpostman.com/view/8851010/T17KeSEP?version=latest)**
 *
 * OpenAPI spec version: 1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System.Text;

namespace RJO.OrderService.Services.ErpIntegration.AgrisServices.Model;

/// <summary>
/// AllOfUpdateFieldRequestFieldAddress
/// </summary>
[DataContract]
public class AllOfUpdateFieldRequestFieldAddress : Address, IEquatable<AllOfUpdateFieldRequestFieldAddress>, IValidatableObject
{
	/// <summary>
	/// Initializes a new instance of the <see cref="AllOfUpdateFieldRequestFieldAddress" /> class.
	/// </summary>
	public AllOfUpdateFieldRequestFieldAddress(int? fsaNumber = default, int? fsaTract = default, string country = default, string stateCode = default, string stateName = default, string countyCode = default, string countyName = default,
		string section = default, string range = default, string township = default, int? latitude = default, int? longitude = default, int? minlat = default, int? minlon = default, int? maxlat = default, int? maxlon = default) : base(
		fsaNumber, fsaTract, country, stateCode, stateName, countyCode, countyName, section, range, township,
		latitude, longitude, minlat, minlon, maxlat, maxlon)
	{
	}

	/// <summary>
	/// Returns the string presentation of the object
	/// </summary>
	/// <returns>String presentation of the object</returns>
	public override string ToString()
	{
		var sb = new StringBuilder();
		sb.Append("class AllOfUpdateFieldRequestFieldAddress {\n");
		sb.Append("  ").Append(base.ToString().Replace("\n", "\n  ")).Append("\n");
		sb.Append("}\n");
		return sb.ToString();
	}

	/// <summary>
	/// Returns the JSON string presentation of the object
	/// </summary>
	/// <returns>JSON string presentation of the object</returns>
	public override string ToJson() => JsonConvert.SerializeObject(this, Formatting.Indented);

	/// <summary>
	/// Returns true if objects are equal
	/// </summary>
	/// <param name="input">Object to be compared</param>
	/// <returns>Boolean</returns>
	public override bool Equals(object input) => Equals(input as AllOfUpdateFieldRequestFieldAddress);

	/// <summary>
	/// Returns true if AllOfUpdateFieldRequestFieldAddress instances are equal
	/// </summary>
	/// <param name="input">Instance of AllOfUpdateFieldRequestFieldAddress to be compared</param>
	/// <returns>Boolean</returns>
	public bool Equals(AllOfUpdateFieldRequestFieldAddress input)
	{
		if (input == null)
			return false;

		return base.Equals(input);
	}

	/// <summary>
	/// Gets the hash code
	/// </summary>
	/// <returns>Hash code</returns>
	public override int GetHashCode()
	{
		var hashCode = base.GetHashCode();
		return hashCode;
	}

	/// <summary>
	/// To validate all properties of the instance
	/// </summary>
	/// <param name="validationContext">Validation context</param>
	/// <returns>Validation Result</returns>
	IEnumerable<ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
	{
		yield break;
	}
}
