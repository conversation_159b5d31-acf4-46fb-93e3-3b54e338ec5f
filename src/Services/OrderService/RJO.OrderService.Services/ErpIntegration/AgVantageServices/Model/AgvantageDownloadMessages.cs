// Request / response messages between Hrvyst and AgVantage
//
using System.Collections.ObjectModel;
using System.Text.Json.Serialization;

namespace RJO.OrderService.Services.ErpIntegration.AgVantageServices.Model;

public class Customer
{
	public int Id { get; set; }
	public string Name { get; set; }
	public bool Active { get; set; }
}

public class CustomerResponse
{
	public int Count { get; set; }
	public int Limit { get; set; }
	public int Offset { get; set; }
	public Collection<Customer> Customers { get; set; }
}

public class CustomerDetails
{
	public int Id { get; set; }
	public bool Active { get; set; }
	
	[JsonPropertyName("first_name")]
	public string FirstName { get; set; }
	[JsonPropertyName("last_name")]
	public string LastName { get; set; }
	public string Address1 { get; set; }
	public string Address2 { get; set; }
	public string City { get; set; }
	public string State { get; set; }
	public int Zip { get; set; }
	public string Email { get; set; }
	public long Phone { get; set; }
	public long Cell { get; set; }
}

public class Location
{
	public int Id { get; set; }
	public string Name { get; set; }
}
