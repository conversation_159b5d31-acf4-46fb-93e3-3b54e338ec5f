using System.Text;
using System.Reflection;
using System.Globalization;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.ERP;
using RJO.OrderService.Domain.Metadata;
using RJO.OrderService.Services.Helper;
using RJO.OrderService.Services.Services.OrderProcess;
using RJO.OrderService.Services.ErpIntegration.AgVantageServices.Model;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Domain.Enumeration;

namespace RJO.OrderService.Services.ErpIntegration.AgVantageServices.Api;

public class AgvantageErpProxy 
{
	const string HrvystCancelComment = "Canceled in Hrvyst";
	AgvantageSetting _setting;
	readonly AppDbContext _dbContext;
	readonly AgvantageClient _client;

	public AgvantageErpProxy(ErpIntegrationAgent erpIntegrationAgent)
	{
		_dbContext = erpIntegrationAgent.DbContext;
		_client = new(erpIntegrationAgent.ServiceProvider);
	}

	#region supporting methods
	static bool TryParseContractNumber(string contractNumber, out string number, out int seq)
	{
		number = null;
		seq = 0;

		var parts = contractNumber?.Split('.');
		if (parts?.Length == 2 && int.TryParse(parts[1], out seq))
		{
			number = parts[0];
			return true;
		}

		return false;
	}

	static int GetType(Contract contract) =>
		// type - Required and must be a 11, 12, 13, or 14 for Fixed Price, Basis, Extended Time to Decide(ETD) and Minimum Price contracts respectively.
		contract.ContractTypeId switch
		{
			var id when id == ContractTypeDictionary.FlatPrice => 11,
			var id when id == ContractTypeDictionary.Basis => 12,
			var id when id == ContractTypeDictionary.HTA => 13,
			_ => 0
		};

	public async Task<string> GetAgvantageContractId(Guid contractId)
	{
		var agvantageContract = await _dbContext.Set<AgvantageContract>().AsNoTracking()
			.FirstOrDefaultAsync(x => x.ContractId == contractId);
		AssertionConcern.ArgumentIsNotNull(agvantageContract, "The contract don't have Agvatage Id");
		return agvantageContract.AgvantageId;
	}

	async Task UpsertIdAgvatangeContract(Guid contractId, string agvantageId, CancellationToken cancellationToken, bool newContractFlag = false)
	{
		AgvantageContract agvantageContract = null;
		var insertFlag = newContractFlag;
		if (!insertFlag)
		{
			agvantageContract = await _dbContext.AgvantageContract.FirstOrDefaultAsync(x => x.ContractId == contractId, cancellationToken);
			insertFlag = agvantageContract == null;
		}

		if (insertFlag)
		{
			await _dbContext.AgvantageContract.AddAsync(new AgvantageContract
			{
				ContractId = contractId,
				AgvantageId = agvantageId
			}, cancellationToken);
			await _dbContext.SaveChangesAsync(cancellationToken);
		}
		else if (agvantageContract?.AgvantageId != agvantageId)
		{
			agvantageContract.AgvantageId = agvantageId;
			await _dbContext.SaveChangesAsync(cancellationToken);
		}
	}

	public async Task VerifyContract(Contract contract, CancellationToken cancellationToken = default)
	{
		if (_setting == null)
		{
			_setting = await _dbContext.Set<AgvantageSetting>().AsNoTracking().FirstOrDefaultAsync(cancellationToken);
			AssertionConcern.ArgumentIsNotNull(_setting, $"No ERP connection setting found for contract {contract.Id}");
		}
	}

	async Task AssignCustomFields(Contract contract, AgvantageNewContractRequest request)
	{
		var qeuryCustomProperties = from m in _dbContext.Set<ContractMetadata>()
									join mc in _dbContext.Set<OrderMetadataConfiguration>() on m.FieldId.Value equals mc.Id
									join i in _dbContext.Set<OrderMetadataItem>()
										on new { mc.Id, m.Value } equals new { Id = i.ConfigurationId, i.Value } into mvalue
									from i in mvalue.DefaultIfEmpty()
									where m.ContractId == contract.Id && mc.ErpField != null
									select new
									{
										Key = mc.ErpField,
										ErpValue = i.ErpValue != null ? i.ErpValue : m.Value
									};
		var customProperties = await qeuryCustomProperties.ToDictionaryAsync(x => x.Key, x => x.ErpValue);
		if (customProperties.Count == 0)
			return;

		var type = typeof(AgvantageNewContractRequest);
		foreach (var propertyPair in customProperties)
		{
			if (propertyPair.Value != null)
				UpdateProperty(request, type, propertyPair);
		}
	}

	static void UpdateProperty(AgvantageNewContractRequest request, Type type, KeyValuePair<string, string> propertyPair)
	{
		try
		{
			var property = type.GetProperty(propertyPair.Key, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
			if (property == null)
				request.CustomProperties[propertyPair.Key] = JsonDocument.Parse(JsonSerializer.Serialize(propertyPair.Value)).RootElement;
			else
			{
				var targetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;
				object value = targetType == typeof(int) && propertyPair.Value.Contains('.', StringComparison.Ordinal)
					? Convert.ChangeType(Convert.ToDouble(propertyPair.Value), targetType)
					: Convert.ChangeType(propertyPair.Value, targetType, CultureInfo.InvariantCulture);
				property.SetValue(request, value);
			}
		}
		catch(Exception ex)
		{
			throw new BusinessException($"Failed to set CustomField {propertyPair.Key} with {propertyPair.Value}: {ex.Message}");
		}
	}
	#endregion

	#region build request
	async Task<AgvantageNewContractRequest> BuildContractRequest(Contract contract, CancellationToken cancellationToken = default)
	{
		var customer = await _dbContext.Customers
			.AsNoTracking()
			.FirstOrDefaultAsync(x => x.Id == contract.CustomerId, cancellationToken);
		AssertionConcern.ArgumentIsNotNull(customer, "Invalid Customer");
		var location = await _dbContext.Locations
			.AsNoTracking()
			.FirstOrDefaultAsync(x => x.Id == contract.LocationId, cancellationToken);
		AssertionConcern.ArgumentIsNotNull(location, "Invalid Location");

		var employee = await _dbContext.Employees
			.AsNoTracking()
			.FirstOrDefaultAsync(x => x.Id == contract.EmployeeId, cancellationToken);
		AssertionConcern.ArgumentIsTrue(int.TryParse(employee.Number.Trim(), out var employeeNumber), "Invalid Employee");

		var commodity = await _dbContext.Commodities
			.AsNoTracking()
			.Include("Product")
			.FirstOrDefaultAsync(x => x.Id == contract.CommodityId, cancellationToken);
		AssertionConcern.ArgumentIsNotNull(commodity, "Invalid Commodity");

		var contractType = GetType(contract);
		AssertionConcern.ArgumentIsTrue(contractType != 0, "Invalid AgVantage contract type");

		// FuturesMonth format: H23
		var futureMonth = FutureMonthsHelper.GetMonthNumberFromMonthCode(contract.FuturesMonth[0]);
		AssertionConcern.ArgumentIsTrue(int.TryParse(contract.FuturesMonth[1..].Trim(), out var futureYear), "Invalid Future Year");
		AssertionConcern.ArgumentIsTrue(int.TryParse(customer.Number.Trim(), out var customerNumber), "Invalid Customer Number");
		AssertionConcern.ArgumentIsTrue(int.TryParse(location.Number.Trim(), out var locationNumber), "Invalid Location Number");

		var commodityMapping = await _dbContext.ErpIdMapping.AsNoTracking()
			.FirstOrDefaultAsync(x => x.ErpName == ErpConstants.Agvantage && x.MappingType == ErpIdMappingType.Commodity && x.SourceId == contract.CommodityId, cancellationToken);
		var locationMapping = await _dbContext.ErpIdMapping.AsNoTracking()
			.FirstOrDefaultAsync(x => x.ErpName == ErpConstants.Agvantage && x.MappingType == ErpIdMappingType.Location && x.SourceId == contract.DeliveryLocationId, cancellationToken);
		AssertionConcern.ArgumentIsNotTrue(string.IsNullOrEmpty(commodityMapping?.DestinationValue), "Invalid Grade in ErpIdMapping table");
		AssertionConcern.ArgumentIsTrue(int.TryParse(commodityMapping.DestinationValue.Trim(), out var grade), "Invalid Grade value");
		AssertionConcern.ArgumentIsNotTrue(string.IsNullOrEmpty(locationMapping?.DestinationValue), "Invalid DeliveryBasis in ErpIdMapping table");

		string expirationDate = contractType == 11 ? null : (contract.Expiration ?? DateTime.Now).ToString("MM/dd/yyyy");
		var deliveryTerms = "D";

		var request = new AgvantageNewContractRequest
		{
			Type = contractType,
			BuyerId = employeeNumber,
			CropId = commodity.Number,
			ControlDate = contract.CreatedOn.ToString("MM/dd/yyyy"),
			CustomerId = customerNumber,
			DeliveryStartDate = contract.DeliveryStartDate.ToString("MM/dd/yyyy"),
			DeliveryEndDate = contract.DeliveryEndDate.ToString("MM/dd/yyyy"),
			LocationId = locationNumber,
			CashPrice = contract.Price ?? 0m,
			BasisAmount = contract.NetBasis ?? 0m,
			FuturesPrice = contract.FuturesPrice ?? 0m,
			FuturesMonth = futureMonth,
			FuturesYear = 2000 + futureYear, 
			FinalPricingDate = expirationDate,
			ContractedBushels = contract.GrossRemainingBalance,
			Grade = grade,
			DeliveryBasis = locationMapping.DestinationValue, 
			DeliveryTerms = deliveryTerms
		};
		await AssignCustomFields(contract, request);
		return request;
	}

	async Task<AgvantagePricingContractRequest> BuildPricingRequest(Contract contract, string priceTag, decimal quantity, string comment, CancellationToken cancellationToken = default)
	{
		var employee = await _dbContext.Employees
			.AsNoTracking()
			.FirstOrDefaultAsync(x => x.Id == contract.EmployeeId, cancellationToken);
		AssertionConcern.ArgumentIsTrue(int.TryParse(employee.Number.Trim(), out var employeeNumber), "Invalid Employee");

		var futureMonth = FutureMonthsHelper.GetMonthNumberFromMonthCode(contract.FuturesMonth[0]);
		var request = new AgvantagePricingContractRequest
		{
			BuyerID = employeeNumber,
			Date = contract.CreatedOn.ToString("MM/dd/yyyy"),
			FinalPrice = contract.Price ?? 0m,
			BasisAmount = contract.NetBasis ?? 0m,
			FuturesPrice = contract.FuturesPrice ?? 0m,
			FuturesMonth = futureMonth,
			PricedBushels = quantity,
			Comments = [ priceTag ]
			// Signed ??
		};

		if (!string.IsNullOrEmpty(contract.Comments))
			request.Comments.Add(contract.Comments);
		if (!string.IsNullOrEmpty(comment))
			request.Comments.Add(comment);

		return request;
	}

	static AgvantageDeletePricedContractRequest BuildDelPricedRequest(int seq) => new()	{ Seq = seq	};
	#endregion

	#region parse response
	async Task<ErpResponse> ParseNewContractResponse(Contract contract, HttpResponseMessage response, string requestBody, string responseBody, CancellationToken cancellationToken)
	{
		ErpResponse erpResponse;
		if (response.IsSuccessStatusCode)
		{
			try
			{
				var agvantageSuccess = JsonSerializer.Deserialize<AgvantageNewSuccessResponse>(responseBody);	
				var controlNumber = agvantageSuccess.Id[1..10];
				await UpsertIdAgvatangeContract(contract.Id, agvantageSuccess.Id, cancellationToken, true);
				erpResponse = ErpResponse.NewOK(controlNumber.TrimStart('0'));
			}
			catch (Exception ex)
			{
				erpResponse = ErpResponse.NewError((int)response.StatusCode, $"AgVantage payload format error: {ex.Message}");
			}
		}
		else
		{
			string errorMsg = $"{response.StatusCode}: ";
			try
			{
				var agvantageError = JsonSerializer.Deserialize<AgvantageErrorResponse>(responseBody);
				errorMsg += string.Join(",", agvantageError.Errors) + " | "; 
				errorMsg += string.Join(", ", agvantageError.Legacy.Select(a => $"{a.Property}: {a.Message}"));
				errorMsg = Regex.Replace(errorMsg, @"\s+", " ");
			}
			catch (Exception)
			{
				errorMsg += responseBody;
			}
			erpResponse = ErpResponse.NewError((int)response.StatusCode, errorMsg);
		}

		erpResponse.RequestPayload = requestBody;
		erpResponse.ResponsePayload = responseBody;
		return erpResponse;
	}

	async Task<ErpResponse> ParsePricingResponse(Contract contract, HttpResponseMessage response, string requestBody, string responseBody, string priceTag, CancellationToken cancellationToken) =>
		await ParsePriceResponse(contract, response, requestBody, responseBody, async () =>
		{
			var agvantageSuccess = JsonSerializer.Deserialize<AgvantagePricingSuccessResponse>(responseBody);
			var priceItem = agvantageSuccess.Prices.FirstOrDefault(x => x.Comments.Count > 0 && x.Comments.Contains(priceTag));
			if (priceItem == null)
				return ErpResponse.NewError((int)response.StatusCode, $"There is no price item found in AgVantage response");

			await UpsertIdAgvatangeContract(contract.Id, agvantageSuccess.Id, cancellationToken);
			return ErpResponse.NewOK($"{agvantageSuccess.ControlNumber}.{priceItem.Seq}");
		});

	static async Task<ErpResponse> ParseDelPricedResponse(Contract contract, HttpResponseMessage response, string requestBody, string responseBody, int seq) =>
		await ParsePriceResponse(contract, response, requestBody, responseBody, async () =>
		{
			var agvantageSuccess = JsonSerializer.Deserialize<AgvantageDeletePricedContractResponse>(responseBody);
			var priceItem = agvantageSuccess.Prices.FirstOrDefault(x => x.Seq == seq);
			return priceItem != null
				 ? ErpResponse.NewError((int)response.StatusCode, $"The price item isn't deleted in AgVantage response")
				 : ErpResponse.NewOK($"{agvantageSuccess.ControlNumber}.{seq}");
		});

	static async Task<ErpResponse> ParsePriceResponse(Contract contract, HttpResponseMessage response, string requestBody, string responseBody
		, Func<Task<ErpResponse>> parseSuccessResponse)
	{
		ErpResponse erpResponse;
		if (response.IsSuccessStatusCode)
		{
			try
			{
				erpResponse = await parseSuccessResponse();
			}
			catch (Exception ex)
			{
				erpResponse = ErpResponse.NewError((int)response.StatusCode, $"AgVantage payload format error: {ex.Message}");
			}
		}
		else
		{
			string errorMsg = $"{response.StatusCode}: ";
			try
			{
				var agvantageError = JsonSerializer.Deserialize<AgvantagePricingErrorResponse>(responseBody);
				if (!string.IsNullOrEmpty(agvantageError.Error))
					errorMsg += agvantageError.Error;
				else
				{
					var agvantageError2 = JsonSerializer.Deserialize<AgvantageErrorResponse>(responseBody);
					errorMsg += string.Join(",", agvantageError2.Errors) + " | ";
					errorMsg += string.Join(", ", agvantageError2.Legacy.Select(a => $"{a.Property}: {a.Message}"));
					errorMsg = Regex.Replace(errorMsg, @"\s+", " ");
				}
			}
			catch (Exception)
			{
				errorMsg += responseBody;
			}
			erpResponse = ErpResponse.NewError((int)response.StatusCode, errorMsg);
		}

		erpResponse.RequestPayload = requestBody;
		erpResponse.ResponsePayload = responseBody;
		return erpResponse;
	}
	#endregion

	#region Agvantage interface
	public async Task<ErpResponse> CreateContract(Contract contract, CancellationToken cancellationToken = default)
	{
		if (contract.IsSell)
			return ErpResponse.NewError("No Sale contract for AgVantage");
		await VerifyContract(contract, cancellationToken);

		var response = await _client.Send(_setting, async httpClient =>
		{
			var requestContent = await BuildContractRequest(contract, cancellationToken);
			var requestBody = JsonSerializer.Serialize(requestContent, requestContent.GetType(), ErpJsonSerializerOptions.DefaultAgvantageOptions);
			using StringContent jsonContent = new(requestBody, Encoding.UTF8, "application/json");

			var httpResponse = await httpClient.PostAsync(new Uri($"{_setting.BaseAddress}/graincontracts"), jsonContent, cancellationToken);
			var responseBody = await httpResponse.Content.ReadAsStringAsync(cancellationToken);
			var erpResponse = await ParseNewContractResponse(contract, httpResponse, requestBody, responseBody, cancellationToken);
			return erpResponse;
		});
		return response;
	}

	public async Task<ErpResponse> PriceContract(Contract contract, string agvantageId, decimal? quantity = null, string comment = null, CancellationToken cancellationToken = default)
	{
		await VerifyContract(contract, cancellationToken);

		var response = await _client.Send(_setting, async (httpClient) =>
		{
			var priceTag = $"<Hrvyst:{contract.InternalCode}.{DateTime.Now.ToUnixTimeSeconds()}>";
			var uriString = $"{_setting.BaseAddress}/graincontractprices/{agvantageId}";
			var requestContent = await BuildPricingRequest(contract, priceTag, quantity ?? contract.GrossRemainingBalance, comment, cancellationToken);
			var requestBody = JsonSerializer.Serialize(requestContent, requestContent.GetType(), ErpJsonSerializerOptions.DefaultAgvantageOptions);
			using StringContent jsonContent = new(requestBody, Encoding.UTF8, "application/json");
			var httpResponse = await httpClient.PostAsync(new Uri(uriString), jsonContent, cancellationToken);
			var responseBody = await httpResponse.Content.ReadAsStringAsync(cancellationToken);
			var erpResponse = await ParsePricingResponse(contract, httpResponse, requestBody, responseBody, priceTag, cancellationToken);
			return erpResponse;
		});
		return response;
	}

	public async Task<ErpResponse> DeletePricedContract(Contract contract, string number, int seq, CancellationToken cancellationToken = default)
	{
		await VerifyContract(contract, cancellationToken);

		var response = await _client.Send(_setting, async (httpClient) =>
		{
			var uriString = $"{_setting.BaseAddress}/graincontractprices/{number}";
			var requestContent = BuildDelPricedRequest(seq);
			var requestBody = JsonSerializer.Serialize(requestContent, requestContent.GetType(), ErpJsonSerializerOptions.DefaultAgvantageOptions);
			using StringContent jsonContent = new(requestBody, Encoding.UTF8, "application/json");
			var request = new HttpRequestMessage
			{
				Method = HttpMethod.Delete,
				RequestUri = new Uri(uriString),
				Content = jsonContent
			};
			var httpResponse = await httpClient.SendAsync(request, cancellationToken);
			var responseBody = await httpResponse.Content.ReadAsStringAsync(cancellationToken);
			var erpResponse = await ParseDelPricedResponse(contract, httpResponse, requestBody, responseBody, seq);
			return erpResponse;
		});

		return response;
	}

	public async Task<ErpResponse> UpdateContract(Contract contract, CancellationToken cancellationToken = default)
	{
		await VerifyContract(contract, cancellationToken);

		AssertionConcern.ArgumentIsTrue(
			(contract.ContractTypeId == ContractTypeDictionary.Basis || contract.ContractTypeId == ContractTypeDictionary.HTA) && contract.IsOnStatus(EContractState.Priced),
			"Unable to edit the contract at AgVantage");

		return await UpdatePricedContract(contract, cancellationToken: cancellationToken);
	}

	public async Task<ErpResponse> AdjustContract(Contract contract, decimal deletedQuantity, bool updateParent, CancellationToken cancellationToken = default)
	{
		await VerifyContract(contract, cancellationToken);

		AssertionConcern.ArgumentIsTrue(
			TryParseContractNumber(contract.Number, out var controlNumber, out var seq),
			$"Invalid AgVantage priced contract number: {contract?.Number ?? string.Empty}");
		var agvantageId = await GetAgvantageContractId(contract.Id);
		var erpResponse = updateParent
			? await DeletePricedContract(contract, agvantageId, seq, cancellationToken)
			: await CancelPricedContract(contract, agvantageId, seq, deletedQuantity, cancellationToken);
		return erpResponse;
	}

	async Task<ErpResponse> UpdatePricedContract(Contract contract, CancellationToken cancellationToken = default)
	{
		AssertionConcern.ArgumentIsTrue(
			TryParseContractNumber(contract.Number, out var controlNumber, out var seq),
			$"Invalid AgVantage priced contract number: {contract?.Number ?? string.Empty}");
		var agvantageId = await GetAgvantageContractId(contract.Id);
		var erpResponse = await DeletePricedContract(contract, agvantageId, seq, cancellationToken);
		if (!erpResponse.Success)
			return erpResponse;

		var erpLog = ErpLog.ErpSuccess(contract.Id, Guid.Empty, ErpAction.Update, erpResponse.RequestPayload, erpResponse.ResponsePayload, erpResponse.ContractNumber);
		await _dbContext.ErpLog.AddAsync(erpLog, cancellationToken);
		await _dbContext.SaveChangesAsync(cancellationToken);

		return await PriceContract(contract, agvantageId, cancellationToken: cancellationToken);
	}

	async Task<ErpResponse> CancelPricedContract(Contract contract, string agvantageId, int seq, decimal deletedQuantity, CancellationToken cancellationToken = default)
	{
		var erpResponse = await DeletePricedContract(contract, agvantageId, seq, cancellationToken);
		if (!erpResponse.Success)
			return erpResponse;
		var erpLog = ErpLog.ErpSuccess(contract.Id, Guid.Empty, ErpAction.Cancel, erpResponse.RequestPayload, erpResponse.ResponsePayload, erpResponse.ContractNumber);
		await _dbContext.ErpLog.AddAsync(erpLog, cancellationToken);

		// Create the original contact with special comment
		erpResponse = await PriceContract(contract, agvantageId, contract.GrossRemainingBalance - deletedQuantity, HrvystCancelComment, cancellationToken);
		if (!erpResponse.Success || contract.GrossRemainingBalance == 0)
		{
			await _dbContext.SaveChangesAsync(cancellationToken);
			return erpResponse;
		}
		erpLog = ErpLog.ErpSuccess(contract.Id, Guid.Empty, ErpAction.Cancel, erpResponse.RequestPayload, erpResponse.ResponsePayload, erpResponse.ContractNumber);
		await _dbContext.ErpLog.AddAsync(erpLog, cancellationToken);
		await _dbContext.SaveChangesAsync(cancellationToken);

		// create a pricing for the remaining
		return await PriceContract(contract, agvantageId, cancellationToken: cancellationToken);
	}
	#endregion
}
