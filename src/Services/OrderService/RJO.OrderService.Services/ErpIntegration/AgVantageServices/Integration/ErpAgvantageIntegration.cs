using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain;
using RJO.OrderService.Services.ErpIntegration.AgVantageServices.Api;
using RJO.OrderService.Services.Services.OrderProcess;

namespace RJO.OrderService.Services.ErpIntegration.AgVantageServices.Integration;

public class ErpAgvantageIntegration : Contracts.ErpIntegration
{
	readonly AgvantageErpProxy _agvantageProxy;

	public ErpAgvantageIntegration(ErpIntegrationAgent erpIntegrationAgent, string erpName = ErpConstants.Agvantage) : base(erpIntegrationAgent, erpName)
	{
		_agvantageProxy = new(erpIntegrationAgent);
	}

	public override async Task<ErpResponse> CreateContract(Contract contract) => await _agvantageProxy.CreateContract(contract);

	public override async Task<ErpResponse> UpdateContract(Contract contract, decimal quantityChanged = 0) => 
		await _agvantageProxy.UpdateContract(contract);

	public override async Task<ErpResponse> PriceContract(Contract contract, Contract body)
	{
		var agvantageId = await _agvantageProxy.GetAgvantageContractId(body.Id);
		return await _agvantageProxy.PriceContract(contract, agvantageId);
	}

	public override Task<ErpResponse> RollContract(Contract contract, Contract body) => throw new NotImplementedException();

	public override Task<ErpResponse> SplitContract(Contract contract, Contract body) => throw new NotImplementedException();

	public override async Task<ErpResponse> AdjustContract(Contract contract, decimal quantity, bool updateParent = true, bool? appliedLoad = null) => 
		await _agvantageProxy.AdjustContract(contract, quantity, updateParent);

	public override Task<ErpResponse> GetErpContract(string number) => throw new NotImplementedException();

	public override Task<ErpResponse> ResendContract(Contract contract, string action, string content) => throw new NotImplementedException();

	public override Task<ErpInventoryResponse> GetInventory(Guid customerId) => throw new NotImplementedException();

	public override bool IsErpUpdateRequired(Contract contract, Contract originalContract) =>
		contract.ContractTypeId switch
		{
			// cash price is based on basis and future price, ignored.
			var id when id == ContractTypeDictionary.Basis =>
				contract.EmployeeId != originalContract.EmployeeId
				|| contract.Quantity != originalContract.Quantity
				|| (contract.FuturesPrice ?? 0m) != (originalContract.FuturesPrice ?? 0m)
				|| contract.FuturesMonth != originalContract.FuturesMonth,

			var id when id == ContractTypeDictionary.HTA =>
				contract.EmployeeId != originalContract.EmployeeId
				|| contract.Quantity != originalContract.Quantity
				|| (contract.NetBasis ?? 0m) != (originalContract.NetBasis ?? 0m)
				|| (contract.FuturesPrice ?? 0m) != (originalContract.FuturesPrice ?? 0m)
				|| contract.FuturesMonth != originalContract.FuturesMonth,
			_ => true
		};
}
