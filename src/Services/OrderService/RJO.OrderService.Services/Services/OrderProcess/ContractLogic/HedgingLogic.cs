using LaunchDarkly.Sdk;
using LaunchDarkly.Sdk.Server;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Common.LaunchDarkly;
using RJO.OrderService.Common;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Persistence.Repositories.Logs;
using RJO.OrderService.Services.DTO.ResultSets;
using RJO.OrderService.Services.Enumeration;
using RJO.OrderService.Services.Helper;
using ETransactionEvent = RJO.OrderService.Services.Enumeration.ETransactionEvent;

namespace RJO.OrderService.Services.Services.OrderProcess.ContractLogic;

public class HedgingLogic : IContractHedgingLogic<HedgingResultDto>
{
	readonly MarketTransactionRepository _futureTransactionBucketRepository;
	readonly BucketBalanceRepository _bucketBalanceRepository;
	readonly SettingRepository _settingRepository;
	readonly DoNotHedgeLogRepository _doNotHedgeLogRepository;
	readonly CommodityRepository _commodityRepository;
	readonly BucketBalanceContractRepository _bucketBalanceContractRepository;
	readonly LogWorkflowRepository _logWorkflow;
	readonly HedgeMappingHelper _hedgeMappingHelper;
	readonly HedgeAccountHelper _hedgeAccountHelper;
	readonly OfferRepository _offerRepository;
	readonly AppDbContext _appDbContext;
	readonly LdClient _ldClient;
	public ETransactionEvent Operation { get; private set; }
	public ETransactionSource Source { get; private set; }

	public HedgingLogic(MarketTransactionRepository futureTransactionBucketRepository,
		BucketBalanceRepository bucketBalanceRepository,
		SettingRepository settingRepository,
		DoNotHedgeLogRepository doNotHedgeLogRepository,
		CommodityRepository commodityRepository,
		LogWorkflowRepository logWorkflow,
		HedgeMappingHelper hedgeMappingHelper,
		BucketBalanceContractRepository bucketBalanceContractRepository,
		HedgeAccountHelper hedgeAccountHelper,
		OfferRepository offerRepository,
		AppDbContext appDbContext,
		LdClient ldClient)
	{
		_futureTransactionBucketRepository = futureTransactionBucketRepository;
		_bucketBalanceRepository = bucketBalanceRepository;
		_settingRepository = settingRepository;
		_doNotHedgeLogRepository = doNotHedgeLogRepository;
		_commodityRepository = commodityRepository;
		_logWorkflow = logWorkflow;
		_hedgeMappingHelper = hedgeMappingHelper;
		_bucketBalanceContractRepository = bucketBalanceContractRepository;
		_hedgeAccountHelper = hedgeAccountHelper;
		_offerRepository = offerRepository;
		_appDbContext = appDbContext;
		_ldClient = ldClient;
	}

	bool IsReverseProcess(Contract contract, bool isSell) => 
		Operation == ETransactionEvent.Cancel || Operation == ETransactionEvent.Undo || (Operation == ETransactionEvent.Edit && contract.IsSell != isSell);

	public void DefineOperation(ETransactionEvent operation, ETransactionSource source)
	{
		Operation = operation;
		Source = source;
	}

	static void CloseMarketTransaction(MarketTransaction transaction, bool isEFP = false)
	{
		transaction.Approved();
		transaction.StartProcessing();
		transaction.ConfirmProcessing(isEFP ? "EFP" : string.Empty);
		transaction.FillLots(transaction.Lots, transaction.FuturesPrice, transaction.Quantity);
	}

	async Task<HedgingResultDto> ProcessContractAsHedging(Contract contract, decimal quantity, bool isSell, BucketBalance balance, bool comesFromDnh, bool omitHedgeMapping = false, string overrideInstrument = null)
	{
		var resultOfThisOperation = new HedgingResultDto();
		await _logWorkflow.AddLog("Hedge logic", "Started processing hedge logic");
		await _logWorkflow.AddLog("Hedging", "New Contract Bushells:{0}", quantity);
		var commodity = await _commodityRepository.GetById(contract.CommodityId);
		await _logWorkflow.AddLog("Hedging", "Current balance:{0}", balance.ToString());

		var lotInformation = new Lot(quantity, commodity.LotFactor);
		await _logWorkflow.AddLog("Hedging", "Calculated lot quantity Lots:{0} Remaining:{1}", lotInformation.Lots, lotInformation.Remainig);
		if (!comesFromDnh || contract.ComesFromOffer())
		{
			var isReverseProcess = IsReverseProcess(contract, isSell);
			balance.CalculateBalanceWithoutUpdate(quantity, isSell, contract.Id, Operation.ToString(), false, false, false, contract.LocationId,
				isReverseProcess, contract.DeliveryStartDate, contract.FuturesMonth, true, contract.RegionId);
		}

		if (lotInformation.Lots > 0)
		{
			resultOfThisOperation.HedgeableQuantityFromContract = lotInformation.Quantity;
			string instrument;
			string futuresMonth;

			// Use override instrument if provided (for spread orders)
			if (!string.IsNullOrEmpty(overrideInstrument))
			{
				instrument = overrideInstrument;
				futuresMonth = contract.FuturesMonth;
				await _logWorkflow.AddLog("Hedging", "Using override instrument (spread symbol): {0}", instrument);
			}
			else if (omitHedgeMapping)
			{
				futuresMonth = contract.FuturesMonth;
				instrument = await _hedgeMappingHelper.GetMarketInstrument(contract.CommodityId, futuresMonth);
			}
			else
			{
				var sellColumn = IsReverseProcess(contract, isSell) ? !isSell : isSell;
				var cqgInstrument = await _hedgeMappingHelper.GetMarketInstrument(contract.CommodityId, contract.RealCropYear, contract.ContractTypeId, sellColumn, contract.DeliveryStartDate, true, contract.FuturesMonth, contract.RegionId);
				futuresMonth = cqgInstrument.Value;
				instrument = cqgInstrument.Key;
			}

			var accountId = await _hedgeAccountHelper.GetAccountId(commodity.Id, contract.RealCropYear, contract.ContractTypeId, contract.RegionId);
			await _logWorkflow.AddLog("Hedging", $"OmitHedgeMapping:{omitHedgeMapping}, Instrument:{instrument}, FuturesMonth:{futuresMonth}, AccountId:{accountId}");

			MarketTransaction futuresTransaction = null;
			decimal ledgerQuantity;

			if (contract.IsBookedPartialFill())
			{
				futuresTransaction = await _futureTransactionBucketRepository.GetActualByOffer((Guid)contract.OfferId);
				ledgerQuantity = quantity;
			}
			else
			{
				futuresTransaction = new(contract.InternalCode,
					lotInformation.TotalQuantityByLots, lotInformation.Lots, commodity.Id, commodity.Name, instrument,
					futuresMonth, contract.FuturesPrice ?? 0, contract.SimulatePrice(), contract.Id, null, !isSell,
					commodity.LotFactor, true, null, EMarketTransactionType.Market, EMarketTransactionSource.Contract, contract.RealCropYear,
					accountId, contract.PassFill, true);
				resultOfThisOperation.Transactions.Add(futuresTransaction);
				ledgerQuantity = lotInformation.TotalQuantityByLots;
			}

			var liveLedgerMarketTransactionId = futuresTransaction.Id;
			if (Source == ETransactionSource.Offer)
			{
				await _logWorkflow.AddLog("Hedging", "Creating a MT for an offer so will hide it");
				var offer = await _offerRepository.GetById((Guid)contract.OfferId);

				if (offer != null)
				{
					if (offer.InternalStatus.Value != EOfferInternalState.Booked)
					{
						CloseMarketTransaction(futuresTransaction);
						futuresTransaction.Deactivate();

						var marketTransaction = await _appDbContext.MarketTransactions.FirstOrDefaultAsync(x => x.OfferId == contract.OfferId);
						if (marketTransaction != null)
							liveLedgerMarketTransactionId = marketTransaction.Id;
					}

					futuresTransaction.ChangeFuturesMonth(offer.FuturesMonth);
					futuresTransaction.ChangeInstrument(await _hedgeMappingHelper.GetMarketInstrument(offer.CommodityId, offer.FuturesMonth));
				}
			}
			else
			{
				await _logWorkflow.AddLog("Hedging", "Creating a MT to be send to the market");
			}

			futuresTransaction.CreateLedgerEvent(ledgerQuantity, balance.CalculateBalanceWithoutUpdate(lotInformation.Remainig, isSell),
				ETransactionEvent.Create.ToString(), ETransactionType.Hedge.ToString(), contract.RegionId, marketTransactionId: liveLedgerMarketTransactionId);
		}

		resultOfThisOperation = await CreateHedgeForBucketBalance(contract, lotInformation, commodity, isSell, balance, comesFromDnh, resultOfThisOperation);
		return resultOfThisOperation;
	}

	public async Task<HedgingResultDto> CreateHedgeForBucketBalance(Contract contract, Lot lotInformation, Commodity commodity, bool isSell, BucketBalance balance, bool comesFromDnh, HedgingResultDto resultOfThisOperation, bool isPreHedge = false, bool isNotFromThresholdEdition = true)
	{
	    // hedging with BucketBalance
	    var bucketBalanceContract = new BucketBalanceContract(
	        contract.Id,
	        balance.Id
	    );

	    if (lotInformation.Remainig > 0)
	    {
	        resultOfThisOperation.HedgeableQuantityFromBalance = lotInformation.Remainig;
	        await _logWorkflow.AddLog("Hedging", "Loaded hedgeable balance Bushels:{0}", balance.BalanceValue.Value);

	        if (!isPreHedge)
	            balance.UpdateBalance(lotInformation.Remainig, isSell);

	        bucketBalanceContract.UpdateBalance(lotInformation.Remainig, isSell);
	        if (isNotFromThresholdEdition)
	        {
	        	await _bucketBalanceContractRepository.InsertAsync(bucketBalanceContract); //no need to create because from hedging threshold edit we are not getting it from a contract or offer
	        }

	        if (contract.ComesFromOffer() && comesFromDnh)
	        {
	            foreach (var transaction in resultOfThisOperation.Transactions)
	            {
	                await _futureTransactionBucketRepository.InsertAsync(transaction);
	            }

				await _logWorkflow.AddLog("Hedging", "Final step, we are in a DNH scenario and this comes from an offer.");
				return resultOfThisOperation;
			}
		}

		if (balance.Balance() != 0 && (lotInformation.Remainig > 0 || _ldClient.BoolVariation(FeatureFlags.TriggerHedgeBalanceWhenDNHTurnedOff, User.WithKey("system"))))
		{
			var tenantContext = ContextFactory.CreateTenantContext(_appDbContext.TenancyContext.Tenant.Id.ToString(), _appDbContext.TenancyContext.Tenant.DisplayName);
			if (Operation == ETransactionEvent.Roll && _ldClient.BoolVariation(FeatureFlags.EnableHedgingThreshold, tenantContext))
			{
				foreach (var transaction in resultOfThisOperation.Transactions)
				{
					await _futureTransactionBucketRepository.InsertAsync(transaction);
				}

				await _logWorkflow.AddLog("Hedging", "Skipping accumulation hedge during roll operation");
				return resultOfThisOperation;
			}

			var isThresholdSell = balance.Balance() > 0;
			var newBalanceToFutures = GetNewBalanceToFuture(balance, commodity);
			var sellFlag = IsReverseProcess(contract, isSell) ? isThresholdSell : !isThresholdSell;
			var cqgInstrument = await _hedgeMappingHelper.GetMarketInstrumentForAccumulation(contract.CommodityId, contract.RealCropYear, sellFlag, contract.FuturesMonth, contract.RegionId);
			if (newBalanceToFutures.Lots > 0)
			{
				await _logWorkflow.AddLog("Hedging", "Recalculated lot quantity Lots:{0} TotalQuantityByLots:{1} Remaining:{2}", newBalanceToFutures.Lots, newBalanceToFutures.TotalQuantityByLots, balance.BalanceValue.Value);
				var accountId = await _hedgeAccountHelper.GetAccountId(commodity.Id, contract.RealCropYear, contract.ContractTypeId, contract.RegionId);

	            var futuresTransaction = new MarketTransaction(contract.InternalCode,
	                newBalanceToFutures.TotalQuantityByLots, newBalanceToFutures.Lots, commodity.Id, commodity.Name, cqgInstrument.Key,
	                cqgInstrument.Value, contract.FuturesPrice ?? 0, contract.SimulatePrice(), contract.Id, null, isThresholdSell, commodity.LotFactor,
	                true, null, EMarketTransactionType.Market, EMarketTransactionSource.Accumulation, contract.RealCropYear, accountId, contract.PassFill, true);
	            futuresTransaction.CreateLedgerEvent(balance.Balance(), ETransactionEvent.Threshold.ToString(), ETransactionType.Hedge.ToString(), contract.RegionId);
	            resultOfThisOperation.Transactions.Add(futuresTransaction);
				
				if (_ldClient.BoolVariation(FeatureFlags.EnableHedgingThreshold, tenantContext))
				{
					// isNotFromThresholdEdition is set to false whenever we edit the threshold on the fly
					// while there are some transactions in the live ledger for that commodity, region and crop year.
					if (!isNotFromThresholdEdition)
					{
						await RedistributeContractsForNewThresholdAsync(commodity, balance, bucketBalanceContract, futuresTransaction);
					}
					// I know we're duplicating the else statement here, but it's a bit more convenient for whoever ends up removing that feature flag in the future.
					else
					{
						bucketBalanceContract.UpdateBalance(lotInformation.Remainig - Math.Abs(newBalanceToFutures.Remainig), isSell);
						bucketBalanceContract.UpdateMarketTransaction(futuresTransaction.Id);

						await _bucketBalanceContractRepository.AssignMarketTransaction(balance.Id, futuresTransaction.Id);

						if (newBalanceToFutures.Remainig != 0)
						{
							var residualBucketBalanceContract = new BucketBalanceContract(
								contract.Id,
								balance.Id
							);
							residualBucketBalanceContract.UpdateBalance(Math.Abs(newBalanceToFutures.Remainig), isSell);
							await _bucketBalanceContractRepository.InsertAsync(residualBucketBalanceContract);
						}
					}
				}
				else
				{
					bucketBalanceContract.UpdateBalance(lotInformation.Remainig - Math.Abs(newBalanceToFutures.Remainig), isSell);
					bucketBalanceContract.UpdateMarketTransaction(futuresTransaction.Id);

					await _bucketBalanceContractRepository.AssignMarketTransaction(balance.Id, futuresTransaction.Id);

					if (newBalanceToFutures.Remainig != 0)
					{
						var residualBucketBalanceContract = new BucketBalanceContract(
							contract.Id,
							balance.Id
						);
						residualBucketBalanceContract.UpdateBalance(Math.Abs(newBalanceToFutures.Remainig), isSell);
						await _bucketBalanceContractRepository.InsertAsync(residualBucketBalanceContract);
					}
				}
			}
	    }

	    foreach (var transaction in resultOfThisOperation.Transactions)
	    {
	        await _futureTransactionBucketRepository.InsertAsync(transaction);
	    }

	    var byPass = await _settingRepository.GetAbsoluteValue<bool>(TenantSettingsDictionary.ByPass);
	    if (byPass)
	    {
	        await _logWorkflow.AddLog("Hedging", $"ByPass:true and MKs: {resultOfThisOperation.Transactions.Count}");
	        for (var i = 0; i < resultOfThisOperation.Transactions.Count; i++)
	        {
	            var transaction = resultOfThisOperation.Transactions[i];
	            await _logWorkflow.AddLog("Hedging", $"Approving the MKs {i} {transaction}");
	            if (!transaction.IsApproved)
	            {
	                transaction.Approved();
	            }
	        }
	    }
	    else
	    {
	        await _logWorkflow.AddLog("Hedging", "IsAutomaticFuturesEnabled:false");
	    }

	    await _logWorkflow.AddLog("Hedging", "Current balance:{0}", balance.ToString());
	    return resultOfThisOperation;
	}

	async Task RedistributeContractsForNewThresholdAsync(Commodity commodity, BucketBalance balance, BucketBalanceContract bucketBalanceContract, MarketTransaction futuresTransaction)
	{
		// We manually track bucket balance contracts in memory rather than relying on the _appDbContext.BucketBalanceContract db query below
		// because the newly created bucketBalanceContract entry above exists only in the EF Core context but haven't been committed to the database yet.
		// It is important that we consider all bucket balance contracts (both persisted and in-memory) in our accumulation logic.

		List<BucketBalanceContract> bbcList = [];

		var bucketBalanceContracts = await _appDbContext.BucketBalanceContract
			.Where(x => x.BucketBalanceId == balance.Id && x.MarketTransactionId == Guid.Empty)
			.OrderBy(x => x.CreatedOn)
			.ToListAsync();

		bbcList.AddRange(bucketBalanceContracts);
		bbcList = bbcList.OrderBy(x => x == bucketBalanceContract ? DateTime.MaxValue : x.CreatedOn).ToList();

		var comparisonThreshold = commodity.AccumulationThreshold ?? commodity.LotFactor;

		decimal runningTotal = 0;

		List<BucketBalanceContract> bbcToAssign = [];
		foreach (var bbc in bbcList)
		{
			var previousTotal = runningTotal;
			runningTotal += bbc.Balance;

			await _logWorkflow.AddLog("Hedging", $"Contract {bbc.ContractId} with balance {bbc.Balance}, running total now {runningTotal}");

			if (runningTotal <= comparisonThreshold)
			{
				// This contract fully contributes to the threshold
				bbcToAssign.Add(bbc);
				await _logWorkflow.AddLog("Hedging", $"Contract {bbc.ContractId} fully contributes with {bbc.Balance}");
			}
			else if (previousTotal < comparisonThreshold)
			{
				// This contract partially contributes to the threshold
				var partialAmount = comparisonThreshold - previousTotal;
				var remainingAmount = bbc.Balance - partialAmount;

				await _logWorkflow.AddLog("Hedging", $"Contract {bbc.ContractId} partially contributes with {partialAmount} of {bbc.Balance}");

				// Create a new contract for the partial amount
				var partialBbc = new BucketBalanceContract(bbc.ContractId, balance.Id);
				partialBbc.UpdateBalance(partialAmount, bbc.Balance < 0);

				// Add it to the assign list
				bbcToAssign.Add(partialBbc);

				// Update the original contract
				bbc.UpdateBalance(remainingAmount, bbc.Balance < 0);

				// Save the new bucket balance contract
				await _bucketBalanceContractRepository.InsertAsync(partialBbc);

				break; // We've reached our threshold
			}
		}

		// Now assign all contributing contracts to this market transaction
		foreach (var bbc in bbcToAssign)
		{
			bbc.UpdateMarketTransaction(futuresTransaction.Id);
			await _logWorkflow.AddLog("Hedging", $"Assigned contract {bbc.ContractId} with balance {bbc.Balance} to market transaction {futuresTransaction.Id}");
		}
	}

	async Task<HedgingResultDto> ProcessContractAsDoNotHedge(Contract contract, decimal quantity, bool isSell, BucketBalance balance, bool isCancel = false, bool isEditUp = false, bool isReverse = false)
	{
		await _logWorkflow.AddLog("Hedging", "Working with DNH restrictions, initial balance :{0}", balance.BalanceValue.Value);
		var isReverseProcess = IsReverseProcess(contract, isSell);
		balance.CalculateBalanceWithoutUpdate(quantity, isSell, contract.Id, Operation.ToString(), true, false, false, contract.LocationId, isReverseProcess, contract.DeliveryStartDate,
			contract.FuturesMonth, true, contract.RegionId);
		balance.UpdateBalance(quantity, isSell);
		if (contract.IsBookedPartialFill())
		{
			var commodity = await _commodityRepository.GetById(contract.CommodityId);
			var lotInformation = new Lot(quantity, commodity.LotFactor);
			var accountId = await _hedgeAccountHelper.GetAccountId(commodity.Id, contract.RealCropYear, contract.ContractTypeId, contract.RegionId);
			var cqgInstrument = await _hedgeMappingHelper.GetMarketInstrument(contract.CommodityId, contract.RealCropYear, contract.ContractTypeId, !isSell, contract.DeliveryStartDate, true, contract.FuturesMonth, contract.RegionId);
			var futuresMonth = cqgInstrument.Value;
			var instrument = cqgInstrument.Key;

			balance.UpdateBalance(lotInformation.Remainig, isSell);
			var bucketBalanceContract = new BucketBalanceContract(
				contract.Id,
				balance.Id
			);
			bucketBalanceContract.UpdateBalance(lotInformation.Remainig, isSell);
			await _bucketBalanceContractRepository.InsertAsync(bucketBalanceContract);

			var futuresTransaction = new MarketTransaction(contract.InternalCode,
				lotInformation.TotalQuantityByLots, lotInformation.Lots, commodity.Id, commodity.Name, instrument,
				futuresMonth, contract.FuturesPrice ?? 0, contract.SimulatePrice(), contract.Id, null, !isSell,
				commodity.LotFactor, true, null, EMarketTransactionType.Market, EMarketTransactionSource.Contract, contract.RealCropYear,
				accountId, contract.PassFill);
			futuresTransaction.CreateLedgerEvent(balance.BalanceValue.Value,
				ETransactionEvent.Create.ToString(), ETransactionType.Hedge.ToString(), contract.RegionId);
			futuresTransaction.SetInactive();
			await _futureTransactionBucketRepository.InsertAsync(futuresTransaction);
		}
		else
		{
			if (contract.OfferId.HasValue && !isCancel) 
			{
				// Ticket 7232: when user UNDO a contract, isCancel is false, and isReverse is true
				var addHedge = true;
				//rolled contracts
				if (contract.ChildCount > 0)
				{
					addHedge = !(_appDbContext.Set<Contract>().Local.Any(a => a.ParentId == contract.Id && a.Event == "Roll") || _appDbContext.Set<Contract>().Any(a => a.ParentId == contract.Id && a.Event == "Roll"));
				}

				var offer = await _appDbContext.Offers.FindAsync(contract.OfferId.Value);
				if ((offer?.InternalStatus.Value is EOfferInternalState.Booked or EOfferInternalState.LocalMonitored) || isEditUp || isReverse)
				{
					addHedge = false;
				}

				if (addHedge)
				{
					var commodity = await _commodityRepository.GetById(contract.CommodityId);
					var lotInformation = new Lot(quantity, commodity.LotFactor);
					balance.UpdateBalance(lotInformation.TotalQuantityByLots, !isSell);
					var futuresTransaction = await _futureTransactionBucketRepository.GetActualByOffer(contract.OfferId.Value);
					var transactionQuantity = futuresTransaction.Quantity;
					var transactionLots = futuresTransaction.Lots;
					futuresTransaction.ChangeQuantity(lotInformation.Lots, lotInformation.TotalQuantityByLots);
					futuresTransaction.CreateLedgerEvent(balance.BalanceValue.Value,
						ETransactionEvent.Create.ToString(), ETransactionType.Hedge.ToString(), contract.RegionId);
					futuresTransaction.ChangeQuantity(transactionLots, transactionQuantity);
					await _bucketBalanceContractRepository.AssignMarketTransaction(balance.Id, futuresTransaction.Id);
				}
			}
		}

		await _logWorkflow.AddLog("Hedging", "Working with DNH restrictions, balance at the end:{0}", balance.BalanceValue.Value);
		return new();
	}

	public async Task<ResultSet<HedgingResultDto>> ProcessContract(Contract contract, decimal quantity, bool isSell, bool omitHedgeMapping = false, bool isCancel = false, bool isAddition = false, bool isReverse = false)
	{
		var resultOfThisOperation = new ResultSet<HedgingResultDto>();
		var balance = await _bucketBalanceRepository.GetOrCreateBalance(contract.CommodityId, contract.RegionId, contract.RealCropYear);
		var doNotHedgeLog = await _doNotHedgeLogRepository.GetActiveLog(contract.CommodityId, contract.RealCropYear, contract.RegionId);
		if (doNotHedgeLog != null && doNotHedgeLog.IsActive)
		{
			resultOfThisOperation.OperationWasSuccessfull(await ProcessContractAsDoNotHedge(contract, quantity, isSell, balance, isCancel, isAddition, isReverse));
		}
		else
		{
			resultOfThisOperation.OperationWasSuccessfull(await ProcessContractAsHedging(contract, quantity, isSell, balance, false, omitHedgeMapping));
		}

		return resultOfThisOperation;
	}

	/// <summary>
	/// Process contract with a specific spread symbol instead of using hedge mapping
	/// </summary>
	public async Task<ResultSet<HedgingResultDto>> ProcessContract(Contract contract, decimal quantity, bool isSell, bool omitHedgeMapping = false, bool isCancel = false, bool isAddition = false, bool isReverse = false, string overrideInstrument = null)

	public async Task<HedgingResultDto> ProcessContractAsHedgingEFP(Contract contract, decimal quantity, decimal efpQuantity, BucketBalance balance, decimal? groupPrice = null)
	{
		await _logWorkflow.AddLog("EFP Hedging", "Started processing hedge logic");
		await _logWorkflow.AddLog("EFP Hedging", $"price quantity:{quantity}, EFPQuantity:{efpQuantity}, balance:{balance.Balance()}");

		MarketTransaction transactionToDoFutures = null;
		var bucketBalanceContract = new BucketBalanceContract(contract.Id, balance.Id);
		var commodity = await _commodityRepository.GetById(contract.CommodityId);

		await _logWorkflow.AddLog("EFP Hedging", "LiveLedger event for pricing");
		balance.CalculateBalanceWithoutUpdate(contract.Quantity, contract.IsSell, contract.Id, Operation.ToString(), false, false, false, contract.LocationId,
					false, contract.DeliveryStartDate, contract.FuturesMonth, true, contract.RegionId);
		balance.UpdateBalance(contract.Quantity, contract.IsSell);

		if (quantity == 0)
			return new HedgingResultDto();

		bucketBalanceContract.UpdateBalance(quantity - efpQuantity, contract.IsSell);
		await _logWorkflow.AddLog("EFP Hedging", "new balance after pricing:{0}", balance.BalanceValue.Value);
		await _logWorkflow.AddLog("EFP Hedging", "LiveLedger event for ERP");

		var resultOfThisOperation = new HedgingResultDto { HedgeableQuantityFromContract = efpQuantity };
		var lotInformation = new Lot(efpQuantity, commodity.LotFactor);
		var instrument = await _hedgeMappingHelper.GetMarketInstrument(contract.CommodityId, contract.FuturesMonth);

		var accountId = await _hedgeAccountHelper.GetAccountId(commodity.Id, contract.RealCropYear, contract.ContractTypeId, contract.RegionId);
		var contractPrice = groupPrice ?? contract.SimulatePrice();
		var transactionToDo = new MarketTransaction(contract.InternalCode, lotInformation.TotalQuantityByLots, lotInformation.Lots, commodity.Id, commodity.Name, instrument, //change for code
			contract.FuturesMonth, contract.FuturesPrice ?? 0, contractPrice, contract.Id, contract.OfferId, !contract.IsSell, commodity.LotFactor,
			true, null, EMarketTransactionType.Market, EMarketTransactionSource.Contract, contract.RealCropYear, accountId, contract.PassFill);
		transactionToDo.CreateLedgerEvent(balance.CalculateBalanceWithoutUpdate(efpQuantity, !contract.IsSell), ETransactionEvent.Create.ToString(), ETransactionType.Hedge.ToString(), contract.RegionId);

		await _futureTransactionBucketRepository.InsertAsync(transactionToDo);
		CloseMarketTransaction(transactionToDo, true);
		resultOfThisOperation.Transactions.Add(transactionToDo);
		_bucketBalanceRepository.Update(balance);
		balance.UpdateBalance(efpQuantity, !contract.IsSell);
		await _logWorkflow.AddLog("EFP Hedging", "new balance after pricing:{0}", balance.BalanceValue.Value);
		var doNotHedgeLog = await _doNotHedgeLogRepository.GetActiveLog(contract.CommodityId, contract.RealCropYear, contract.RegionId);
		if (doNotHedgeLog != null && doNotHedgeLog.IsActive)
		{
			await _logWorkflow.AddLog("EFP Hedging", "Closing operation by DNH active");
			return resultOfThisOperation;
		}

		//Threshold
		var balanceBeforeThreshold = balance.Balance();
		var balanceAbs = Math.Abs(balanceBeforeThreshold);
		if ((commodity.AccumulationThreshold.HasValue && balanceAbs >= commodity.AccumulationThreshold) || balanceAbs >= commodity.LotFactor)
		{
			resultOfThisOperation.HedgeableQuantityFromBalance = balanceAbs - commodity.LotFactor;
			var newBalanceToFutures = GetNewBalanceToFuture(balance, commodity);

			if (newBalanceToFutures.Lots > 0)
			{
				await _logWorkflow.AddLog("EFP Hedging", "Recalculated lot quantity Lots:{0} TotalQuantityByLots:{1} Remaining:{2}", newBalanceToFutures.Lots, newBalanceToFutures.TotalQuantityByLots, balance.BalanceValue.Value);
				var cqgInstrument = await _hedgeMappingHelper.GetMarketInstrumentForAccumulation(contract.CommodityId, contract.RealCropYear, !contract.IsSell, contract.FuturesMonth, contract.RegionId);
				accountId = await _hedgeAccountHelper.GetAccountId(commodity.Id, contract.RealCropYear, contract.ContractTypeId, contract.RegionId);
				var isSell = quantity - efpQuantity > 0 ? contract.IsSell : !contract.IsSell;  // change Buy/Sell direction if 'Maxinum Quantity' is selected at Font end
				transactionToDoFutures = new(contract.InternalCode, newBalanceToFutures.TotalQuantityByLots, newBalanceToFutures.Lots, commodity.Id, commodity.Name, cqgInstrument.Key,
					cqgInstrument.Value, contract.FuturesPrice ?? 0, contract.SimulatePrice(), contract.Id, contract.OfferId, !isSell, commodity.LotFactor, true, null,
					EMarketTransactionType.Market, EMarketTransactionSource.Accumulation, contract.RealCropYear, accountId, contract.PassFill);
				if (Source == ETransactionSource.Offer)
				{
					var offer = await _offerRepository.GetById((Guid)contract.OfferId);
					if (offer != null)
					{
						transactionToDoFutures.ChangeFuturesMonth(offer.FuturesMonth);
					}
				}
				if (newBalanceToFutures.Remainig != 0)
				{
					bucketBalanceContract.UpdateBalance(quantity - efpQuantity - newBalanceToFutures.Remainig, contract.IsSell);
				}
				bucketBalanceContract.UpdateMarketTransaction(transactionToDoFutures.Id);
				await _bucketBalanceContractRepository.AssignMarketTransaction(balance.Id, transactionToDoFutures.Id);
				transactionToDoFutures.CreateLedgerEvent(balance.Balance(), ETransactionEvent.Threshold.ToString(), ETransactionType.Hedge.ToString(), contract.RegionId);
				await _futureTransactionBucketRepository.InsertAsync(transactionToDoFutures);
				resultOfThisOperation.Transactions.Add(transactionToDoFutures);

				if (newBalanceToFutures.Remainig != 0)
				{
					var residualBucketBalanceContract = new BucketBalanceContract(
						contract.Id,
						balance.Id
					);
					residualBucketBalanceContract.UpdateBalance(Math.Abs(newBalanceToFutures.Remainig), contract.IsSell);
					await _bucketBalanceContractRepository.InsertAsync(residualBucketBalanceContract);
				}
			}
		}
		await _bucketBalanceContractRepository.InsertAsync(bucketBalanceContract);
		if (transactionToDoFutures != null)
		{
			var byPass = await _settingRepository.GetAbsoluteValue<bool>(TenantSettingsDictionary.ByPass);
			if (byPass)
			{
				await _logWorkflow.AddLog("EFP Hedging", "IsAutomaticFuturesEnabled:true");
				await _logWorkflow.AddLog("EFP Hedging", "Processing bushels");
				transactionToDoFutures.Approved();
			}
			else
			{
				await _logWorkflow.AddLog("EFP Hedging", "IsAutomaticFuturesEnabled:false");
			}
		}

		await _logWorkflow.AddLog("EFP Hedging", "Current balance:{0}", balance.ToString());
		return resultOfThisOperation;
	}

	Lot GetNewBalanceToFuture(BucketBalance balance, Commodity commodity)
	{
	    var tenantContext = ContextFactory.CreateTenantContext(_appDbContext.TenancyContext.Tenant.Id.ToString(), _appDbContext.TenancyContext.Tenant.DisplayName);
	    
	    if (_ldClient.BoolVariation(FeatureFlags.EnableHedgingThreshold, tenantContext) && 
	        commodity.AccumulationThreshold.HasValue)
	    {
	        var originalBalance = balance.Balance();
	        
	        if (Math.Abs(originalBalance) >= commodity.AccumulationThreshold.Value)
	        {
				return balance.CalculateThresholdFutureLots(
					commodity.LotFactor, 
					commodity.AccumulationThreshold.Value);
	        }
	    }
	    
	    // Fall back to original logic if threshold feature is disabled or not applicable
	    return balance.CalculateFuturesLots(commodity.LotFactor);
	}
}
