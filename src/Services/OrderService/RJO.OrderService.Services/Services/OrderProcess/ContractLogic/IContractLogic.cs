using RJO.OrderService.Domain;
using RJO.OrderService.Services.Enumeration;

namespace RJO.OrderService.Services.Services.OrderProcess.ContractLogic;

public interface IContractLogic
{
	void DefineOperation(ETransactionEvent operation, ETransactionSource source);
}

public interface IContractLogic<T> : IContractLogic
{
	Task<ResultSet<T>> ProcessContract(Contract contract);
}

public interface IContractHedgingLogic<T> : IContractLogic
{
	Task<ResultSet<T>> ProcessContract(Contract contract, decimal quantity, bool isSell, bool omitHedgeMapping = false, bool isCancel = false, bool isAddition = false, bool isReverse = false, string overrideInstrument = null);
}
