using LaunchDarkly.Sdk.Server;
using Microsoft.EntityFrameworkCore;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Persistence.Repositories.Logs;
using RJO.OrderService.Services.DTO.Contract;
using RJO.OrderService.Services.DTO.ResultSets;
using RJO.OrderService.Services.Enumeration;
using RJO.OrderService.Services.Helper;
using RJO.OrderService.Services.Services.OrderProcess.ContractLogic;
using ETransactionEvent = RJO.OrderService.Services.Enumeration.ETransactionEvent;

namespace RJO.OrderService.Services.Services.OrderProcess;

public abstract class ContractWorkflowStrategy
{
	protected const byte HedgingLogic = 1;
	protected const byte DoNotHedgeLogic = 2;
	protected const byte ERPIntegration = 4;
	protected const byte SpotTradeLogic = 5;
	protected IDictionary<byte, IContractLogic> HedgingLogics;
	protected readonly LogWorkflowRepository LogWorkflow;
	protected readonly BucketBalanceRepository BucketBalanceRepository;
	protected readonly ContractRepository ContractRepository;
	protected readonly Persistence.Repositories.Historical.ContractRepository HistoricalContractRepository;
	protected readonly OfferRepository OfferRepository;
	protected readonly BucketBalanceContractRepository BucketBalanceContractRepository;
	protected readonly AppDbContext _appDbContext;
	public string Name { get; private set; }
	public ETransactionEvent Operation { get; protected set; }

	protected ContractWorkflowStrategy(
		MarketTransactionRepository futureTransactionBucketRepository,
		BucketBalanceRepository bucketBalanceRepository,
		SettingRepository settingRepository,
		DoNotHedgeLogRepository doNotHedgeLogRepository,
		ContractRepository contractRepository,
		CommodityRepository commodityRepository,
		LogWorkflowRepository logWorkflow,
		HedgeMappingHelper hedgeMappingHelper,
		BucketBalanceContractRepository bucketBalanceContractRepository,
		HedgeAccountHelper hedgeAccountHelper,
		OfferRepository offerRepository,
		Persistence.Repositories.Historical.ContractRepository historicalContractRepository,
		ErpIntegrationAgent erpAgent,
		string name,
		AppDbContext appDbContext,
		LdClient ldClient)
	{
		_appDbContext = appDbContext;
		LogWorkflow = logWorkflow;
		BucketBalanceRepository = bucketBalanceRepository;
		ContractRepository = contractRepository;
		HistoricalContractRepository = historicalContractRepository;
		BucketBalanceContractRepository = bucketBalanceContractRepository;
		Name = name;
		OfferRepository = offerRepository;
		HedgingLogics = new Dictionary<byte, IContractLogic>(4)
		{
			{
				HedgingLogic, new HedgingLogic(futureTransactionBucketRepository, bucketBalanceRepository,
					settingRepository, doNotHedgeLogRepository, commodityRepository, logWorkflow, hedgeMappingHelper,
					bucketBalanceContractRepository, hedgeAccountHelper, offerRepository, appDbContext, ldClient)
			},
			{ DoNotHedgeLogic, new DoNotHedgeLogic(doNotHedgeLogRepository, bucketBalanceRepository, logWorkflow, appDbContext) },
			{ ERPIntegration, new ErpIntegrationLogic(logWorkflow, erpAgent) },
			{ SpotTradeLogic, new SpotTradeLogic(bucketBalanceRepository, logWorkflow) }
		};
	}

	public async Task<CreateContractResultDto> UpdateBucketWithPreHedge(Contract contract, decimal remaining)
	{
		var result = new CreateContractResultDto();
		var hedgingLogic = GetLogic<HedgingLogic>(HedgingLogic);
		var hedgingResultDto = new HedgingResultDto();
		var commodity = await _appDbContext.Commodities
			.AsNoTracking()
			.FirstOrDefaultAsync(x => x.Id == contract.CommodityId);
		var lotInformation = new Lot(remaining, commodity.LotFactor);
		var balance = await _appDbContext.BucketBalance
			.FirstOrDefaultAsync(x => x.CommodityId == contract.CommodityId && x.RegionId == contract.RegionId && x.CropYear == contract.RealCropYear);
		if (balance == null)
			balance = new(contract.CommodityId, contract.RegionId, contract.RealCropYear);

		var hedgeResultDto = await hedgingLogic.CreateHedgeForBucketBalance(contract, lotInformation, commodity, contract.IsSell, balance, false, hedgingResultDto, true);
		var resultOfThisOperation = new ResultSet<HedgingResultDto>();
		resultOfThisOperation.OperationWasSuccessfull(hedgeResultDto);
		result.Transactions = resultOfThisOperation.Data.Transactions;

		return result;
	}

	public async Task<CreateContractResultDto> UpdateBucketForThresholdChange(Contract contract, Commodity commodity, BucketBalance balance)
	{
		var result = new CreateContractResultDto();
		var hedgingLogic = GetLogic<HedgingLogic>(HedgingLogic);
		var hedgingResultDto = new HedgingResultDto();
		var lotInformation = new Lot(balance.Balance(), commodity.LotFactor);
		var hedgeResultDto = await hedgingLogic.CreateHedgeForBucketBalance(contract, lotInformation, commodity, lotInformation.IsToSell, balance, false, hedgingResultDto, true, false);
		var resultOfThisOperation = new ResultSet<HedgingResultDto>();
		resultOfThisOperation.OperationWasSuccessfull(hedgeResultDto);
		result.Transactions = resultOfThisOperation.Data.Transactions;

		return result;
	}

	public abstract Task<CreateContractResultDto> ProcessContract(Contract contract);
	public abstract Task<CancelQuantityResultDto> CancelQuantityProcess(Contract contract, decimal cancelQuantity, bool transferCancelQuantityToParent = false, string comments = null);
	public abstract Task<ChangeQuantityResultDto> QuantityChangeProcess(Contract contract, decimal newQuantity, bool isCancel = false);
	public abstract Task ProcessPassTheFill(Contract contract, decimal futurePrice);
	public abstract Task ProcessErpIntegration(Contract contract, string contractNumber);
	public abstract Task<RollingResultDto> RollProcess(Contract contract, decimal rollQuantity, short cropYear, decimal? futuresPrice, DateTime deliveryStartDate, DateTime deliveryEndDate, Guid employeeId, string futuresMonth);
	public abstract Task<PricingResultDto> PriceProcess(Contract contract, decimal pricingQuantity, bool passTheFill, decimal? futuresPrice, DateTime deliveryStartDate, DateTime deliveryEndDate, Guid employeeId, string futuresMonth);
	public abstract Task<RollingHedgeResultDto> RollingHedge(Contract contract, short newCropYear, string futureMonth, DateTime newDeliveryStartDate, DateTime newDeliveryEndDate);
	public abstract Task<Offer> PriceContractViaOffer(Contract contract, decimal priceQuantity, bool gtc, DateTime? expiration, decimal? postedBasis, decimal? pushBasis, decimal? futuresPrice, Guid employeeId);
	public abstract Task<FillPriceContractViaOfferResultDto> FillPriceContractViaOffer(Contract contract, Offer offer, Contract childContract);
	public abstract Task<HedgingResultDto> CreateHedgeForEFPContract(Contract contract, decimal quantity, decimal efpQuantity, decimal price);
	public abstract Task<PriceContractViaEFPResultDto> PriceContractViaEFP(Contract contract, ContractViaEFPDto efp);
	public abstract Task<Contract> ApplyNameIdProcess(Contract contract, decimal applyQuantity, decimal? freightPrice, decimal? fees1, decimal? fees2);
	protected T GetLogic<T>(byte logicIndex) where T : IContractLogic => (T)HedgingLogics[logicIndex];

	internal void DefineOperation(ETransactionEvent operation, ETransactionSource source)
	{
		Operation = operation;
		foreach (var key in HedgingLogics.Keys)
		{
			HedgingLogics[key].DefineOperation(Operation, source);
		}
	}

	internal static bool IsApplyNameIdOperation(Contract contract) => contract.ParentId.HasValue && contract.Event == EContractEvent.ApplyNameId.ToString();

	#region Rolling Hedge

	protected async Task<RollingHedgeResultDto> RollingHedge(decimal rollQuantity, Contract rollContract, Contract editContract, bool operation, ETransactionEvent firstOperation, ETransactionEvent secondOperation,
		bool omitHedgeMappingOnFirtsOperation = false, bool omitHedgeMappingOnSecondOperation = false)
	{
		var result = new RollingHedgeResultDto();
		var hedgingLogic = GetLogic<HedgingLogic>(HedgingLogic);

		// Check if we can use a spread order for rolling operations
		if (firstOperation == ETransactionEvent.Roll && secondOperation == ETransactionEvent.Create &&
			CanUseSpreadOrder(rollContract, editContract))
		{
			// Create a single spread transaction instead of two separate transactions
			var spreadSymbol = GetSpreadSymbol(rollContract.FuturesMonth, editContract.FuturesMonth);
			if (!string.IsNullOrEmpty(spreadSymbol))
			{
				// Create single spread transaction using existing hedging logic but with spread symbol
				rollContract.ChangeFuturesMonth(rollContract.FuturesMonth);
				Operation = firstOperation;
				hedgingLogic.DefineOperation(firstOperation, ETransactionSource.Contract);

				if (rollContract.ContractTypeId != ContractTypeDictionary.NTC)
				{
					var resultSpread = await hedgingLogic.ProcessContract(rollContract, rollQuantity, !operation, omitHedgeMappingOnFirtsOperation, false, false, false, spreadSymbol);
					resultSpread.ValidateResult("Hedging for spread roll process was not successful");
					result.Transactions.AddRange(resultSpread.Data.Transactions);
				}

				return result;
			}
		}

		// Fall back to existing logic for separate transactions
		rollContract.ChangeFuturesMonth(rollContract.FuturesMonth);
		Operation = firstOperation;
		hedgingLogic.DefineOperation(firstOperation, ETransactionSource.Contract);
		if (rollContract.ContractTypeId != ContractTypeDictionary.NTC)
		{
			var resultParent = await hedgingLogic.ProcessContract(rollContract, rollQuantity, !operation, omitHedgeMappingOnFirtsOperation);
			resultParent.ValidateResult("Hedging for roll process was not successful for original contract");
			result.Transactions.AddRange(resultParent.Data.Transactions);
		}

		Operation = secondOperation;
		Thread.Sleep(1);
		hedgingLogic.DefineOperation(secondOperation, ETransactionSource.Contract);
		if (editContract.ContractTypeId != ContractTypeDictionary.NTC)
		{
			var resultChild = await hedgingLogic.ProcessContract(editContract, rollQuantity, operation, omitHedgeMappingOnSecondOperation);
			resultChild.ValidateResult("Hedging for roll process was not successful for child contract");
			result.Transactions.AddRange(resultChild.Data.Transactions);
		}

		return result;
	}

	/// <summary>
	/// Determines if a spread order can be used for rolling contracts
	/// </summary>
	private bool CanUseSpreadOrder(Contract oldContract, Contract newContract)
	{
		// Use spread orders for HTA and NTC contracts when rolling between different futures months
		// of the same commodity and crop year
		return oldContract.CommodityId == newContract.CommodityId &&
			   oldContract.RealCropYear == newContract.RealCropYear &&
			   oldContract.FuturesMonth != newContract.FuturesMonth &&
			   (oldContract.ContractTypeId == ContractTypeDictionary.HTA || oldContract.ContractTypeId == ContractTypeDictionary.NTC);
	}

	/// <summary>
	/// Gets the spread symbol for the given old and new futures months
	/// Hardcoded for now - will be replaced with database lookup
	/// </summary>
	private string GetSpreadSymbol(string oldFuturesMonth, string newFuturesMonth)
	{
		// Hardcoded spread symbols as requested
		// ZCES1N25 to buy 1 ZCEN25 and sell 1 ZCEU25
		// ZCES2N25 to buy 1 ZCEN25 and sell 1 ZCEZ25

		if (oldFuturesMonth == "N25" && newFuturesMonth == "U25")
			return "F.US.ZCES1N25";

		if (oldFuturesMonth == "N25" && newFuturesMonth == "Z25")
			return "F.US.ZCES2N25";

		// Return null if no spread symbol is available - will fall back to separate transactions
		return null;
	}

	#endregion
}
