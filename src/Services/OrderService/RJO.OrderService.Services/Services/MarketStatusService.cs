using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.BusinessExceptions;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.Settings;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Services.DTO;
using RJO.OrderService.Services.Helper;
using System.Net;

namespace RJO.OrderService.Services.Services;

public class MarketStatusService
{
	readonly CQGSettings _cqgSettings;
	readonly Settings _settings;
	readonly HighAndLowSnapshotRepository _highAndLowSnapshotRepository;
	readonly ILogger<MarketStatusService> _logger;
	readonly IHttpClientFactory _httpClientFactory;
	readonly DbSet<GlobexSchedule> _scheduleDbSet;

	public MarketStatusService(CQGSettings cqgSettings, IOptions<Settings> settings, HighAndLowSnapshotRepository highAndLowSnapshotRepository, ILogger<MarketStatusService> logger, IHttpClientFactory httpClientFactory, AppDbContext appDbContext)
	{
		_cqgSettings = cqgSettings;
		_settings = settings.Value;
		_highAndLowSnapshotRepository = highAndLowSnapshotRepository;
		_logger = logger;
		_httpClientFactory = httpClientFactory;
		_scheduleDbSet = appDbContext.GlobexSchedules;
	}

	static string GetSecurityGroup(string instrument)
	{
		if (string.IsNullOrEmpty(instrument) || instrument.Length < 8)
			return null;
		return instrument.Substring(5, 3);
	}

	ScheduleInfo GetScheduleInfoWhenOpen(string instrument, DateTime searchTime, string nextStatus)
	{
		var secGroup = GetSecurityGroup(instrument);
		if (string.IsNullOrEmpty(secGroup))
			return null;

		var latestSession = _scheduleDbSet.AsQueryable()
			.Where(x => x.SecurityGroup == secGroup && x.StartTime <= searchTime)
			.OrderByDescending(x => x.StartTime)
			.FirstOrDefault();
		if (latestSession == null)
			return null;

		GlobexSchedule session = default;
		var possibleSessions = _scheduleDbSet.AsQueryable()
			.Where(x => x.SecurityGroup == secGroup && x.StartTime == latestSession.StartTime).ToList();
		session = possibleSessions.Find(x => x.TradingStatus == ExchangeTradingStatus.Open || x.TradingStatus == nextStatus); // open
		if (session != null)
		{
			var sessionTime = session.StartTime.RoundUp(TimeSpan.FromMinutes(1));
			return new()
			{
				Session = session.SessionName,
				IsScheduled = false,
				Offset = DateTime.SpecifyKind(sessionTime, DateTimeKind.Utc)
			};
		}

		var nextSession = _scheduleDbSet.AsQueryable()
			.Where(x => x.SecurityGroup == secGroup && x.StartTime > searchTime && (x.TradingStatus == ExchangeTradingStatus.Open || x.TradingStatus == nextStatus))
			.OrderBy(x => x.StartTime)
			.FirstOrDefault();
		if (nextSession == null)
			return null;

		var tm = nextSession.StartTime.RoundUp(TimeSpan.FromMinutes(1));
		return new()
		{
			Session = nextSession.SessionName,
			IsScheduled = true,
			Offset = DateTime.SpecifyKind(tm, DateTimeKind.Utc)
		};
	}

	bool IsMarketOpenAtTime(string instrument, DateTime schTime)
	{
		var secGroup = GetSecurityGroup(instrument);
		if (string.IsNullOrEmpty(secGroup))
			return false;

		var latestSession = _scheduleDbSet.AsQueryable()
			.Where(x => x.SecurityGroup == secGroup && x.StartTime <= schTime)
			.OrderByDescending(x => x.StartTime)
			.FirstOrDefault();
		if (latestSession == null)
			return false;

		GlobexSchedule session = default;
		var possibleSessions = _scheduleDbSet.AsQueryable()
			.Where(x => x.SecurityGroup == secGroup && x.StartTime == latestSession.StartTime).ToList();
		session = possibleSessions.Find(x => x.TradingStatus == ExchangeTradingStatus.Open || x.TradingStatus == ExchangeTradingStatus.PreOpen);
		return session != null;
	}

	public async Task<ScheduleInfo> GetStatusForSymbol(string instrument, string type, string source = "", DateTime? searchTime = null)
	{
		var schTime = DateTime.UtcNow;
		if (searchTime != null)
			schTime = DateTime.SpecifyKind(searchTime.Value, DateTimeKind.Utc);

		if (!string.IsNullOrEmpty(source) && source == DataSource.CQG)
			return await GetStatusForSymbolCqg(instrument, type, schTime);

		ScheduleInfo rtn;
		rtn = GetScheduleInfoWhenOpen(instrument, schTime, ExchangeTradingStatus.Open);
		//if (type == "market")
		//    rtn = GetScheduleInfoWhenOpen(instrument, schTime, ExchangeTradingStatus.Open);
		//else
		//    rtn = GetScheduleInfoWhenOpen(instrument, schTime, ExchangeTradingStatus.PreOpen);

		return rtn;
	}

	public async Task<Dictionary<string, bool>> IsMarketOpen(IList<string> instruments, string source = "", DateTime? searchTime = null)
	{
		if (!string.IsNullOrEmpty(source) && source == DataSource.CQG)
			return await IsMarketOpenCqg(instruments);

		var schTime = DateTime.UtcNow;
		if (searchTime != null)
			schTime = DateTime.SpecifyKind(searchTime.Value, DateTimeKind.Utc);

		var dict = new Dictionary<string, bool>();
		foreach (var instrument in instruments)
		{
			var isMarketOpen = IsMarketOpenAtTime(instrument, schTime);
			dict.Add(instrument, isMarketOpen);
		}

		return dict;
	}

	public async Task<bool> IsMarketOpen(string instrument, string source = "", DateTime? searchTime = null)
	{
		if (!string.IsNullOrEmpty(source) && source == DataSource.CQG)
			return await IsMarketOpenCqg(instrument);

		var schTime = DateTime.UtcNow;
		if (searchTime != null)
			schTime = DateTime.SpecifyKind(searchTime.Value, DateTimeKind.Utc);

		return IsMarketOpenAtTime(instrument, schTime);
	}

	public async Task<ScheduleInfo> GetStatusForSymbolCqg(string instrument, string type, DateTime? searchTime = null)
	{
		if (string.IsNullOrEmpty(instrument) || instrument.Length < 4) return null; //TODO: use regex to find a optimal way to validate a full instrument symbol
		_logger.LogInformation("{ServiceName}: Getting market status from instrument {Instrument} from CQG", nameof(MarketStatusService), instrument);
		try
		{
			var uriString = $"{_cqgSettings.WebPath}/SessionInformation/MarketStatus?instrument={instrument}&type={type}";
			if (searchTime != null)
				uriString += $"&dtime={searchTime.Value}";

			var httpClient = _httpClientFactory.CreateClient();
			var result = await httpClient.GetAsync(uriString);

			if (!result.IsSuccessStatusCode)
			{
				_logger.LogWarning("{ServiceName}: Result from calling market status with instrument: {Instrument} and order type: {OrderType} is {StatusCode} with message {ReasonPhrase}",
									nameof(MarketStatusService), instrument, type, result.StatusCode, result.ReasonPhrase);
				throw result.StatusCode switch
				{
					HttpStatusCode.BadRequest => new InvalidArgumentException(
						$"An error occured: {result.ReasonPhrase}"),
					HttpStatusCode.InternalServerError => new InternalServerErrorException(
						$"CQG service failed to retrieve market status with: {result.ReasonPhrase}"),
					HttpStatusCode.ServiceUnavailable => new CqgClientNotReadyException(
						"CQG client is not available at this moment"),
					_ => new HttpResponseNotExpectedException(
						$"Request failed with error code {result.StatusCode}")
				};
			}

			var json = await result.Content.ReadAsStringAsync();
			AssertionConcern.ArgumentIsNotNullOrEmpty(json, $"{nameof(MarketStatusService)}: {instrument} response is empty or not found");
			var scheduleInfo = JsonConvert.DeserializeObject<ScheduleInfo>(json);
			return scheduleInfo;
		}
		catch (Exception ex)
		{
			_logger.LogError("{ServiceName}: Getting market status from instrument {Instrument} error: {ErrorMessage}", nameof(MarketStatusService), instrument, ex.Message);
			throw;
		}
	}

	public async Task<Dictionary<string, bool>> IsMarketOpenCqg(IList<string> instruments)
	{
		var marketStatusesBySymbol = new Dictionary<string, bool>();
		var peridot = _settings.Cqg.GetHighLowsUnitOrDefault;
		var utcNow = DateTime.UtcNow;
		var filterDate = utcNow.AddMinutes(peridot * -2);
		if (instruments == null || instruments.Count == 0)
		{
			return marketStatusesBySymbol;
		}

		var query = (await _highAndLowSnapshotRepository.GetAllEntities()).Where(x => 1 == 0);
		instruments = instruments.Distinct().ToList();
		foreach (var instrument in instruments)
		{
			query.Union((await _highAndLowSnapshotRepository.GetAllEntities()).Where(x => x.Instrument == instrument && x.CreatedOn >= filterDate).OrderByDescending(x => x.CreatedOn).Take(1));
		}

		var data = query.ToDictionary(r => r.Instrument, s => s.IsMarketOpen);
		foreach (var instrument in instruments)
		{
			var isMarketOpen = data.ContainsKey(instrument) && data[instrument];
			marketStatusesBySymbol.Add(instrument, isMarketOpen);
		}

		return marketStatusesBySymbol;
	}

	public async Task<bool> IsMarketOpenCqg(string instrument)
	{
		var period = _settings.Cqg.GetHighLowsUnitOrDefault;
		var utcNow = DateTime.UtcNow;
		var filterDate = utcNow.AddMinutes(period * -2);
		if (string.IsNullOrEmpty(instrument))
		{
			return true;
		}

		var isMarketOpen = (await _highAndLowSnapshotRepository.GetAllEntities()).Where(x => x.Instrument == instrument && x.CreatedOn >= filterDate).OrderByDescending(x => x.CreatedOn).Select(x => x.IsMarketOpen).FirstOrDefault();
		return isMarketOpen;
	}

	public async Task<decimal?> GetSpread(string symbol)
	{
		if (string.IsNullOrEmpty(symbol))
			return null;

		var client = _httpClientFactory.CreateClient("SpreadService");
		var address = $"http://localhost:57695/Spread?symbol={symbol}";
		var result = await client.GetAsync(address);
		AssertionConcern.ArgumentIsNotTrue(!result.IsSuccessStatusCode, $"{nameof(MarketStatusService)}: {symbol} symbol information is unavailable");
		var json = await result.Content.ReadAsStringAsync();
		AssertionConcern.ArgumentIsNotNullOrEmpty(json, $"{nameof(MarketStatusService)}: {symbol} symbol information is unavailable");
		var spreatResult = JsonConvert.DeserializeObject<SpreadInfo>(json);
		if (spreatResult == null || spreatResult.SpreadPriceResponse == null)
		{
			throw new BusinessException("Spread information was not received");
		}

		if (spreatResult.SpreadPriceResponse.ErrorMessage == null || spreatResult.SpreadPriceResponse.ErrorMessage.HasError)
		{
			throw new BusinessException("Getting the spread information throw and error: " + spreatResult.SpreadPriceResponse.ErrorMessage?.Message ?? "Error information not found");
		}

		var value = spreatResult.SpreadPriceResponse.SpreadPrices?.FuturesPrice;
		if (value.HasValue)
		{
			return Convert.ToDecimal(value);
		}

		return null;
	}
}
