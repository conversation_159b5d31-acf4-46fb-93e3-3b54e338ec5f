using RJO.OrderService.Domain.Enumeration;

namespace RJO.OrderService.Services.DTO.Import.Contract;

class ContractDto
{
	public Guid ContractTypeId { get; set; }
	public Guid ExtendedContractTypeId { get; set; }
	public string Number { get; set; }
	public string ParentContractNumber { get; set; }
	public bool HasParent => !string.IsNullOrEmpty(ParentContractNumber);
	public EContractEvent Event { get; set; }
	public Guid TransactionTypeId { get; set; }	
	public bool IsSell { get; set; }
	public Guid CommodityId { get; set; }
	public Guid LocationId { get; set; }
	public Guid? DeliveryLocationId { get; set; }
	public short CropYear { get; set; }
	public DateTime DeliveryStartDate { get; set; }
	public DateTime DeliveryEndDate { get; set; }
	public decimal Quantity { get; set; }
	public decimal? FuturesPrice { get; set; }
	public decimal? PostedBasis { get; set; }
	public decimal? PushBasis { get; set; }
	public decimal FreightPrice { get; set; }
	public decimal Fees1 { get; set; }
	public decimal Fees2 { get; set; }
	public Guid CustomerId { get; set; }
	public Guid EmployeeId { get; set; }
	public string Comments { get; set; }
	public string FuturesMonth { get; set; }
	public string RegionCode { get; set; }
	public bool IsInvalid { get; set; }
	public string FormatDataImport { get; set; }
	public Domain.Contract Contract { get; set; }
	public List<ContractDto> Childs { get; }
	public ContractDto() => Childs = new();
}
