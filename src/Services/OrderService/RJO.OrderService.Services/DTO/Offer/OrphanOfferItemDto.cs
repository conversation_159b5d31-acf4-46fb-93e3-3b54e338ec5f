namespace RJO.OrderService.Services.DTO.Offer;

public class OrphanOfferItemDto
{
	public Guid Id { get; init; }
	public OrphanOfferCustomerItemDto Customer { get; init; }
	public string Commodity { get; init; }
	public short CropYear { get; init; }
	public short RealCropYear { get; init; }
	public string ContractType { get; init; }
	public string Location { get; init; }
	public decimal Quantity { get; init; }
	public OrphanOfferBasisItemDto Basis { get; init; }
	public OrphanOfferPriceItemDto Price { get; init; }
	public string FutureMonth { get; init; }
}

public class OrphanOfferCustomerItemDto
{
	public string FirstName { get; init; }
	public string LastName { get; init; }
	public string Number { get; init; }
}

public class OrphanOfferBasisItemDto
{
	public decimal? Posted { get; init; }
	public decimal? Push { get; init; }
	public decimal? Net { get; init; }
}

public class OrphanOfferPriceItemDto
{
	public decimal Cash { get; init; }
	public decimal Freight { get; init; }
	public decimal? Futures { get; init; }
	public decimal[] Fees { get; init; }
}
