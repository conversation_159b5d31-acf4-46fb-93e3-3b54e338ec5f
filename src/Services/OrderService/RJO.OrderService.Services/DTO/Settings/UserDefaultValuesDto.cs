namespace RJO.OrderService.Services.DTO.Settings;

public class UserDefaultValuesDto
{
	public bool? IsSell { get; set; }
	public Guid? ContractTypeId { get; set; }
	public Guid? LocationId { get; set; }
	public Guid? DeliveryLocationId { get; set; }
	public Guid? CommodityId { get; set; }
	public short? CropYear { get; set; }
	public DeliveryDateDto DeliveryPeriod { get; set; }
	public string FuturesMonth { get; set; }
	public Guid? TransactionTypeId { get; set; }
	public bool? IsDeliveryDateCustom { get; set; }
	public Guid? EmployeeId { get; set; }

	public override string ToString() =>
		$"IsSell:{IsSell}, ContractTypeId:{ContractTypeId}, LocationId:{LocationId}, DeliveryLocationId:{DeliveryLocationId}, CommodityId:{CommodityId}, CropYear:{CropYear}, FuturesMonth:{FuturesMonth}, DeliveryPeriod:{DeliveryPeriod}, TransactionTypeId:{TransactionTypeId}, IsDeliveryDateCustom:{IsDeliveryDateCustom}";
}

public class DeliveryDateDto
{
	public DateTime Start { get; set; }
	public DateTime End { get; set; }
	public string FutureMonth { get; set; }
	public decimal? PostedBasis { get; set; }

	public override string ToString() => $"{Start:yyyy-MM-dd} - {End:yyyy-MM-dd} - {FutureMonth}";
}
