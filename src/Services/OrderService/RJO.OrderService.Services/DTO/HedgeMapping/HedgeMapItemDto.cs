namespace RJO.OrderService.Services.DTO.HedgeMapping;

public class HedgeMapItemDto
{
	public string Label { get; set; }
	public string FlatPriceP { get; set; }
	public string FlatPriceS { get; set; }
	public string HtaP { get; set; }
	public string HtaS { get; set; }
	public string BasisP { get; set; }
	public string BasisS { get; set; }
	public HedgeMapItemDto(string label) => Label = label;

	public override string ToString() => $"{Label}: FP-S:{FlatPriceS},FP-P:{FlatPriceP},HTA-S:{HtaS},HTA-P:{HtaP},Basis-S:{BasisS},Basis-P:{BasisP}";
}
