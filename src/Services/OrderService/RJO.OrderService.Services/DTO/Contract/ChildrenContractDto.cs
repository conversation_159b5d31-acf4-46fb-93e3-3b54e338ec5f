namespace RJO.OrderService.Services.DTO.Contract;

public class ChildrenContractDto
{
	public Guid Id { get; set; }
	public IList<Guid> LinkedOffers { get; set; }
	public DateTime CreatedOn { get; set; }
	public DateTime? UpdatedOn { get; set; }
	public ContractTypeItemDto Contract { get; set; }
	public bool IsSell { get; set; }
	public string TransactionType { get; set; }
	public string Event { get; set; }
	public EmployeeItemDto CreatedBy { get; set; }
	public string UpdatedBy { get; set; }
	public short CropYear { get; set; }
	public short RealCropYear { get; set; }
	public QuantityItemDto Quantity { get; set; }
	public string Status { get; set; }
	public PriceItemDto Price { get; set; }
	public BasisItemDto Basis { get; set; }
	public ContractRestrictionDto Restrictions { get; set; }
	public CustomerItemDto Customer { get; set; }
	public string Commodity { get; set; }
	public ContractParentItemDto Parent { get; set; }
	public DeliveryItemDto Delivery { get; set; }
	public bool ComesFromAnOffer { get; set; }
	public int Childs { get; set; }
	public bool PassFill { get; set; }

	public override string ToString() => $"Id:{Id}, Quantity:{Quantity}, CropYear:{CropYear}, Event:{Event}, Status:{Status}";
}
