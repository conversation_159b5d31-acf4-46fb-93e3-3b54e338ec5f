using Magneto;
using RJO.OrderService.Domain.Notifications;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.Features.Notifications.Notifications.Contracts.Responses;
using RJO.OrderService.Services.Features.Notifications.Notifications.Queries;

namespace RJO.OrderService.Services.Features.Notifications;

#nullable enable

public sealed class UserNotificationManager : AsyncCommand<(AppDbContext, IMagneto)>
{
	readonly UserNotificationDto _userToNotify;
	readonly UserType _userType;
	readonly string _inAppSubject;
	readonly string _emailSubject;
	readonly string _smsSubject;
	readonly string[] _inAppFormattedMessage;
	readonly string _emailFormattedMessage;
	readonly string _smsFormattedMessage;
	readonly EventType _eventType;
	readonly AggregateType _aggregateType;
	readonly Guid? _aggregateId;
	readonly bool _force;

	public UserNotificationManager(
		UserNotificationDto userToNotify,
		UserType userType,
		string subject,
		string messageContent,
		EventType eventType,
		AggregateType aggregateType,
		Guid? aggregateId = null,
		bool force = false)
		: this(userToNotify, userType, subject, subject, subject, new[] { messageContent }, messageContent, messageContent, eventType, aggregateType, aggregateId, force)
	{
	}

	public UserNotificationManager(
		UserNotificationDto userToNotify,
		UserType userType,
		string subject,
		string messageContent,
		string smsContent,
		EventType eventType,
		AggregateType aggregateType,
		Guid? aggregateId = null,
		bool force = false)
		: this(userToNotify, userType, subject, subject, subject, new[] { messageContent }, messageContent, smsContent, eventType, aggregateType, aggregateId, force)
	{
	}

	public UserNotificationManager(
		UserNotificationDto userToNotify,
		UserType userType,
		string inAppSubject,
		string emailSubject,
		string smsSubject,
		string[] inAppFormattedMessage,
		string emailFormattedMessage,
		string smsFormattedMessage,
		EventType eventType,
		AggregateType aggregateType,
		Guid? aggregateId = null,
		bool force = false)
	{
		_userToNotify = userToNotify;
		_userType = userType;
		_inAppSubject = inAppSubject;
		_emailSubject = emailSubject;
		_smsSubject = smsSubject;
		_inAppFormattedMessage = inAppFormattedMessage.Where(a => !string.IsNullOrEmpty(a)).ToArray();
		_emailFormattedMessage = emailFormattedMessage;
		_smsFormattedMessage = smsFormattedMessage;
		_eventType = eventType;
		_aggregateType = aggregateType;
		_aggregateId = aggregateId;
		_force = force;
	}

	public override async Task Execute((AppDbContext, IMagneto) context, CancellationToken cancellationToken)
	{
		if ((_inAppFormattedMessage == null || _inAppFormattedMessage.Length == 0) && string.IsNullOrWhiteSpace(_emailFormattedMessage) && string.IsNullOrWhiteSpace(_smsFormattedMessage))
			return;

		var dbContext = context.Item1;
		var magneto = context.Item2;

		NotificationSubscription? userSubscription = null;
		if (!_force)
		{
			userSubscription = await magneto.QueryAsync(
				new GetUserSubscriptionForEventType(
					_userType == UserType.Employee ? _userToNotify.Id : null,
					_userType == UserType.Customer ? _userToNotify.Id : null,
					_eventType),
				cancellationToken);
		}

		if (_force || (userSubscription != null && IsAnyNotificationChannelEnabled(userSubscription)))
		{
			var notification = new Notification(_eventType,
				_userType == UserType.Employee ? _userToNotify.Id : null,
				_userType == UserType.Customer ? _userToNotify.Id : null,
				null);
			dbContext.Notifications.Add(notification);

			CreateNotificationMessages(dbContext, userSubscription, notification.Id);
			await dbContext.SaveChangesAsync(cancellationToken);
		}
	}

	void CreateNotificationMessages(AppDbContext dbContext, NotificationSubscription? userSubscription, Guid notificationId)
	{
		if (_force || userSubscription?.IsInAppEnabled == true)
		{
			foreach (var message in _inAppFormattedMessage)
			{
				dbContext.NotificationMessages.Add(new(ChannelType.InApp, _aggregateType, _aggregateId, _inAppSubject, message, _userToNotify.Email, _userToNotify.PhoneNumber, notificationId));
			}
		}

		if (_force || userSubscription?.IsEmailEnabled == true)
		{
			dbContext.NotificationMessages.Add(new(ChannelType.Email, _aggregateType, _aggregateId, _emailSubject, _emailFormattedMessage, _userToNotify.Email, _userToNotify.PhoneNumber, notificationId));
		}

		if ((_force || userSubscription?.IsSmsEnabled == true) && !string.IsNullOrWhiteSpace(_userToNotify.PhoneNumber))
		{
			dbContext.NotificationMessages.Add(new(ChannelType.Sms, _aggregateType, _aggregateId, _smsSubject, _smsFormattedMessage, _userToNotify.Email, _userToNotify.PhoneNumber, notificationId));
		}
	}

	static bool IsAnyNotificationChannelEnabled(NotificationSubscription userSubscription) =>
		userSubscription.IsInAppEnabled || userSubscription.IsEmailEnabled || userSubscription.IsSmsEnabled;
}

public sealed record UserNotificationDto(Guid Id, string PhoneNumber, string Email);

public enum UserType
{
	Employee,
	Customer
}
