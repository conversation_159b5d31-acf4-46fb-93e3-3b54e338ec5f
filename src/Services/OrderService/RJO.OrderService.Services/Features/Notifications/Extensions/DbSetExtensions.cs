using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.CustomExceptions.BusinessExceptions;
using RJO.OrderService.Domain;

// ReSharper disable once CheckNamespace
namespace Microsoft.Extensions.DependencyInjection;

public static class DbSetExtensions
{
	public static async Task<Guid> GetEmployeeIdAsync(this DbSet<Employee> employees, string idOrEmail)
	{
		if (Guid.TryParse(idOrEmail, out var parsedId))
			return parsedId;

		var employee = await employees.FirstOrDefaultAsync(e => e.Email == idOrEmail);
		return employee?.Id ?? throw new InvalidArgumentException("Employee does not exist.");
	}
}
