using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.OrderService.Domain.Notifications;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.Features.Notifications.NotificationGroups.Contracts.Responses;
using RJO.OrderService.Services.Features.Notifications.NotificationGroups.Mapping;

namespace RJO.OrderService.Services.Features.Notifications.NotificationGroups.Queries;

public sealed record GetAllEmployeeNotificationGroupsQuery(bool IncludeSystemGroups = false) : IRequest<IReadOnlyCollection<NotificationGroupResponse>>;

public sealed class GetAllEmployeeNotificationGroupsQueryHandler : IRequestHandler<GetAllEmployeeNotificationGroupsQuery, IReadOnlyCollection<NotificationGroupResponse>>
{
	readonly AppDbContext _dbContext;

	public GetAllEmployeeNotificationGroupsQueryHandler(AppDbContext dbContext) => _dbContext = dbContext;

	public async Task<IReadOnlyCollection<NotificationGroupResponse>> Handle(GetAllEmployeeNotificationGroupsQuery request, CancellationToken cancellationToken)
	{
		var groups = await _dbContext.NotificationGroups
			.AsNoTracking()
			.Where(ng => ng.GroupType == NotificationGroupType.Employee)
			.Select(ng => ng.MapToResponse())
			.ToListAsync(cancellationToken);

		if (request.IncludeSystemGroups)
		{
			var systemGroups = await _dbContext.SystemNotificationGroups
				.AsNoTracking()
				.Include(sng => sng.Employees)
				.Select(sng => sng.MapToResponse())
				.ToListAsync(cancellationToken);

			groups.AddRange(systemGroups);
		}

		return groups
			.OrderByDescending(g => g.IsSystem)
			.ThenBy(g => g.Name)
			.ToList();
	}
}
