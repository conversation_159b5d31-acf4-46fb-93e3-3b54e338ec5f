using MediatR;
using Microsoft.Extensions.Logging;
using RJO.OrderService.Domain;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Services.DTO.Bidsheet;

namespace RJO.OrderService.Services.Handlers;

public class GetDeliveryDatesQuery : IRequest<BidsheetDeliveryDatesItemDto>
{
	public BidsheetDeliveryDatesFilterDto Filter { get; set; }
}

public class GetDeliverydatesHandler : IRequestHandler<GetDeliveryDatesQuery, BidsheetDeliveryDatesItemDto>
{
	readonly ILogger<GetDeliverydatesHandler> _logger;
	readonly BidsheetRepository _bidsheetRepository;
	readonly CommodityRepository _commodityRepository;

	public GetDeliverydatesHandler(ILogger<GetDeliverydatesHandler> logger, BidsheetRepository bidsheetRepository, CommodityRepository commodityRepository)
	{
		_bidsheetRepository = bidsheetRepository;
		_commodityRepository = commodityRepository;
		_logger = logger;
	}

	public async Task<BidsheetDeliveryDatesItemDto> Handle(GetDeliveryDatesQuery request, CancellationToken cancellationToken)
	{
		if (request == null || request.Filter == null)
		{
			return null;
		}

		var startCrop = request.Filter.CropYear.HasValue ? request.Filter.CropYear.Value : 2021;
		IEnumerable<Domain.Settings.Bidsheet> result = await _bidsheetRepository.GetAllEntities();
		var commodity = await _commodityRepository.GetById(request.Filter.CommodityId);
		var startDate = new DateTime(startCrop, commodity.CropStartMonth, commodity.CropStartDay);

		var lst = result.Where(t =>
			request.Filter.CommodityId == t.CommodityId &&
			(request.Filter.CropYear.HasValue ? t.CropYear == request.Filter.CropYear : true) &&
			t.DeliveryStart >= startDate &&
			t.DeliveryEnd >= DateTime.Now.Date &&
			t.DeliveryLocationId == request.Filter.LocationId
		).OrderBy(x => x.DeliveryStart).Select(x => new
		{
			x.DeliveryStart,
			x.DeliveryEnd,
			x.Delivery,
			x.FutureMonth,
			x.Basis,
			x.CropYear
		}).ToList();

		if (lst.Count == 0)
		{
			return CreateDefaultValue(commodity, (short)startCrop);
		}

		var dto = new BidsheetDeliveryDatesItemDto
		{
			Min = lst[0].DeliveryStart,
			Max = lst[lst.Count - 1].DeliveryEnd
		};
		foreach (var item in lst)
		{
			dto.DeliveryDates.Add(new()
			{
				End = item.DeliveryEnd,
				Start = item.DeliveryStart,
				FutureMonth = item.FutureMonth,
				Name = item.Delivery,
				Basis = item.Basis,
				CropYear = item.CropYear
			});
		}

		;
		return dto;
	}

	static BidsheetDeliveryDatesItemDto CreateDefaultValue(Commodity commodity, short cropYear) =>
		new()
		{
			Min = commodity.StartDate(cropYear),
			Max = commodity.EndDate(cropYear)
		};
}
