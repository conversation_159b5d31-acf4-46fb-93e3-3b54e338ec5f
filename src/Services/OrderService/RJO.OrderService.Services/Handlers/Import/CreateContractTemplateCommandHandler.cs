using MediatR;
using Microsoft.Extensions.Logging;
using ClosedXML.Excel;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.MultiTenancyServer.Core;
using RJO.OrderService.Domain.MultiTenancyServer;
using RJO.OrderService.Services.DTO.Import.Contract;
using RJO.OrderService.Persistence.Database;
using Microsoft.EntityFrameworkCore;

namespace RJO.OrderService.Services.Handlers.Import;

public class CreateImportTemplateCommand : IRequest<TemplateInfoDto>, ITransactionalRequest;


public class CreateContractTemplateCommandHandler(AppDbContext appDbContext, ITenancyContext<ApplicationTenant> tenancyContext, ILogger<CreateContractTemplateCommandHandler> logger) : IRequestHandler<CreateImportTemplateCommand, TemplateInfoDto>
{
    const string SHEETONENAME = "Catalogs";
    const string SHEETTOWNAME = "Contracts";
    const string DISCLAIMER = "Please fill out the second sheet with the contracts to upload, on this sheet we list the options you can select in each field. (1000 contracts maximum)";
    readonly string[] TITLESSHEETONE = [ "Contract Type", "Contract Types for Bushels Only", "Transaction Type", "Commodity", "Location", "Delivery Location", "Customer", "Employee", "Purchase/Sell" ];

    public static readonly string[] TITLESSHEETTWO =
    [
        "Contract Type",
        "Number",
        "Transaction Type",
        "Buy/Sell",
        "Commodity",
        "Location",
        "Delivery Location",
        "Crop Year",
        "Delivery Start Date",
        "Delivery End Date",
        "Quantity",
        "Futures Month",
        "Futures Price",
        "Posted Basis",
        "Push Basis",
        "Freight Price",
        "Fees 1",
        "Fees 2",
        "Customer",
        "Employee",
        "Comments",
        "Location Number",
        "Delivery Location Number",
        "Region Code"
    ];

    public async Task<TemplateInfoDto> Handle(CreateImportTemplateCommand request, CancellationToken cancellationToken)
    {
        var data = new TemplateInfoDto();
        data.TenantName = tenancyContext.Tenant.DisplayName;
		
		using (var workbook = new XLWorkbook())
        {
            await CreateCatalogsPage(workbook.AddWorksheet(SHEETONENAME));
            CreateContractPage(workbook.AddWorksheet(SHEETTOWNAME));
            using (var stream = new MemoryStream())
            {
                workbook.SaveAs(stream);
                data.Data = stream.ToArray();
            }
        }

        return data;
    }

    async Task CreateCatalogsPage(IXLWorksheet wSheet)
    {
        //Type
        FillColumn(wSheet, [ "Flat Price", "HTA", "Basis" ], 1);
		FillColumn(wSheet, await appDbContext.ExtendedContractTypes.Where(a => a.IsActive && a.TenantId == tenancyContext.Tenant.Id).Select(a => a.Name).ToListAsync(), 2);
		//Transaction Type
		FillColumn(wSheet, await appDbContext.TransactionTypes.Select(a => a.Name).ToListAsync(), 3);
        //Commodity
        FillColumn(wSheet, await appDbContext.Commodities.Select(a => a.Name).ToListAsync(), 4);
        //Location
        FillColumn(wSheet, await appDbContext.Locations.Where(a => a.IsLocation).Select(a => a.Name).ToListAsync(), 5);
        //Delivery Location
        FillColumn(wSheet, await appDbContext.Locations.Where(a => a.IsDestination).Select(a => a.Name).ToListAsync(), 6);
        //Customer
        FillColumn(wSheet, await appDbContext.Customers.Where(a => a.IsActive).Select(a => a.Number).ToListAsync(), 7);
        //Employee
        FillColumn(wSheet, await appDbContext.Employees.Where(a => a.IsActive).Select(a => a.Number).ToListAsync(), 8);
        //Buy/Sell
        FillColumn(wSheet, [ "Purchase or Buy", "Sell" ],9);
        //Titles
        CreateTitlesPageOne(wSheet);
        //Set Disclaimer
        wSheet.Cell(1, 1).Value = DISCLAIMER;
        var disclaimerCell = wSheet.Range(1, 1, 1, TITLESSHEETONE.Length);
        disclaimerCell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
        disclaimerCell.Style.Font.Bold = true;
        disclaimerCell.Style.Font.FontSize = 14;
        disclaimerCell.Style.Alignment.WrapText = true;
        disclaimerCell.Merge();
        wSheet.Row(1).Height = 40;
    }

    void CreateTitlesPageOne(IXLWorksheet wSheet)
    {
        for (var i = 0; i < TITLESSHEETONE.Length; i++)
        {
            SetAsTitle(wSheet.Cell(3, i + 1), TITLESSHEETONE[i]);
        }
    }

    static void FillColumn(IXLWorksheet wSheet, IList<string> data, int index)
    {
        for (var i = 0; i < data.Count; i++)
        {
            wSheet.Cell(4 + i, index).Value = data[i];
        }
    }

    static IXLCell SetAsTitle(IXLCell cell, string value)
    {
        cell.Value = value;
        return SetAsTitle(cell);
    }

    static IXLCell SetAsTitle(IXLCell cell)
    {
        cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
        cell.Style.Font.Bold = true;
        cell.WorksheetColumn().AdjustToContents();
        return cell;
    }

    static void CreateContractPage(IXLWorksheet wSheet)
    {
        for (var i = 0; i < TITLESSHEETTWO.Length; i++)
        {
            SetAsTitle(wSheet.Cell(1, i + 1), TITLESSHEETTWO[i]);
        }
    }
}
