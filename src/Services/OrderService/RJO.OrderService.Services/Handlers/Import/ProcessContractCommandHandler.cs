using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.BusinessExceptions;
using RJO.OrderService.Common;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.Settings;
using RJO.OrderService.Persistence;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Persistence.Repositories.Logs;
using RJO.OrderService.Services.DTO.Import.Contract;
using System.Globalization;
using System.Text;

namespace RJO.OrderService.Services.Handlers.Import;

public class ProcessImportTemplateCommand : IRequest<ImportResultDto>, ITransactionalRequest
{
	public ImportDto ImportInformation { get; set; }
	public bool HasBushelsOnlySetup { get; set; }
}

public class ProcessContractCommandHandler : IRequestHandler<ProcessImportTemplateCommand, ImportResultDto>
{
	readonly AppDbContext _dbContext;
	readonly ContractMigrationRepository _migrationLog;
	readonly ContractRepository _contractRepository;
	readonly SettingRepository _settingRepository;
	readonly TransactionTypeRepository _transactionTypeRepository;
	readonly CommodityRepository _commodityRepository;
	readonly CustomerRepository _customerRepository;
	readonly EmployeeRepository _employeeRepository;
	readonly UnitOfWork _unitOfWork;

	NumberStyles _styles = NumberStyles.AllowLeadingSign | NumberStyles.AllowDecimalPoint;
	CultureInfo _provider;
	const byte IndexContractType = 1;
	const byte IndexNumber = 2;
	const byte IndexTransactionType = 3;
	const byte IndexBuy_Sell = 4;
	const byte IndexCommodity = 5;
	const byte IndexLocation = 22;
	const byte IndexDeliveryLocation = 23;
	const byte IndexCropYear = 8;
	const byte IndexDeliveryStartDate = 9;
	const byte IndexDeliveryEndDate = 10;
	const byte IndexQuantity = 11;
	const byte IndexFuturesMonth = 12;
	const byte IndexFuturesPrice = 13;
	const byte IndexPostedBasis = 14;
	const byte IndexPushBasis = 15;
	const byte IndexFreightPrice = 16;
	const byte IndexFees1 = 17;
	const byte IndexFees2 = 18;
	const byte IndexCustomer = 19;
	const byte IndexEmployee = 20;
	const byte IndexComments = 21;

	const byte IndexEvent = 23;
	const byte IndexRegionCode = 24;

	Dictionary<string, Guid> _listTransactionType;
	Dictionary<string, Guid> _listCommodity;
	Dictionary<string, Guid> _listLocation;
	Dictionary<string, Guid> _listDeliveryLocation;
	Dictionary<string, Guid> _listCustomer;
	Dictionary<string, Guid> _listEmployee;
	IXLWorksheet ws;
	Dictionary<string, ContractDto> _contractsDataImpor;
	List<ContractDto> _parentContracts;
	List<ContractDto> _childContracts;
	List<Contract> _contracts;
	List<ExtendedContractType> _extendedContractTypes = new List<ExtendedContractType>();	
	const short MinCropYear = 2018;

	public ProcessContractCommandHandler(ContractMigrationRepository migrationLog, ContractRepository contractRepository,
		SettingRepository settingRepository, TransactionTypeRepository transactionTypeRepository,
		CommodityRepository commodityRepository, LocationRepository locationRepository,
		CustomerRepository customerRepository, EmployeeRepository employeeRepository, UnitOfWork unitOfWork, AppDbContext dbContext)
	{
		_migrationLog = migrationLog;
		_contractRepository = contractRepository;
		_settingRepository = settingRepository;
		_unitOfWork = unitOfWork;
		_transactionTypeRepository = transactionTypeRepository;
		_commodityRepository = commodityRepository;
		_customerRepository = customerRepository;
		_employeeRepository = employeeRepository;
		_dbContext = dbContext;
	}

	public async Task<ImportResultDto> Handle(ProcessImportTemplateCommand request, CancellationToken cancellationToken)
	{
		var decimalSeparator = await _settingRepository.GetAbsoluteValue<string>(GenericSettingsDictionary.NumberDecimalSeparator);
		if (request.HasBushelsOnlySetup)
		{
			_extendedContractTypes = await _dbContext.ExtendedContractTypes.Where(a => a.IsActive).ToListAsync(cancellationToken);
		}
		_provider = new("en-us", false);
		_provider.NumberFormat.NumberDecimalSeparator = decimalSeparator;
		var data = new ImportResultDto();
		await ReadData(request, data);
		CreateContracts(data);
		await CommitChanges(data);
		return data;
	}

	async Task CommitChanges(ImportResultDto data)
	{
		for (var i = 0; i < data.Errors.Count; i++)
		{
			await _migrationLog.InsertAsync(new(data.Errors[i].Data, data.Errors[i].Error));
		}

		for (var i = 0; i < data.NewContracts.Count; i++)
		{
			await _migrationLog.InsertAsync(new(data.NewContracts[i].ContractId, data.NewContracts[i].Data));
		}

		for (var i = 0; i < _contracts.Count; i++)
		{
			await _contractRepository.InsertAsync(_contracts[i]);
		}

		if (data.Errors.Count == 0 && data.NewContracts.Count > 0)
		{
			await _unitOfWork.CommitAsync();
		}
	}

	static void ValidateParentChild(ContractDto parent, ContractDto child)
	{
		AssertionConcern.ArgumentIsEquals(parent.ContractTypeId, child.ContractTypeId, "The contract type has to be the same with the parent");
		AssertionConcern.ArgumentIsNotEquals(child.Event, EContractEvent.Create, "The contract event needs to be Price or Roll");
		AssertionConcern.ArgumentIsNotEquals(child.Contract.ContractTypeId, ContractTypeDictionary.FlatPrice, "Flat Price can not has a child operation");
	}

	void CreateParentContracts(ImportResultDto data)
	{
		foreach (var item in _parentContracts)
		{
			try
			{
				AssertionConcern.ArgumentIsNotNullOrEmpty(item.RegionCode, "Region Code should not be empty");
				var regionId = _dbContext.Regions.AsNoTracking().FirstOrDefault(r => r.ErpNumber == item.RegionCode)?.Id;
				AssertionConcern.ArgumentIsNotNull(regionId, "Invalid Region Code");
				var groupedLocation = _dbContext.GroupedLocations.AsNoTracking().FirstOrDefault(r => r.DestinationLocationId == item.DeliveryLocationId && r.ContractLocationId == item.LocationId && r.RegionId == regionId && r.IsActive);

				AssertionConcern.ArgumentIsEquals(regionId, groupedLocation?.RegionId, "There is no grouped location for the region provided");
				AssertionConcern.ArgumentGuidIsNotNullOrDefault(item.DeliveryLocationId, "Delivery location is required");
				AssertionConcern.ArgumentIsNotEquals(item.CropYear, 0, "The Crop Year is required");
				AssertionConcern.ArgumentIsBiggerThan(item.CropYear, MinCropYear, $"The Crop Year is required (and bigger thant {MinCropYear})");
				item.Contract = new(item.TransactionTypeId, item.ContractTypeId, item.IsSell,
					item.CommodityId, item.LocationId, item.DeliveryLocationId.Value, true, item.DeliveryStartDate,
					item.DeliveryEndDate, item.CropYear, item.CustomerId, item.EmployeeId, item.FuturesMonth,
					item.FuturesPrice, item.PostedBasis, item.PushBasis, item.FreightPrice, item.Fees1,
					item.Fees2, item.Quantity, item.Comments, false, false, false, null,
				ContractSource.Migration, regionId);
				item.Contract.AssignContractNumber(item.Number);
				if (item.ExtendedContractTypeId != Guid.Empty)
				{
					item.Contract.AssignExtendedContractTypeId(item.ExtendedContractTypeId);
				}
				item.Contract.ValidateAndUpdateErpStatus(ErpStatus.Success, string.Empty, true);
			}
			catch (Exception ed)
			{
				item.IsInvalid = true;
				data.Errors.Add(new()
				{
					WasSuccessful = false,
					CreatedOn = DateTime.Now,
					Error = ed.Message,
					Data = item.FormatDataImport
				});
			}
		}
	}

	void CreateChildContracts(ImportResultDto data)
	{
		foreach (var item in _childContracts)
		{
			ContractDto parent = null;
			try
			{
				if (!_contractsDataImpor.ContainsKey(item.ParentContractNumber))
				{
					throw new BusinessException("The parent contract has to be defined in this file.");
				}

				parent = _contractsDataImpor[item.ParentContractNumber];
				if (parent.HasParent)
				{
					throw new BusinessException("We only can load parent/child relations, more levels are not allowed");
				}

				ValidateParentChild(parent, item);
				//After all validations
				parent.Childs.Add(item);
				item.Contract = parent.Contract.CreateChild(item.Quantity, item.Event);
				item.Contract.ChangeComments(item.Comments);
				item.Contract.ChangeEmployee(item.EmployeeId);
				parent.Contract.ChangeEmployee(item.EmployeeId);
				item.Contract.ChangeDeliveryDates(item.DeliveryStartDate, item.DeliveryEndDate);
				item.Contract.ChangeFuturesPrice(item.FuturesPrice);
				item.Contract.ChangeFuturesMonth(item.FuturesMonth);
				item.Contract.ChangePushBasis(item.PushBasis);
				item.Contract.ChangePushBasis(item.PostedBasis);
				if (item.ContractTypeId == ContractTypeDictionary.HTA)
				{
					if (item.Event == EContractEvent.Price)
					{
						AssertionConcern.ArgumentGuidIsNotNullOrDefault(item.DeliveryLocationId, "On HTA price the delivery location is required");
						AssertionConcern.ArgumentIsBiggerThan(item.CropYear, MinCropYear, $"On Basis roll the Crop Year is required (and bigger thant {MinCropYear})");
						item.Contract.ChangeCropYear(item.CropYear);
						item.Contract.ChangeDeliveryLocation(item.DeliveryLocationId.Value);
						item.Contract.ChangeFees(item.Fees1, item.Fees2);
						item.Contract.ChangeFreightPrice(item.FreightPrice);
						parent.Contract.CreateHistoricEventForParent(EContractEvent.Price);
						item.Contract.ReloadPriceAsFlatPrice();
					}
					else /* Roll */
					{
						item.Contract.ChangeAndVerifyPrice(item.FuturesMonth, item.FuturesPrice,
							item.PostedBasis, item.PushBasis,
							item.FreightPrice, item.Fees1, item.Fees2);
						item.Contract.CancelRemainingBalance();
						item.Contract.Priced();
						parent.Contract.CreateHistoricEventForParent(EContractEvent.Roll);
					}
				}
				else /* Basis */
				{
					if (item.Event == EContractEvent.Price)
					{
						item.Contract.CancelRemainingBalance();
						item.Contract.Priced();
						item.Contract.ChangeFees(item.Fees1, item.Fees2);
						item.Contract.ChangeFreightPrice(item.FreightPrice);
						parent.Contract.CreateHistoricEventForParent(EContractEvent.Price);
						item.Contract.ReloadPriceAsFlatPrice();
					}
					else /* Roll */
					{
						item.Contract.ChangeAndVerifyPrice(item.FuturesMonth, item.FuturesPrice,
							item.PostedBasis, item.PushBasis,
							item.FreightPrice, item.Fees1, item.Fees2);
						AssertionConcern.ArgumentIsBiggerThan(item.CropYear, MinCropYear, $"On Basis roll the Crop Year is required (and bigger thant {MinCropYear})");
						item.Contract.ChangeCropYear(item.CropYear);
						item.Contract.Open();
						parent.Contract.CreateHistoricEventForParent(EContractEvent.Roll);
					}
				}

				parent.Contract.ReduceRemainingBalance(item.Quantity);
				if (parent.Contract.RemainingBalance == 0)
				{
					parent.Contract.Priced();
				}

				item.Contract.AssignContractNumberWithOutInformIt(item.Number);
				item.Contract.ValidateAndUpdateErpStatus(ErpStatus.Success, string.Empty, true);
			}
			catch (Exception ed)
			{
				item.IsInvalid = true;
				if (parent != null)
				{
					parent.IsInvalid = true;
				}

				data.Errors.Add(new()
				{
					WasSuccessful = false,
					CreatedOn = DateTime.Now,
					Error = ed.Message,
					Data = item.FormatDataImport
				});
			}
		}
	}

	void ValidateParentRelations(ImportResultDto data)
	{
		foreach (var item in _parentContracts.Where(x => x.IsInvalid))
		{
			foreach (var child in item.Childs)
			{
				if (!child.IsInvalid)
				{
					child.IsInvalid = true;
					data.Errors.Add(new()
					{
						WasSuccessful = false,
						CreatedOn = DateTime.Now,
						Error = "Parent or any sibling contract is invalid",
						Data = child.FormatDataImport
					});
				}
			}
		}
	}

	void ProcessParentContracts(ImportResultDto data)
	{
		foreach (var item in _parentContracts.Where(x => !x.IsInvalid))
		{
			_contracts.Add(item.Contract);
			data.NewContracts.Add(new()
			{
				ContractId = item.Contract.Id,
				CreatedOn = DateTime.Now,
				Data = item.FormatDataImport,
				WasSuccessful = true
			});
			foreach (var child in item.Childs)
			{
				_contracts.Add(child.Contract);
				data.NewContracts.Add(new()
				{
					ContractId = child.Contract.Id,
					CreatedOn = DateTime.Now,
					Data = child.FormatDataImport,
					WasSuccessful = true
				});
			}
		}
	}

	void CreateContracts(ImportResultDto data)
	{
		CreateParentContracts(data);
		CreateChildContracts(data);
		ValidateParentRelations(data);
		ProcessParentContracts(data);
	}

	#region Read data from excel

	async Task ReadData(ProcessImportTemplateCommand request, ImportResultDto data)
	{
		using (var workbook = ReadFile(request))
		{
			AssertionConcern.ArgumentIsEquals(workbook.Worksheets.Count, 2, "Invalid sheet count");
			ws = workbook.Worksheet(2);
			AssertionConcern.ArgumentIsSmallerThan(ws.RowsUsed().Count(), 1001, "We only support 1000 contracts by file");
			try
			{
				_listTransactionType = (await _transactionTypeRepository.List(true, false)).ToDictionary(x => x.Name.ToLower(CultureInfo.InvariantCulture), x => x.Id);
			}
			catch (Exception ed)
			{
				throw new BusinessException("We can not process the database information about catalogs, this is an error with the information on the database (duplicated transaction types).", ed);
			}

			try
			{
				_listCommodity = (await _commodityRepository.List()).ToDictionary(x => x.Name.ToLower(CultureInfo.InvariantCulture), x => x.Id);
			}
			catch (Exception ed)
			{
				throw new BusinessException("We can not process the database information about catalogs, this is an error with the information on the database (duplicated commodoities).", ed);
			}

			try
			{
				_listLocation = await _dbContext.Locations.Where(x => x.IsLocation).ToDictionaryAsync(x => x.Number.ToLower(CultureInfo.InvariantCulture), x => x.Id);
			}
			catch (Exception ed)
			{
				throw new BusinessException("We can not process the database information about catalogs, this is an error with the information on the database (duplicated locations).", ed);
			}

			try
			{
				_listDeliveryLocation = await _dbContext.Locations.Where(x => x.IsDestination).ToDictionaryAsync(x => x.Number.ToLower(CultureInfo.InvariantCulture), x => x.Id);
			}
			catch (Exception ed)
			{
				throw new BusinessException("We can not process the database information about catalogs, this is an error with the information on the database (duplicated delivery locations).", ed);
			}

			try
			{
				_listCustomer = (await _customerRepository.ErpList()).ToDictionary(x => x.Name.ToLower(CultureInfo.InvariantCulture), x => x.Id);
			}
			catch (Exception ed)
			{
				throw new BusinessException("We can not process the database information about catalogs, this is an error with the information on the database (duplicated customers).", ed);
			}

			try
			{
				_listEmployee = (await _employeeRepository.ErpList()).ToDictionary(x => x.Name.ToLower(CultureInfo.InvariantCulture), x => x.Id);
			}
			catch (Exception ed)
			{
				throw new BusinessException("We can not process the database information about catalogs, this is an error with the information on the database (duplicated employees).", ed);
			}

			_contractsDataImpor = new(ws.RowsUsed().Count());
			_parentContracts = new(ws.RowsUsed().Count());
			_childContracts = new(ws.RowsUsed().Count());
			GetContractsInformation(data);
			_contracts = new(_contractsDataImpor.Count);
			ws = null;
		}
	}

	void GetContractsInformation(ImportResultDto data)
	{
		foreach (var row in ws.RowsUsed().Skip(1)) // Skip header row
		{
			try
			{
				if (AllColumnsAreEmpty(row)) { continue; }

				GetContractInformationAndCreateDto(row);
			}
			catch (Exception ed)
			{
				data.Errors.Add(new()
				{
					WasSuccessful = false,
					CreatedOn = DateTime.Now,
					Error = ed.Message,
					Data = FormatRow(row)
				});
			}
		}
	}
	
	void GetContractInformationAndCreateDto(IXLRow row)
	{
		var item = new ContractDto
		{
			ContractTypeId = GetContractType(row),
			ExtendedContractTypeId = GetExtendedContractType(row),
			Number = GetStringField(row, IndexNumber, true, "Contract Number"),
			ParentContractNumber = string.Empty, //GetStringField(row, IndexParentContractNumber, false, "Parent Contract Number"),
			Event = EContractEvent.Create, //GetEvent(row),
			TransactionTypeId = GetTransactionType(row),
			IsSell = GetIsSell(row),
			CommodityId = GetCommodity(row),
			LocationId = GetLocation(row),
			DeliveryLocationId = GetDeliveryLocation(row),
			CropYear = GetShortField(row, IndexCropYear, true, "Crop Year"),
			Quantity = GetDecimalField(row, IndexQuantity, true, "Quantity").Value,
			FuturesMonth = GetFuturesMonth(row),
			FuturesPrice = GetDecimalField(row, IndexFuturesPrice, false, "Futures Price", null),
			PostedBasis = GetDecimalField(row, IndexPostedBasis, false, "Posted Basis", null),
			PushBasis = GetDecimalField(row, IndexPushBasis, false, "Push Basis"),
			FreightPrice = GetDecimalField(row, IndexFreightPrice, false, "Futures Price").Value,
			Fees1 = GetDecimalField(row, IndexFees1, false, "Fees 1").Value,
			Fees2 = GetDecimalField(row, IndexFees2, false, "Fees 2").Value,
			Comments = GetStringField(row, IndexComments, false, "Comments", string.Empty),
			RegionCode = GetStringField(row, IndexRegionCode, false, "Region Code", string.Empty),
			CustomerId = GetCustomer(row),
			EmployeeId = GetEmployee(row),
			DeliveryStartDate = GetDateTime(row, IndexDeliveryStartDate, true, "Delivery Start Date", DateTime.MinValue),
			DeliveryEndDate = GetDateTime(row, IndexDeliveryEndDate, true, "Delivery End Date", DateTime.MinValue),
			FormatDataImport = FormatRow(row),
			Contract = null,
			IsInvalid = false
		};
		if (_contractsDataImpor.ContainsKey(item.Number))
		{
			throw new ArgumentException("Duplicated Contract Number");
		}

		_contractsDataImpor.Add(item.Number, item);
		if (item.HasParent)
		{
			_childContracts.Add(item);
		}
		else
		{
			_parentContracts.Add(item);
		}
	}

	short GetShortField(IXLRow row, int column, bool required, string fieldName, short defaultValue = 0)
	{
		var value = row.Cell(column).Value.ToString(CultureInfo.InvariantCulture);
		if (!short.TryParse(value, out var result))
		{
			if (!string.IsNullOrEmpty(value))
			{
				throw new InvalidArgumentException($"{fieldName} is not a valid value :{value}:");
			}

			if (required)
			{
				throw new InvalidArgumentException(fieldName + " is required");
			}

			return defaultValue;
		}

		return result;
	}

	decimal? GetDecimalField(IXLRow row, int column, bool required, string fieldName, decimal? defaultValue = 0)
	{
		var value = row.Cell(column).Value.ToString(CultureInfo.InvariantCulture);
		if (!decimal.TryParse(value, _styles, _provider, out var result))
		{
			if (!string.IsNullOrEmpty(value))
			{
				throw new InvalidArgumentException($"{fieldName} is not a valid value :{value}:");
			}

			if (required)
			{
				throw new InvalidArgumentException(fieldName + " is required");
			}

			return defaultValue;
		}

		return result;
	}

	string GetStringField(IXLRow row, int column, bool required, string fieldName, string defaultValue = null)
	{
		var value = row.Cell(column).Value.ToString(CultureInfo.InvariantCulture);
		if (string.IsNullOrWhiteSpace(value))
		{
			if (required)
			{
				throw new InvalidArgumentException(fieldName + " is required");
			}

			return defaultValue;
		}

		return value.Trim();
	}

	string GetFuturesMonth(IXLRow row)
	{
		var value = GetStringField(row, IndexFuturesMonth, true, "Futures Month");
		AssertionConcern.ArgumentIsTrue(DateExtensions.ValidateFuturesMonth(value), "Invalid futures month");
		return value;
	}

	DateTime GetDateTime(IXLRow row, int column, bool required, string fieldName, DateTime defaultValue)
	{
		var value = row.Cell(column).Value.ToString(CultureInfo.InvariantCulture);
		if (!DateTime.TryParse(value, out var result))
		{
			if (!string.IsNullOrEmpty(value))
			{
				if (double.TryParse(value, out var d))
				{
					try
					{
						return DateTime.FromOADate(d);
					}
					catch
					{
						if (required)
						{
							throw new InvalidArgumentException(fieldName + " is required");
						}

						return defaultValue;
					}
				}

				throw new InvalidArgumentException($"{fieldName} is not a valid value :{value}:");
			}

			if (required)
			{
				throw new InvalidArgumentException(fieldName + " is required");
			}

			return defaultValue;
		}

		return result;
	}

	bool GetIsSell(IXLRow row)
	{
		var status = GetStringField(row, IndexBuy_Sell, true, "Buy/Sell");
		if (string.Equals(status, "sell", StringComparison.OrdinalIgnoreCase))
		{
			return true;
		}

		if (string.Equals(status, "buy", StringComparison.OrdinalIgnoreCase) || string.Equals(status, "purchase", StringComparison.OrdinalIgnoreCase))
		{
			return false;
		}

		throw new ArgumentException("Invalid Buy/Sell Value");
	}

	EContractEvent GetEvent(IXLRow row)
	{
		var status = GetStringField(row, IndexEvent, false, "Event", string.Empty);
		if (string.Equals(status, "Price", StringComparison.Ordinal))
		{
			return EContractEvent.Price;
		}

		if (string.Equals(status, "Roll", StringComparison.Ordinal))
		{
			return EContractEvent.Roll;
		}

		if (string.IsNullOrEmpty(status))
		{
			return EContractEvent.Create;
		}

		throw new ArgumentException("Invalid Event Value");
	}

	Guid GetContractType(IXLRow row)
	{
		var contractType = GetStringField(row, IndexContractType, true, "Contract Type");
		var transactionType = GetStringField(row, IndexTransactionType, true, "Transaction Type");
		if (string.Equals(transactionType, "Cash contract", StringComparison.Ordinal))
		{
			if (string.Equals(contractType, "Flat Price", StringComparison.Ordinal))
			{
				return ContractTypeDictionary.FlatPrice;
			}

			if (string.Equals(contractType, "HTA", StringComparison.Ordinal))
			{
				return ContractTypeDictionary.HTA;
			}

			if (string.Equals(contractType, "Basis", StringComparison.Ordinal))
			{
				return ContractTypeDictionary.Basis;
			}
		}
		if (string.Equals(transactionType, "Bushels Only", StringComparison.Ordinal))
		{
			return ContractTypeDictionary.NTC;
		}

		throw new ArgumentException("Invalid Contract Type");
	}

	Guid GetExtendedContractType(IXLRow row)
	{
		var contractType = GetStringField(row, IndexContractType, true, "Contract Type");
		var transactionType = GetStringField(row, IndexTransactionType, true, "Transaction Type");
		if (string.Equals(transactionType, "Bushels Only", StringComparison.Ordinal))
		{
			var extendedContractType = _extendedContractTypes.FirstOrDefault(a => a.Name == contractType);
			if (extendedContractType != null)
				return extendedContractType.Id;
			throw new ArgumentException("Invalid Extended Contract Type");
		}
		return Guid.Empty;
	}

	Guid GetTransactionType(IXLRow row)
	{
		var value = GetStringField(row, IndexTransactionType, false, "Transaction Type", "Cash Contract").ToLower(CultureInfo.InvariantCulture);
		if (!_listTransactionType.ContainsKey(value)) { throw new InvalidArgumentException("Transaction Type is incorrect"); }

		return _listTransactionType[value];
	}

	Guid GetCommodity(IXLRow row) => GetId(row, IndexCommodity, "Commodity", _listCommodity);

	Guid GetLocation(IXLRow row) => GetId(row, IndexLocation, "Location Number", _listLocation);

	Guid? GetDeliveryLocation(IXLRow row)
	{
		var value = GetStringField(row, IndexDeliveryLocation, false, "Delivery Location Number", string.Empty).ToLower(CultureInfo.InvariantCulture);
		if (!_listDeliveryLocation.ContainsKey(value)) { return null; }

		return _listDeliveryLocation[value];
	}

	Guid GetCustomer(IXLRow row) => GetId(row, IndexCustomer, "Customer", _listCustomer);

	Guid GetEmployee(IXLRow row) => GetId(row, IndexEmployee, "Employee", _listEmployee);

	Guid GetId(IXLRow row, int column, string valueName, Dictionary<string, Guid> list)
	{
		var value = GetStringField(row, column, true, valueName).ToLower(CultureInfo.InvariantCulture).Trim();
		if (!list.ContainsKey(value)) { throw new InvalidArgumentException(valueName + " is incorrect"); }

		return list[value];
	}
	
	XLWorkbook ReadFile(ProcessImportTemplateCommand hedge)
	{
		AssertionConcern.ArgumentIsNotNull(hedge.ImportInformation, "Information is missing");
		AssertionConcern.ArgumentIsNotNull(hedge.ImportInformation.File, "Import file not found");
		AssertionConcern.ArgumentIsNotTrue(hedge.ImportInformation.File == null || hedge.ImportInformation.File.Length <= 0, "File is empty");
		AssertionConcern.ArgumentIsNotTrue(Math.Round((decimal)hedge.ImportInformation.File.Length / 1048576) > 25, "File size exceeds the expected 25Mb");
		XLWorkbook workbook;
		try
		{
			using (var stream = new MemoryStream())
			{
				hedge.ImportInformation.File.CopyTo(stream);
				workbook = new XLWorkbook(stream);
			}
		}
		catch (Exception ed)
		{
			throw new BusinessException("File could not be loaded", ed);
		}

		return workbook;
	}
	
	string FormatRow(IXLRow row)
	{
		var sb = new StringBuilder("{");
		for (var i = 0; i < CreateContractTemplateCommandHandler.TITLESSHEETTWO.Length; i++)
		{
			sb.AppendLine($"\"{CreateContractTemplateCommandHandler.TITLESSHEETTWO[i]}\":\"{row.Cell(i + 1).Value.ToString(CultureInfo.InvariantCulture)}\",");
		}

		sb.AppendLine($"\"Tenant\":\"{ws.Name}\"");
		sb.Append('}');
		return sb.ToString();
	}
	
	bool AllColumnsAreEmpty(IXLRow row)
	{
		for (var column = 1; column <= IndexComments; column++)
		{
			if (!string.IsNullOrEmpty(row.Cell(column).Value.ToString(CultureInfo.InvariantCulture)))
			{
				return false;
			}
		}
		return true;
	}

	#endregion
}
