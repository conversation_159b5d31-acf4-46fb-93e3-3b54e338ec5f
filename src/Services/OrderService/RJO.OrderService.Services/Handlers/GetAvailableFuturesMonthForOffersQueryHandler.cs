using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.DTO.Catalogs.FutureMonths;
using RJO.OrderService.Services.Extensions;

namespace RJO.OrderService.Services.Handlers;

public sealed record GetAvailableFuturesMonthForOffersQuery() : IRequest<IReadOnlyCollection<FutureMonthsItemDto>>;

public sealed class GetAvailableFuturesMonthForOffersQueryHandler : IRequestHandler<GetAvailableFuturesMonthForOffersQuery, IReadOnlyCollection<FutureMonthsItemDto>>
{
	readonly AppDbContext _dbContext;

	public GetAvailableFuturesMonthForOffersQueryHandler(AppDbContext dbContext) => _dbContext = dbContext;

	public async Task<IReadOnlyCollection<FutureMonthsItemDto>> Handle(GetAvailableFuturesMonthForOffersQuery request, CancellationToken cancellationToken) =>
		await _dbContext.Offers
			.AsNoTracking()
			.Join(_dbContext.FuturesMonths,
				x => x.FuturesMonth,
				y => y.MonthYear,
				(x, y) => new FutureMonthsItemDto
				{
					Name = y.MonthYear,
					Year = y.YearNumber,
					Month = y.MonthNumber
				})
			.ToFutureMonthsItemDtoListAsync(false, cancellationToken);
}
