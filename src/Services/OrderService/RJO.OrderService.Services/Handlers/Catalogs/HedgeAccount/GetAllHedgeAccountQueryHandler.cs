using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.CustomExceptions.BusinessExceptions;
using RJO.OrderService.Common;
using RJO.OrderService.Persistence.Extensions;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Services.DTO.Catalogs.HedgeAccount;
using RJO.OrderService.Services.DTO.Common;
using RJO.OrderService.Services.Extensions.Validator;

namespace RJO.OrderService.Services.Handlers.Catalogs.HedgeAccount;

public class GetAllHedgeAccountQuery : IRequest<ListDto<HedgeAccountItemDto>>
{
	public HedgeAccountFilterDto Filter { get; set; }
}

public class GetAllHedgeAccountQueryHandler : IRequestHandler<GetAllHedgeAccountQuery, ListDto<HedgeAccountItemDto>>
{
	readonly ILogger _logger;
	readonly HedgeAccountRepository _hedgeAccountRepository;

	public GetAllHedgeAccountQueryHandler(ILoggerFactory loggerFactory, HedgeAccountRepository hedgeAccountRepository)
	{
		_hedgeAccountRepository = hedgeAccountRepository;
		_logger = loggerFactory.CreateLogger<GetAllHedgeAccountQueryHandler>();
	}

	public async Task<ListDto<HedgeAccountItemDto>> Handle(GetAllHedgeAccountQuery request, CancellationToken cancellationToken)
	{
		var query = from f in await _hedgeAccountRepository.GetAllEntities()
			select f;

		if (!request.Filter.ValidatePagingParametersAreGreaterThanZero())
			throw new PaginationNotValidException("Pagination parameters are not valid, please review your parameters");

		if (!string.IsNullOrEmpty(request.Filter.Name))
		{
			query = query.Where(x => x.Name.Contains(request.Filter.Name));
		}

		if (!string.IsNullOrEmpty(request.Filter.SortColumnName))
		{
			query = query.OrderByDynamic(request.Filter.SortColumnName, request.Filter.SortOrder == SortOrder.Descending);
		}
		else
		{
			query = query.OrderBy(x => x.Name);
		}

		var count = await query.CountAsync(cancellationToken);

		query = query.GetPagedQuery(request.Filter.Start, request.Filter.Limit);

		_logger.HedgeAccountAll();

		return new(count, (
			from l in query
			select new HedgeAccountItemDto
			{
				Id = l.Id,
				Name = l.Name,
				Account = l.Account,
				IsActive = l.IsActive
			}).ToList());
	}
}
