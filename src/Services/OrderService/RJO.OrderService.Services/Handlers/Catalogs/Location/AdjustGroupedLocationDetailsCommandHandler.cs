using LaunchDarkly.Sdk;
using LaunchDarkly.Sdk.Server;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.DTO.Catalogs.Location;

namespace RJO.OrderService.Services.Handlers.Catalogs.Location;

public sealed record AdjustGroupedLocationDetailsCommand(Guid Id, GroupedLocationDataDto GroupedLocation, Context FlagContext) : IRequest<Guid>, ITransactionalRequest;

public sealed class AdjustGroupedLocationDetailsCommandHandler : IRequestHandler<AdjustGroupedLocationDetailsCommand, Guid>
{
	readonly AppDbContext _dbContext;
	readonly LdClient _ldClient;

	public AdjustGroupedLocationDetailsCommandHandler(AppDbContext dbContext, LdClient ldClient)
	{
		_dbContext = dbContext;
		_ldClient = ldClient;
	}

	public async Task<Guid> Handle(AdjustGroupedLocationDetailsCommand request, CancellationToken cancellationToken)
	{
		var groupedLocation = await _dbContext.GroupedLocations.FindAsync(new object[] { request.Id }, cancellationToken);

		AssertionConcern.ArgumentIsNotNull(groupedLocation, $"Location with GUID '{request.Id}' does not exist");
		if (request.GroupedLocation.RegionId != groupedLocation.RegionId)
		{
			var regionAndGroupingAreUsed = await _dbContext.Contracts.AnyAsync(x => x.RegionId == groupedLocation.RegionId && x.LocationId == groupedLocation.ContractLocationId && x.DeliveryLocationId == groupedLocation.DestinationLocationId, cancellationToken) ||
					await _dbContext.Offers.AnyAsync(x => x.RegionId == groupedLocation.RegionId && x.LocationId == groupedLocation.ContractLocationId && x.DeliveryLocationId == groupedLocation.DestinationLocationId, cancellationToken);
			AssertionConcern.ArgumentIsNotTrue(regionAndGroupingAreUsed && groupedLocation.RegionId != request.GroupedLocation.RegionId, "Region cannot be changed because of existing transactions for this location");
		}
		
		AssertionConcern.ArgumentIsNotNull(request.GroupedLocation.RegionId, "A region must be selected for this location pair");

		if (groupedLocation!.ContractLocationId != null)
		{
			var contractLocation = await _dbContext.Locations.FindAsync(new object[] { groupedLocation.ContractLocationId }, cancellationToken);
			if (contractLocation != null)
			{
				contractLocation.ChangeName(request.GroupedLocation.ContractLocationName);
				contractLocation.ChangeNumber(request.GroupedLocation.ContractLocationNumber);
			}
		}

		if (groupedLocation!.DestinationLocationId != null)
		{
			var destinationLocation = await _dbContext.Locations.FindAsync(new object[] { groupedLocation.DestinationLocationId }, cancellationToken);
			if (destinationLocation != null)
			{
				destinationLocation.ChangeName(request.GroupedLocation.DestinationLocationName);
				destinationLocation.ChangeNumber(request.GroupedLocation.DestinationLocationNumber);
			}
		}
		if (request.GroupedLocation.RegionId != groupedLocation.RegionId)
		{  
			groupedLocation.ChangeRegionId(request.GroupedLocation.RegionId);
		}

		await _dbContext.SaveChangesAsync(cancellationToken);
		return groupedLocation.Id;
	}
}
