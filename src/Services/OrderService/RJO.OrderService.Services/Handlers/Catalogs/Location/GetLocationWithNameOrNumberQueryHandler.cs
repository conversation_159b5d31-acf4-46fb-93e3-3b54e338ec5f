using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RJO.OrderService.Common;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.DTO.Catalogs.Location;

namespace RJO.OrderService.Services.Handlers.Catalogs.Location;

public sealed class GetLocationWithNameOrNumberQuery : IRequest<IReadOnlyCollection<LocationSearchDto>>
{
	public string Name { get; init; }
	public string Number { get; init; }
	public bool IsDestination { get; init; }
}

public sealed class GetLocationWithNameOrNumberQueryHandler : IRequestHandler<GetLocationWithNameOrNumberQuery, IReadOnlyCollection<LocationSearchDto>>
{
	readonly ILogger<GetLocationWithNameOrNumberQueryHandler> _logger;
	readonly AppDbContext _dbContext;

	public GetLocationWithNameOrNumberQueryHandler(ILogger<GetLocationWithNameOrNumberQueryHandler> logger, AppDbContext dbContext)
	{
		_logger = logger;
		_dbContext = dbContext;
	}

	public async Task<IReadOnlyCollection<LocationSearchDto>> Handle(GetLocationWithNameOrNumberQuery request, CancellationToken cancellationToken)
	{
		var query = _dbContext.Locations.AsQueryable();

		if (!string.IsNullOrEmpty(request.Name))
		{
			query = query.Where(x => x.Name.Contains(request.Name));
		}

		if (!string.IsNullOrEmpty(request.Number))
		{
			query = query.Where(x => x.Number.Contains(request.Number));
		}

		query = request.IsDestination ? query.Where(x => x.IsDestination) : query.Where(x => x.IsLocation);

		var locations = await query.Select(x => new LocationSearchDto
		{
			Id = x.Id,
			Name = x.Name,
			Number = x.Number
		}).ToListAsync(cancellationToken);

		_logger.LocationAll();

		return locations;
	}
}
