using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.Resources;
using RJO.OrderService.Domain;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.DTO.Catalogs.Location;

namespace RJO.OrderService.Services.Handlers.Catalogs.Location;

public class SettingLocationEmployeeCommand : IRequest<Unit>, ITransactionalRequest
{
	public LocationEmployeeDto LocationEmployee { get; set; }
}

public class SettingLocationEmployeeCommandHandler : IRequestHandler<SettingLocationEmployeeCommand, Unit>
{
	readonly AppDbContext _dbContext;
	public SettingLocationEmployeeCommandHandler(AppDbContext dbContext) => _dbContext = dbContext;

	public async Task<Unit> Handle(SettingLocationEmployeeCommand request, CancellationToken cancellationToken)
	{
		AssertionConcern.ArgumentIsNotNull(request.LocationEmployee.EmployeeId, GeneralResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(request.LocationEmployee.Locations, GeneralResources.RequiredValueIsNotPresent);

		foreach (var item in request.LocationEmployee.Locations)
		{
			var locationEmployeeToSave = await _dbContext.LocationEmployees.FirstOrDefaultAsync(x =>
				x.EmployeeId == request.LocationEmployee.EmployeeId && x.LocationId == item.LocationId, cancellationToken);

			if (locationEmployeeToSave == null)
			{
				var newLocationEmployee = new LocationEmployee(request.LocationEmployee.EmployeeId, item.LocationId, item.CanSell, item.CanBuy);
				_dbContext.LocationEmployees.Add(newLocationEmployee);
			}
			else
			{
				locationEmployeeToSave.UpdateCanSell(item.CanSell);
				locationEmployeeToSave.UpdateCanBuy(item.CanBuy);
				_dbContext.LocationEmployees.Update(locationEmployeeToSave);
			}
		}

		await _dbContext.SaveChangesAsync(cancellationToken);
		return Unit.Value;
	}

}
