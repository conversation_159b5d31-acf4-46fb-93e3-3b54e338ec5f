using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Persistence;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.DTO.Catalogs.Customer;
using RJO.OrderService.Services.Features.Notifications.UserSubscriptions.Commands;

namespace RJO.OrderService.Services.Handlers.Catalogs.Customer;

public sealed class CreateCustomerCommand : IRequest<string>, ITransactionalRequest
{
	public CustomerDto Customer { get; init; }
	public bool HasMobileClaim { get; init; }
}

public sealed class CreateCustomerCommandHandler : IRequestHandler<CreateCustomerCommand, string>
{
	readonly UnitOfWork _unitOfWork;
	readonly AppDbContext _dbContext;
	readonly IMediator _mediator;

	public CreateCustomerCommandHandler(UnitOfWork unitOfWork, AppDbContext dbContext, IMediator mediator)
	{
		_unitOfWork = unitOfWork;
		_dbContext = dbContext;
		_mediator = mediator;
	}

	public async Task<string> Handle(CreateCustomerCommand request, CancellationToken cancellationToken)
	{
		AssertionConcern.ArgumentIsTrue(!await _dbContext.Customers.AnyAsync(c => c.Number == request.Customer.Number, cancellationToken), "The ERP number is already assigned, please enter other.");

		if (request.Customer.IsMobileAppEnable) 
			SecurityAssertionConcern.ArgumentIsTrue(request.HasMobileClaim, "You need the correct claim to change the mobile app setting.");

		var customer = new Domain.Customer(
			request.Customer.Number,
			request.Customer.FirstName,
			request.Customer.LastName,
			request.Customer.WorkPhoneNumber,
			request.Customer.PhoneNumber,
			request.Customer.Street,
			request.Customer.City,
			request.Customer.State,
			request.Customer.ZipCode,
			request.Customer.Country,
			request.Customer.Email,
			request.Customer.IsMobileAppEnable,
			request.Customer.MobileAppLimit
		);

		_dbContext.Customers.Add(customer);

		await _mediator.Send(new UpsertCustomerSubscriptionCommand(customer.Id, request.Customer.IsEmailNotificationEnabled, request.Customer.IsSmsNotificationEnabled), cancellationToken);
		
		await _unitOfWork.CommitAsync();
		
		return customer.Number;
	}
}
