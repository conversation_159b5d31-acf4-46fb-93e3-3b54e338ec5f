using LaunchDarkly.Sdk;
using LaunchDarkly.Sdk.Server;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Persistence;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Extensions;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Services.DTO.Bidsheet;
using RJO.OrderService.Services.DTO.Offer;
using RJO.OrderService.Services.DTO.ReviewAndRelease;
using RJO.OrderService.Services.Features.Notifications;
using RJO.OrderService.Services.Features.Notifications.AutoRefresh;
using RJO.OrderService.Services.Features.Notifications.NotificationGroups.Commands;
using RJO.OrderService.Services.Features.Notifications.Notifications.Commands;
using RJO.OrderService.Services.Helper;
using RJO.OrderService.Services.Jobs.DeleteJobs;
using RJO.OrderService.Services.Mapping;
using RJO.OrderService.Services.Services;
using RJO.OrderService.Services.Services.OfferProcess;

namespace RJO.OrderService.Services.Handlers.Offers;

public sealed record UpdateOfferCommand(Guid OfferId, OfferUpdateDto OfferDto, bool IsMobile, Context FlagContext) : IRequest<string>, ITransactionalRequest;

public sealed class UpdateOfferHandler : IRequestHandler<UpdateOfferCommand, string>
{
	readonly UnitOfWork _unitOfWork;
	readonly OfferRepository _offerRepository;
	readonly OfferWorkflowContext _offerWorkflowContext;
	readonly MetadataDomainService _metadataDomainService;
	readonly IMediator _mediator;
	readonly BidSheetHelper _bidSheetHelper;
	readonly AppDbContext _dbContext;
	readonly LdClient _ldClient;
	readonly DeleteScheduledJob _deleteScheduledJob;
	readonly FutureMonthsHelper _htaFutureMonthsHelper;
	readonly ILogger<UpdateOfferCommand> _logger;
	readonly CurrentUser _currentUser;

	public UpdateOfferHandler(UnitOfWork unitOfWork, OfferRepository offerRepository, OfferWorkflowContext offerWorkflowContext,
		MetadataDomainService metadataDomainService, IMediator mediator, AppDbContext dbContext, FutureMonthsHelper htaFutureMonthsHelper,
		BidSheetHelper bidSheetHelper, LdClient ldClient, DeleteScheduledJob deleteScheduledJob, ILogger<UpdateOfferCommand> logger,
		CurrentUser currentUser)
	{
		_logger = logger;
		_unitOfWork = unitOfWork;
		_offerRepository = offerRepository;
		_offerWorkflowContext = offerWorkflowContext;
		_metadataDomainService = metadataDomainService;
		_mediator = mediator;
		_dbContext = dbContext;
		_bidSheetHelper = bidSheetHelper;
		_ldClient = ldClient;
		_deleteScheduledJob = deleteScheduledJob;
		_htaFutureMonthsHelper = htaFutureMonthsHelper;
		_currentUser = currentUser;
	}

	public async Task<string> Handle(UpdateOfferCommand request, CancellationToken cancellationToken)
	{
		var triggerEdit = false;
		var offer = await _dbContext.Offers
			.Include(o => o.Commodity)
			.FirstOrDefaultAsync(o => o.Id == request.OfferId, cancellationToken);

		if (offer == null)
		{
			throw new InvalidOperationException("Offer not found.");
		}
		AssertionConcern.ArgumentIsEquals(request.OfferDto.UpdatedOn, offer.UpdatedOn, "Another process has updated this transaction. Please close module and try again.");
		var isValidToEdit = offer.IsOnStatus(EOfferState.PartiallyFilled) || offer.IsOnStatus(EOfferState.Working) ||
							(offer.IsOnStatus(EOfferState.Rejected) && offer.ContractTypeId != ContractTypeDictionary.Basis)
							|| (offer.IsOnStatus(EOfferState.Pending) && offer.Event == ETransactionEvent.Creation)
							|| (offer.IsOnStatus(EOfferState.Pending) && offer.Event == ETransactionEvent.Edition);

		AssertionConcern.ArgumentIsTrue(isValidToEdit, $"Offer '{offer.Number}' must be on a correct status. It cannot be edited");

		if (request.IsMobile)
		{
			var employee = await _dbContext.Employees.FirstOrDefaultAsync(a => a.Email == _currentUser.Email, cancellationToken);
			if (employee != null)
			{
				var groupedLocation = await _dbContext.MobileIntegrationLocationMapping.AsNoTracking()
							.Where(x => x.DestinationLocationId == offer.DeliveryLocationId)
							.Select(x => x.GroupedLocation)
							.FirstOrDefaultAsync(cancellationToken);
				AssertionConcern.ArgumentIsNotNull(groupedLocation, "Grouped location for the mobile user could not be found");
				AssertionConcern.ArgumentIsTrue(groupedLocation.IsActive, "Grouped location for the mobile user is inactive");
			}
		}

		var isEnoughQuantityChangedtoHedge = false;

		var marketTransaction = await _dbContext.MarketTransactions.GetActualByOffer(request.OfferId);
		if (marketTransaction != null)
		{
			var isEnoughQuantityIncreasedToHedge = (request.OfferDto.RemainingBalance - (offer.Commodity.LotFactor * marketTransaction.Lots)) >= offer.Commodity.LotFactor;
			var isEnoughQuantityDecreasedToHedge = Math.Abs(marketTransaction.Quantity - request.OfferDto.RemainingBalance) > (marketTransaction.Quantity - (offer.Commodity.LotFactor * marketTransaction.Lots)); // if existing quantity is 10001 and it is reduced to 10000  then also this should be false
			isEnoughQuantityChangedtoHedge = (marketTransaction.Quantity > request.OfferDto.RemainingBalance) ? isEnoughQuantityDecreasedToHedge : isEnoughQuantityIncreasedToHedge;
		}

		var isPriceChanged = (offer.Price != request.OfferDto.Price) || (offer.FuturesPrice != request.OfferDto.FuturesPrice);

		offer.ChangeEmployee(request.OfferDto.EmployeeId);
		offer.ChangeComments(request.OfferDto.Comments);
		offer.ChangeLocation(request.OfferDto.LocationId);
		if (offer.IsOnStatus(EOfferState.Rejected))
		{
			var oldFuturePrice = offer.FuturesPrice;
			offer.ChangeAndVerifyPrice(request.OfferDto.FuturesMonth, request.OfferDto.FuturesPrice, request.OfferDto.PostedBasis,
				request.OfferDto.PushBasis, request.OfferDto.FreightPrice, request.OfferDto.Fees1, request.OfferDto.Fees2, request.OfferDto.Price);
			if (offer.FuturesPrice != oldFuturePrice)
			{
				await _offerWorkflowContext.ProcessRejectedOffer(offer);
			}
		}

		var bidSheetInformation = await GetBidsheetInformation(offer);
		if (_ldClient.BoolVariation(FeatureFlags.EnableOfferQuantityEdit, request.FlagContext))
		{
			triggerEdit = await HandleQuantityChange(request, bidSheetInformation, offer, cancellationToken);
		}
		if (offer.ContractTypeId == ContractTypeDictionary.HTA)
		{
			triggerEdit = HandleHtaPriceChange(request, triggerEdit, offer);
		}
		else if (offer.ContractTypeId == ContractTypeDictionary.FlatPrice)
		{
			triggerEdit = HandleFlatPriceChange(request, triggerEdit, offer);
		}
		else if (offer.ContractTypeId == ContractTypeDictionary.Basis)
		{
			offer.ChangeAndVerifyPrice(request.OfferDto.FuturesMonth, request.OfferDto.FuturesPrice, request.OfferDto.PostedBasis,
				request.OfferDto.PushBasis, request.OfferDto.FreightPrice, request.OfferDto.Fees1, request.OfferDto.Fees2, request.OfferDto.Price);
		}
		if (marketTransaction != null && (marketTransaction.IsOnStatus(EMarketTransactionState.Pending) || marketTransaction.IsOnStatus(EMarketTransactionState.Canceled)) && marketTransaction.Event == ETransactionEvent.Cancelation)
		{
			triggerEdit = false;
		}

		if (triggerEdit && (isEnoughQuantityChangedtoHedge || isPriceChanged))
			await _offerWorkflowContext.EditOfferProcess(offer);

		offer.ChangeTheirContract(request.OfferDto.TheirContract);
		await _metadataDomainService.UpdateOfferFields(offer.Id, request.OfferDto.CustomFields);
		_offerRepository.Update(offer);
		offer.CreateHistoricEvent(ETransactionEvent.Edition);
		try
		{
			await _unitOfWork.CommitAsync();
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "{ClassName}: Failed to update Offer for Id {OfferId}", nameof(UpdateOfferCommand), offer.Id);
			throw new("Another process has updated this transaction. Please close module and try again.");
		}

		if (_ldClient.BoolVariation(FeatureFlags.EnableNotifications, request.FlagContext))
		{
			await _mediator.Send(new SetOfferGroupsCommand(offer.Id, request.OfferDto.Groups), cancellationToken);
		}

		if (_ldClient.BoolVariation(FeatureFlags.EnableAutoRefresh, request.FlagContext))
		{
			var offerInfo = await _mediator.Send(new GetOfferByIdQuery(request.OfferId, request.FlagContext), cancellationToken);
			await _mediator.Send(new SendPushNotificationCommand<OfferInfoDto>(Constants.OfferChannel, new(ActionType.Update, offerInfo)), cancellationToken);

			var transaction = await _dbContext.MarketTransactions.AsNoTracking().FirstOrDefaultAsync(x => x.OfferId == offerInfo.Id, cancellationToken);
			if (transaction != null)
				await _mediator.Send(new SendPushNotificationCommand<ReviewAndReleaseItemDto>(Constants.ReviewAndReleaseChannel, new(ActionType.Update, transaction.MapToResponse())), cancellationToken);
		}

		return offer.Number;
	}

	async Task<BidSheetItemDto> GetBidsheetInformation(Offer offer)
	{
		BidSheetItemDto bidSheetInformation = null;
		
		// HTA offers are purely futures-based and should not undergo bidsheet validation.
		if (offer.ContractTypeId != ContractTypeDictionary.HTA)
		{
			if (offer.ContractTypeId == ContractTypeDictionary.FlatPrice)
			{
				var bidsheet = await _bidSheetHelper.GetInformationFromBidSheetExactValues(
					offer.CommodityId,
					offer.DeliveryLocationId,
					offer.CropYear,
					offer.DeliveryStartDate,
					offer.FuturesMonth,
					offer.DeliveryEndDate);

				if (bidsheet == null)
				{
					bidSheetInformation = await _bidSheetHelper.GetInformationFromBidSheetApproximateValues(
						offer.CommodityId,
						offer.DeliveryLocationId,
						offer.CropYear,
						offer.DeliveryStartDate,
						offer.DeliveryEndDate);

					AssertionConcern.ArgumentIsNotNull(bidSheetInformation, ErrorCodes.BidsheetNotFound);
					AssertionConcern.ArgumentIsTrue(offer.FuturesMonth == bidSheetInformation.FuturesMonth, "Futures month is not valid.");
				}
				else
				{
					bidSheetInformation = new()
					{
						FuturesMonth = bidsheet.FutureMonth,
						PostedBasis = bidsheet.Basis
					};
				}

				AssertionConcern.ArgumentIsTrue(offer.PostedBasis == bidSheetInformation.PostedBasis, "Basis has changed, please re-input offer");
			}
			else
			{
				var bidsheet = await _bidSheetHelper.GetInformationFromBidSheetExactValues(
					offer.CommodityId,
					offer.DeliveryLocationId,
					offer.CropYear,
					offer.DeliveryStartDate,
					null,
					offer.DeliveryEndDate);

				AssertionConcern.ArgumentIsNotNull(bidsheet, ErrorCodes.BidsheetNotFound);

				bidSheetInformation = new()
				{
					FuturesMonth = bidsheet.FutureMonth,
					PostedBasis = bidsheet.Basis
				};
			}
		}

		return bidSheetInformation;
	}

	static bool HandleFlatPriceChange(UpdateOfferCommand request, bool triggerEdit, Offer offer)
	{
		if (offer.IsChangingFuturesPrice(request.OfferDto.FuturesPrice, request.OfferDto.PostedBasis, request.OfferDto.PushBasis, request.OfferDto.FreightPrice, request.OfferDto.Fees1, request.OfferDto.Fees2,
							request.OfferDto.Price))
		{
			offer.ChangeAndVerifyPrice(request.OfferDto.FuturesMonth, request.OfferDto.FuturesPrice, request.OfferDto.PostedBasis,
				request.OfferDto.PushBasis, request.OfferDto.FreightPrice, request.OfferDto.Fees1, request.OfferDto.Fees2, request.OfferDto.Price);
			triggerEdit = true;
		}
		else
		{
			offer.ChangeAndVerifyPrice(request.OfferDto.FuturesMonth, request.OfferDto.FuturesPrice, request.OfferDto.PostedBasis,
				request.OfferDto.PushBasis, request.OfferDto.FreightPrice, request.OfferDto.Fees1, request.OfferDto.Fees2, request.OfferDto.Price);
		}

		return triggerEdit;
	}

	static bool HandleHtaPriceChange(UpdateOfferCommand request, bool triggerEdit, Offer offer)
	{
		if (offer.IsChangingFuturesPrice(request.OfferDto.FuturesPrice, request.OfferDto.PostedBasis, request.OfferDto.PushBasis, request.OfferDto.FreightPrice, request.OfferDto.Fees1, request.OfferDto.Fees2,
							request.OfferDto.Price))
		{
			var oldFuturePrice = offer.FuturesPrice;
			offer.ChangeAndVerifyPrice(request.OfferDto.FuturesMonth, request.OfferDto.FuturesPrice, request.OfferDto.PostedBasis,
				request.OfferDto.PushBasis, request.OfferDto.FreightPrice, request.OfferDto.Fees1, request.OfferDto.Fees2, request.OfferDto.Price);
			if (oldFuturePrice != offer.FuturesPrice)
			{
				triggerEdit = true;
			}
		}
		else
		{
			offer.ChangeAndVerifyPrice(request.OfferDto.FuturesMonth, request.OfferDto.FuturesPrice, request.OfferDto.PostedBasis,
				request.OfferDto.PushBasis, request.OfferDto.FreightPrice, request.OfferDto.Fees1, request.OfferDto.Fees2, request.OfferDto.Price);
		}
		return triggerEdit;
	}

	async Task<bool> HandleQuantityChange(UpdateOfferCommand request, BidSheetItemDto bidSheetInformation, Offer offer, CancellationToken cancellationToken)
	{
		if (offer.RemainingBalance != request.OfferDto.RemainingBalance)
		{
			AssertionConcern.ArgumentIsBiggerOrEqualThan(offer.RemainingBalance, 0, "The new quantity must be more than zero");
			var difference = request.OfferDto.RemainingBalance - offer.RemainingBalance;
			var isAddition = difference > 0;
			if (offer.ContractParentId.HasValue) //is from price via offer
			{
				await UpdateParentContract(offer, Math.Abs(difference), isAddition, cancellationToken);
			}
			if (isAddition)
				offer.AddQuantity(difference);
			else
				offer.ReduceQuantity(Math.Abs(difference));

			if (offer.IsInternal)
			{
				await _offerWorkflowContext.ProcessOffer(offer, bidSheetInformation);
			}
			else
			{
				await HandleLimitOrder(bidSheetInformation, offer, cancellationToken);
			}
			return true;
		}
		return false;
	}

	async Task HandleLimitOrder(BidSheetItemDto bidSheetInformation, Offer offer, CancellationToken cancellationToken)
	{
		var marketTransaction = await _dbContext.MarketTransactions
			.OrderByDescending(x => x.CreatedOn)
			.FirstOrDefaultAsync(x => x.OfferId == offer.Id && x.Source == EMarketTransactionSource.Offer, cancellationToken);

		var commodity = await _dbContext.Set<Commodity>().FirstOrDefaultAsync(c => c.Id == offer.CommodityId, cancellationToken);
		if (commodity.LotFactor > offer.RemainingBalance)
			offer.MakePrivate();

		if (offer.RemainingBalance == 0)
			await _offerWorkflowContext.FillOffer(offer.Id, 0, offer.Price);

		if (marketTransaction != null && offer.IsInternal)
		{
			if (marketTransaction.WasAlreadySent)
			{
				_deleteScheduledJob.DeleteScheduledEditOrderRequestHangfireJob(marketTransaction);
				marketTransaction.StartCancelation();
				marketTransaction.CancelEvent();
				offer.UpdateOfferStatus(new(EOfferState.Working));
				offer.UpdateOfferEvent(ETransactionEvent.Creation);
			}
			else
			{
				marketTransaction.ConfirmCancelation();
				offer.CompleteOffer();
			}
			await _offerWorkflowContext.ProcessOffer(offer, bidSheetInformation);
		}
	}

	async Task UpdateParentContract(Offer offer, decimal difference, bool isAddition, CancellationToken cancellationToken)
	{
		var parentContract = await _dbContext.Contracts.FirstOrDefaultAsync(c => c.Id == offer.ContractParentId.Value, cancellationToken);
		AssertionConcern.ArgumentIsBiggerOrEqualThan(parentContract.RemainingBalance, difference, "The quantity of the offer cannot exceed the available quantity on it's parent contract");
		if (isAddition)
			parentContract.ReduceRemainingBalance(difference);
		else
			parentContract.AddRemainingBalance(difference);
	}
}
