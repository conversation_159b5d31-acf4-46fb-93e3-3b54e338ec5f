using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.OrderService.Domain;
using RJO.OrderService.Persistence;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Repositories;

namespace RJO.OrderService.Services.Handlers;

public class CloseLiveledgerCommand : IRequest<bool>, ITransactionalRequest
{
}

public class CloseLiveledgerCommandHandler : IRequestHandler<CloseLiveledgerCommand, bool>
{
	readonly BucketBalanceRepository _bucketBalanceRepository;
	readonly BalanceSnapshotRepository _balanceSnapshotRepository;
	readonly UnitOfWork _unitOfWork;
	readonly AppDbContext _dbContext;

	public CloseLiveledgerCommandHandler(BucketBalanceRepository bucketBalanceRepository, UnitOfWork unitOfWork, BalanceSnapshotRepository balanceSnapshotRepository, AppDbContext dbContext)
	{
		_bucketBalanceRepository = bucketBalanceRepository;
		_balanceSnapshotRepository = balanceSnapshotRepository;
		_unitOfWork = unitOfWork;
		_dbContext = dbContext;
	}

	public async Task<bool> Handle(CloseLiveledgerCommand request, CancellationToken cancellationToken)
	{
		var commodities = await _dbContext.Commodities.Where(x => x.IsActive).Select(x => new
		{
			x.Id,
			x.CropStartYear
		}).ToListAsync(cancellationToken);
		var regions = await _dbContext.Regions.Where(x => x.IsEnabled && x.IsActive).ToListAsync(cancellationToken);
		foreach (var region in regions)
			foreach (var commodity in commodities)
			{
				for (short i = 0; i < 4; i++)
				{
					await TakeSnapshotIfThereIsnt(commodity.Id, region.Id, (short)(i + commodity.CropStartYear));
				}
			}

		await _unitOfWork.CommitAsync();
		return false;
	}

	async Task<BalanceSnapshot> TakeSnapshotIfThereIsnt(Guid commodityId, Guid regionId, short cropYear)
	{
		var snapshot = await _balanceSnapshotRepository.TodayBalanceSnapshot(commodityId, regionId, cropYear);
		if (snapshot == null)
		{
			var balance = await _bucketBalanceRepository.GetOrCreateBalance(commodityId, regionId, cropYear);
			snapshot = new(balance.Balance(), balance.RejectedBalance(), commodityId, regionId, cropYear);
			await _balanceSnapshotRepository.InsertAsync(snapshot);
		}

		return snapshot;
	}
}
