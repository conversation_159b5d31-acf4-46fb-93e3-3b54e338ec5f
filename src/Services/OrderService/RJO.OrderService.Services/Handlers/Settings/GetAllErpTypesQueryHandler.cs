using MediatR;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.OrderService.Domain.Enumeration;

namespace RJO.OrderService.Services.Handlers.Settings;

public class GetAllErpTypesQuery : IRequest<List<string>>, ITransactionalRequest
{
}

public class GetAllErpTypesQueryHandler : IRequestHandler<GetAllErpTypesQuery, List<string>>
{
	public Task<List<string>> Handle(GetAllErpTypesQuery request, CancellationToken cancellationToken)
	{
		var list = new List<string>();

		foreach (var item in Enum.GetValues(typeof(ErpIntegrationTypeEnum)))
		{
			list.Add(item.ToString());
		}

		return Task.FromResult(list);
	}
}
