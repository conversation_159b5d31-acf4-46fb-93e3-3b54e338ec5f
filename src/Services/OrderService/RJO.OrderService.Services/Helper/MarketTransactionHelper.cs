using RJO.IntegrationEvents.Commons.Events;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;

namespace RJO.OrderService.Services.Helper;

public static class MarketTransactionHelper
{
	const string RejectLabel = "Rejected";

	public static string GetRejectDescription(this MarketTransaction transaction)
	{
		if (transaction.Source == EMarketTransactionSource.Accumulation)
		{
			return RejectLabel;
		}

		if (string.IsNullOrEmpty(transaction.InternalCode))
		{
			return RejectLabel;
		}

		return $"{transaction.InternalCode} Status: {RejectLabel}";
	}

	public static void RejectTransaction(this MarketTransaction transaction, OrderStatusEvent reject) => transaction.Reject(string.IsNullOrEmpty(reject.MarketRejectedReason) ? "unspecified reason" : reject.MarketRejectedReason);

	public static void PendingOperations(this MarketTransaction transaction)
	{
		if (transaction.IsOnStatus(EMarketTransactionState.Pending))
		{
			if (transaction.Event == ETransactionEvent.Edition)
			{
				transaction.ConfirmEdition();
			}
			else if (transaction.Event == ETransactionEvent.Creation)
			{
				transaction.ConfirmProcessing();
			}
		}
	}

	public static string GetInternalCode(string number)
	{
		if (string.IsNullOrEmpty(number)) return number;
		return number.Split('_')[0];
	}
}
