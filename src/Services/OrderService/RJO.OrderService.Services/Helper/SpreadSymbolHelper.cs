using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Services.Helper;

namespace RJO.OrderService.Services.Helper;

public static class SpreadSymbolHelper
{
	/// <summary>
	/// Generates a spread symbol for contract rolling based on CQG spread symbol format
	/// Example: F.US.ZCES1N25 (buys 1 ZCEN25 and sells 1 ZCEU25)
	/// </summary>
	/// <param name="productPrefix">Product prefix (e.g., "F.US.")</param>
	/// <param name="productCode">Product code (e.g., "ZCE")</param>
	/// <param name="oldFuturesMonth">Old contract futures month (e.g., "N25")</param>
	/// <param name="newFuturesMonth">New contract futures month (e.g., "U25")</param>
	/// <param name="availableFuturesMonths">Available futures months for the commodity</param>
	/// <returns>Spread symbol string</returns>
	public static string GenerateSpreadSymbol(string productPrefix, string productCode, 
		string oldFuturesMonth, string newFuturesMonth, char[] availableFuturesMonths)
	{
		AssertionConcern.ArgumentStringNotNullOrEmpty(productPrefix, "Product prefix is required");
		AssertionConcern.ArgumentStringNotNullOrEmpty(productCode, "Product code is required");
		AssertionConcern.ArgumentStringNotNullOrEmpty(oldFuturesMonth, "Old futures month is required");
		AssertionConcern.ArgumentStringNotNullOrEmpty(newFuturesMonth, "New futures month is required");
		AssertionConcern.ArgumentIsNotNull(availableFuturesMonths, "Available futures months are required");

		var oldFuturesInfo = FutureMonthsHelper.GetFuturesPartsFromFuturesMonth(oldFuturesMonth);
		var newFuturesInfo = FutureMonthsHelper.GetFuturesPartsFromFuturesMonth(newFuturesMonth);

		// Calculate jumps between the two contract months
		var jumps = FutureMonthsHelper.CalculateJumps(oldFuturesInfo, newFuturesInfo, availableFuturesMonths);

		// Generate spread symbol: ProductPrefix + ProductCode + "S" + Jumps + StartingMonth
		// Example: F.US.ZCES1N25
		var spreadSymbol = $"{productPrefix}{productCode}S{jumps}{oldFuturesMonth}";

		return spreadSymbol;
	}

	/// <summary>
	/// Generates individual contract symbols for the spread
	/// </summary>
	/// <param name="productPrefix">Product prefix (e.g., "F.US.")</param>
	/// <param name="productCode">Product code (e.g., "ZCE")</param>
	/// <param name="futuresMonth">Futures month (e.g., "N25")</param>
	/// <returns>Contract symbol string</returns>
	public static string GenerateContractSymbol(string productPrefix, string productCode, string futuresMonth)
	{
		AssertionConcern.ArgumentStringNotNullOrEmpty(productPrefix, "Product prefix is required");
		AssertionConcern.ArgumentStringNotNullOrEmpty(productCode, "Product code is required");
		AssertionConcern.ArgumentStringNotNullOrEmpty(futuresMonth, "Futures month is required");

		return $"{productPrefix}{productCode}{futuresMonth}";
	}
}
