using Hangfire;
using Hangfire.Server;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.MultiTenancyServer.Core;
using RJO.OrderService.Domain.MultiTenancyServer;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Repositories;

namespace RJO.OrderService.Services.Jobs.Jobs;

[Queue("critical")]
[AutomaticRetry(Attempts = 5)]
public class CreateEmployeeJob(IServiceProvider currentServiceProvider, AppDbContext appDbContext)
{
	public async Task Perform(CreateEmployeeJobOptions jobOptions, PerformContext performContext)
	{
		var @event = jobOptions.EmployeeCreateEvent;
		var tenancyContext = currentServiceProvider.GetRequiredService<ITenancyContext<ApplicationTenant>>();
		tenancyContext.Tenant = new()
		{
			Id = @event.TenantId,
			DisplayName = "EmployeeCreateJob"
		};

		AssertionConcern.ArgumentIsTrue(await appDbContext.Employees.AnyAsync(a => a.Number == @event.Number), "The ERP number is already assigned, please enter another.");

		var employee = new Domain.Employee(
			@event.Number,
			@event.FirstName,
			@event.LastName,
			@event.Email,
			null,
			@event.RoleId.ToString(),
			@event.Tag50,
			@event.Tag50Account);
		appDbContext.Employees.Add(employee);
		await appDbContext.SaveChangesAsync();
	}
}
