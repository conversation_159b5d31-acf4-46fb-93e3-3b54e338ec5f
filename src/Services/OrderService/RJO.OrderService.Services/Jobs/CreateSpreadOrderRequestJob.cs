using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.EventBus.Abstractions;
using RJO.IntegrationEvents.Commons.Events;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Services.DTO;
using RJO.OrderService.Services.Services;
using ETransactionEvent = RJO.OrderService.Domain.Enumeration.ETransactionEvent;

namespace RJO.OrderService.Services.Jobs;

[Queue("critical")]
[AutomaticRetry(Attempts = 5)]
public class CreateSpreadOrderRequestJob
{
	readonly IServiceProvider _currentServiceProvider;
	readonly ILogger<CreateSpreadOrderRequestJob> _logger;
	readonly MarketStatusService _marketStatusService;

	public CreateSpreadOrderRequestJob(IServiceProvider currentServiceProvider, ILogger<CreateSpreadOrderRequestJob> logger, MarketStatusService marketStatusService)
	{
		_currentServiceProvider = currentServiceProvider;
		_logger = logger;
		_marketStatusService = marketStatusService;
	}

	[DisplayName("CreateSpreadOrder: {0}")]
	public async Task Perform(CreateSpreadOrderRequestJobOptions jobOptions, PerformContext performContext)
	{
		performContext?.AddTags($"{jobOptions.InternalNumber}");
		var session = jobOptions.Session;
		if (session == null)
		{
			AssertionConcern.ArgumentIsNotNull(jobOptions.SpreadSymbol, GeneralResources.RequiredValueIsNotPresent);
			var marketStatus = await _marketStatusService.GetStatusForSymbol(jobOptions.SpreadSymbol, "spread");
			AssertionConcern.ArgumentIsNotNull(marketStatus, GeneralResources.SessionInformationNotAvail);
			session = marketStatus.Session;
		}

		using var scope = _currentServiceProvider.CreateScope();
		var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
		var marketTransactionRepository = scope.ServiceProvider.GetRequiredService<IMarketTransactionRepository>();
		var eventBus = scope.ServiceProvider.GetRequiredService<IEventBus>();
		var tag50ResolveService = scope.ServiceProvider.GetRequiredService<Tag50ResolveService>();

		var transaction = await marketTransactionRepository.GetById(jobOptions.TransactionId);
		AssertionConcern.ArgumentIsNotNull(transaction, $"{nameof(CreateSpreadOrderRequestJob)}:{transaction?.ClientNumber} Transaction not found.");
		if (!transaction.IsApproved || !transaction.IsOnStatus(EMarketTransactionState.Pending) || transaction.Event != ETransactionEvent.Creation)
		{
			_logger.LogInformation("{JobName}:{ClientNumber},[{TransactionState}:{TransactionEvent}] Transaction in incorrect status. IsApproved: {IsApproved}", 
				nameof(CreateSpreadOrderRequestJob), transaction.ClientNumber, transaction.State, transaction.Event, transaction.IsApproved);
			return;
		}

		var tag50 = await tag50ResolveService.GetTag50Query(session, jobOptions.Email, false, jobOptions.IsBypassOn);
		AssertionConcern.ArgumentIsNotNullOrEmpty(tag50, GeneralResources.RequiredValueIsNotPresent);

		var createSpreadOrderEvent = new CreateSpreadOrderEvent
		{
			Quantity = transaction.Lots,
			SpreadSymbol = transaction.SpreadSymbol,
			OldContractSymbol = transaction.OldContractSymbol,
			NewContractSymbol = transaction.NewContractSymbol,
			LimitPrice = transaction.Type == EMarketTransactionType.Limit ? transaction.FuturesPrice : (decimal?)null,
			IsMarketOrder = transaction.Type == EMarketTransactionType.Market,
			Limit = transaction.Expiration?.ToUniversalTime(),
			Duration = transaction.IsGtc ? (uint)EMarketDuration.GTC : (uint)EMarketDuration.GTD,
			ContractNumber = transaction.ClientNumber,
			IsSell = transaction.IsSell,
			AccountId = transaction.MarketAccount,
			TenantId = jobOptions.TenantId.Value.ToString(),
			Tag50 = tag50
		};

		transaction.SentToTheMarket();
		await unitOfWork.CommitAsync();

		await eventBus.Publish(createSpreadOrderEvent);
	}
}
