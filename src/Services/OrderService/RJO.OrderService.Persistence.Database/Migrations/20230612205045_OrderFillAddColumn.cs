using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RJO.OrderService.Persistence.Database.Migrations
{
    public partial class OrderFillAddColumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "Hedge2Erp",
                schema: "Settings",
                table: "TenantSetting",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Hedge<PERSON>ust<PERSON>",
                schema: "Settings",
                table: "TenantSetting",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HedgeLocation",
                schema: "Settings",
                table: "TenantSetting",
                type: "nvarchar(50)",
				maxLength: 50,
                nullable: true);

			migrationBuilder.AddColumn<string>(
                name: "HedgeEmployee",
                schema: "Settings",
                table: "TenantSetting",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ErpMessage",
                schema: "Transactions",
                table: "OrderFill",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ErpStatus",
                schema: "Transactions",
                table: "OrderFill",
                type: "int",
                nullable: false,
                defaultValue: 4);

            migrationBuilder.AddColumn<string>(
                name: "InternalCode",
                schema: "Transactions",
                table: "OrderFill",
                type: "nvarchar(22)",
                maxLength: 22,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Number",
                schema: "Transactions",
                table: "OrderFill",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

			migrationBuilder.CreateIndex(
                name: "IX_OrderFill_InternalCode",
                schema: "Transactions",
                table: "OrderFill",
                column: "InternalCode");

			migrationBuilder.CreateIndex(
                name: "IX_OrderFill_CreatedOn",
                schema: "Transactions",
                table: "OrderFill",
                column: "CreatedOn");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
			migrationBuilder.DropIndex("IX_OrderFill_InternalCode", "OrderFill", "Transactions");
			migrationBuilder.DropIndex("IX_OrderFill_CreatedOn", "OrderFill", "Transactions");

            migrationBuilder.DropColumn(
                name: "Hedge2Erp",
                schema: "Settings",
                table: "TenantSetting");

            migrationBuilder.DropColumn(
                name: "HedgeCustomer",
                schema: "Settings",
                table: "TenantSetting");

            migrationBuilder.DropColumn(
                name: "HedgeLocation",
                schema: "Settings",
                table: "TenantSetting");

            migrationBuilder.DropColumn(
                name: "HedgeEmployee",
                schema: "Settings",
                table: "TenantSetting");

            migrationBuilder.DropColumn(
                name: "ErpMessage",
                schema: "Transactions",
                table: "OrderFill");

            migrationBuilder.DropColumn(
                name: "ErpStatus",
                schema: "Transactions",
                table: "OrderFill");

            migrationBuilder.DropColumn(
                name: "InternalCode",
                schema: "Transactions",
                table: "OrderFill");

            migrationBuilder.DropColumn(
                name: "Number",
                schema: "Transactions",
                table: "OrderFill");
        }
    }
}
