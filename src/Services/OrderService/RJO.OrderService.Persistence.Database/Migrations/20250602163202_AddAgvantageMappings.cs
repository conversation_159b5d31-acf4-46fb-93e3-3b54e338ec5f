using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RJO.OrderService.Persistence.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddAgvantageMappings : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AgvantageContract",
                schema: "Transactions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ContractId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AgvantageId = table.Column<string>(type: "nvarchar(80)", maxLength: 80, nullable: false),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgvantageContract", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ErpIdMapping",
                schema: "ERP",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    SourceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DestinationValue = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ErpIdMapping", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AgvantageContract_ContractId",
                schema: "Transactions",
                table: "AgvantageContract",
                column: "ContractId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ErpIdMapping_Name_SourceId",
                schema: "ERP",
                table: "ErpIdMapping",
                columns: new[] { "Name", "SourceId" },
                unique: true);           
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
             migrationBuilder.DropTable(
                name: "AgvantageContract",
                schema: "Transactions");

            migrationBuilder.DropTable(
                name: "ErpIdMapping",
                schema: "ERP");
        }
    }
}
