using Microsoft.EntityFrameworkCore.Migrations;

namespace RJO.OrderService.Persistence.Database.Migrations
{
    public partial class newInternalCodeForLiveLedger : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ContractInternalCode",
                schema: "Transactions",
                table: "Transaction",
                type: "nvarchar(30)",
                maxLength: 30,
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "InternalCode",
                schema: "Transactions",
                table: "Offer",
                type: "nvarchar(22)",
                maxLength: 22,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "FreightPrice",
                schema: "Historical",
                table: "Offer",
                type: "decimal(18,4)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "Fees2",
                schema: "Historical",
                table: "Offer",
                type: "decimal(18,4)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "Fees1",
                schema: "Historical",
                table: "Offer",
                type: "decimal(18,4)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AddColumn<string>(
                name: "InternalCode",
                schema: "Historical",
                table: "Offer",
                type: "nvarchar(30)",
                maxLength: 30,
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "FuturesMonth",
                schema: "Transactions",
                table: "MarketTransaction",
                type: "nvarchar(4)",
                maxLength: 4,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500);

            migrationBuilder.AlterColumn<string>(
                name: "ClientNumber",
                schema: "Transactions",
                table: "MarketTransaction",
                type: "nvarchar(64)",
                maxLength: 64,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(30)",
                oldMaxLength: 30);
            /*
            migrationBuilder.AddColumn<string>(
                name: "InternalCode",
                schema: "Transactions",
                table: "MarketTransaction",
                type: "nvarchar(22)",
                maxLength: 22,
                nullable: true);
            */
            migrationBuilder.AlterColumn<string>(
                name: "InternalCode",
                schema: "Transactions",
                table: "Contract",
                type: "nvarchar(22)",
                maxLength: 22,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "InternalCode",
                schema: "Historical",
                table: "Contract",
                type: "nvarchar(30)",
                maxLength: 30,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ContractInternalCode",
                schema: "Transactions",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "InternalCode",
                schema: "Historical",
                table: "Offer");
            /*
            migrationBuilder.DropColumn(
                name: "InternalCode",
                schema: "Transactions",
                table: "MarketTransaction");
            */
            migrationBuilder.DropColumn(
                name: "InternalCode",
                schema: "Historical",
                table: "Contract");

            migrationBuilder.AlterColumn<string>(
                name: "InternalCode",
                schema: "Transactions",
                table: "Offer",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(22)",
                oldMaxLength: 22,
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "FreightPrice",
                schema: "Historical",
                table: "Offer",
                type: "decimal(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)");

            migrationBuilder.AlterColumn<decimal>(
                name: "Fees2",
                schema: "Historical",
                table: "Offer",
                type: "decimal(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)");

            migrationBuilder.AlterColumn<decimal>(
                name: "Fees1",
                schema: "Historical",
                table: "Offer",
                type: "decimal(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)");

            migrationBuilder.AlterColumn<string>(
                name: "FuturesMonth",
                schema: "Transactions",
                table: "MarketTransaction",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(4)",
                oldMaxLength: 4);

            migrationBuilder.AlterColumn<string>(
                name: "ClientNumber",
                schema: "Transactions",
                table: "MarketTransaction",
                type: "nvarchar(30)",
                maxLength: 30,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(64)",
                oldMaxLength: 64);

            migrationBuilder.AlterColumn<string>(
                name: "InternalCode",
                schema: "Transactions",
                table: "Contract",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(22)",
                oldMaxLength: 22,
                oldNullable: true);
        }
    }
}
