#nullable disable

using Microsoft.EntityFrameworkCore.Migrations;

namespace RJO.OrderService.Persistence.Database.Migrations
{
    public partial class AddingMyGrowerFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsMobileAppEnable",
                schema: "Settings",
                table: "Customer",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "MobileAppLimit",
                schema: "Settings",
                table: "Customer",
                type: "decimal(18,2)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsMobileAppEnable",
                schema: "Settings",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "MobileAppLimit",
                schema: "Settings",
                table: "Customer");
        }
    }
}
