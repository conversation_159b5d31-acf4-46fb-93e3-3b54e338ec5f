using Microsoft.EntityFrameworkCore.Migrations;

namespace RJO.OrderService.Persistence.Database.Migrations
{
    public partial class AddIndexBucketBalanceDeliveryDate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_BucketBalanceDeliveryDate_CommodityId_CropYear_DeliveryDate_Date_IsActive",
                schema: "Historical",
                table: "BucketBalanceDeliveryDate",
                columns: new[] { "CommodityId", "CropYear", "DeliveryDate", "Date", "IsActive" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_BucketBalanceDeliveryDate_CommodityId_CropYear_DeliveryDate_Date_IsActive",
                schema: "Historical",
                table: "BucketBalanceDeliveryDate");
        }
    }
}
