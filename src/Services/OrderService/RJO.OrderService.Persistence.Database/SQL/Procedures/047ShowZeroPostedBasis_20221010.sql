    exec sp_executesql N'Create OR ALTER procedure Logs.SPReportBasisPush(
                    	@tenantid		uniqueidentifier,
                    	@startdate		DATETIME,	
                    	@enddate		DATETIME,
						@userEmail		VARCHAR(MAX) = null,
                		@contractType   VARCHAR(max) = null,
                		@cropYear       VARCHAR(max) = null,
                    	@commodityD		VARCHAR(max) = null,
                    	@destination	VARCHAR(max) = null,
                    	@employeeD		VARCHAR(max) = NULL,
                		@transaction    VARCHAR(MAX) = null
						)
                    As
                    begin
                    	declare @tDestination		table(Id uniqueidentifier);
                    	declare @tCommodity			table(Id uniqueidentifier);
                    	declare @tEmployee			table(Id uniqueidentifier);
                    	declare @tContractType		table(Id UNIQUEIDENTIFIER);
                    	declare @tCropYear			table(Id smallint);
                		DECLARE @tTransaction   	TABLE(Id VARCHAR(MAX));
                    	declare @tEmployeeLocations	TABLE(Id uniqueidentifier);
                		DECLARE @tResults       	TABLE(id UNIQUEIDENTIFIER,
                										Created DATETIME,
                										Employee VARCHAR(MAX),
                										PS VARCHAR(1),
                										contractType VARCHAR(MAX),
                										tEvent VARCHAR(MAX),
                										Product VARCHAR(MAX),
                										Crop VARCHAR(MAX),
                										Quantity DECIMAL(18,4) null,
                										Futures DECIMAL(18,4) null,
                										Basis DECIMAL(18,4) null,
														PushBasis DECIMAL(18,4) null,
                										Freight DECIMAL(18,4) null,
                										fees DECIMAL(18,4) null,
                										FlatPrice DECIMAL(18,4) null,
                										OrderNo VARCHAR(MAX),
                										customer VARCHAR(MAX),
                										CustNo VARCHAR(MAX),
                										Comment VARCHAR(MAX),
                										Destination VARCHAR(MAX),
                										HrvstEdgeIsSubTotal bit,
                                                        HrvstEdgeIsTotal bit)						
                		DECLARE @tResultsEdit	TABLE(id UNIQUEIDENTIFIER,
                										Created DATETIME,
                										Employee VARCHAR(MAX),
                										PS VARCHAR(1),
                										contractType VARCHAR(MAX),
                										tEvent VARCHAR(MAX),
                										Product VARCHAR(MAX),
                										Crop VARCHAR(MAX),
                										Quantity DECIMAL(18,4) null,
                										Futures DECIMAL(18,4) null,
                										Basis DECIMAL(18,4) null,
														PushBasis DECIMAL(18,4) null,
                										Freight DECIMAL(18,4) null,
                										fees DECIMAL(18,4) null,
                										FlatPrice DECIMAL(18,4) null,
                										OrderNo VARCHAR(MAX),
                										customer VARCHAR(MAX),
                										CustNo VARCHAR(MAX),
                										Comment VARCHAR(MAX),
                										Destination VARCHAR(MAX))

                DECLARE @id UNIQUEIDENTIFIER,@Created DATETIME,@EmployeeC VARCHAR(MAX),@PS VARCHAR(1),@contractTypeC VARCHAR(MAX),@tEvent VARCHAR(MAX),
                @Product VARCHAR(MAX),@Crop VARCHAR(MAX),@Quantity decimal,@Futures DECIMAL(18,4),@Basis DECIMAL(18,4),@PushBasis DECIMAL(18,4),@Freight DECIMAL(18,4),
                @fees DECIMAL(18,4),@FlatPrice DECIMAL(18,4),@OrderNo VARCHAR(MAX),@customer VARCHAR(MAX),@CustNo VARCHAR(MAX),@Comment VARCHAR(MAX),
                 @currentDestination varchar(max),@qtyDestinationBushels decimal = 0
                		BEGIN
                			INSERT INTO @tEmployeeLocations
                				SELECT LocationId AS Id FROM Settings.LocationEmployee WHERE EmployeeId = 
									(SELECT TOP(1) Id FROM Settings.Employee WHERE Email = @userEmail)
   								AND (CanBuy = 1 OR CanSell = 1) AND IsActive = 1
                		END
            			IF @startdate = @enddate
            			BEGIN
            				SET @enddate = DATEADD(DAY, 1, @enddate)
            			END
                		IF @transaction IS NOT NULL AND LEN(@transaction) > 0
                		BEGIN
                			INSERT INTO @tTransaction
                			select value from string_split(@transaction,'','')
                		END
                		ELSE
                        BEGIN
                			SET @transaction = null
                		END

                    	if @destination is not null and len(@destination) > 0
                    	begin
                    		insert into @tDestination
                    		select value from string_split(@destination,'','')
                    	end
                    	else
                    	begin
                    		set @destination = null;
                    	end

                    	if @commodityD is not null and len(@commodityD) > 0
                    	begin
                    		insert into @tCommodity
                    		select value from string_split(@commodityD,'','')
                    	end
                    	else
                    	begin
                    		set @commodityD = null;
                    	end

                    	if @employeeD is not null and len(@employeeD) > 0
                    	begin
                    		insert into @tEmployee
                    		select value from string_split(@employeeD,'','')
                    	end
                    	else
                    	begin
                    		set @employeeD = null;
                    	end

                    	if @contractType is not null and len(@contractType) > 0
                    	begin
                    		insert into @tContractType
                    		select value from string_split(@contractType,'','')
                    	end
                    	else
                    	begin
                    		set @contractType = null;
                    	end
                    	
                		if @cropYear is not null and len(@cropYear) > 0
                    	begin
                    		insert into @tCropYear
                    		select value from string_split(@cropYear,'','')
                    	end
                    	else
                    	begin
                    		set @cropYear= null;
                    	end
    					
    					If (Select Count(Id) From @tTransaction Where Id = ''Edit'') > 0 
    					Begin
    						Insert Into @tResultsEdit
    						SELECT 	h.Id,
    								h.CreatedOn AS Created,
    								e.FirstName + '' '' + e.LastName AS Employee,
    								CASE WHEN h.IsSell = 0 THEN ''P'' ELSE ''S'' END AS PS,
    								ct.Name AS contractType,
    								h.Event AS tEvent,
    								co.Name AS Product,
    								h.cropyear AS Crop,
    								CASE WHEN h.IsSell = 0 AND h.Event <> ''Cancel'' THEN h.Quantity 
    								WHEN h.IsSell = 0 AND h.Event = ''Cancel'' THEN h.Quantity * -1
    								WHEN h.IsSell = 1 AND h.Event <> ''Cancel'' THEN h.Quantity * -1
    								WHEN h.IsSell = 1 AND h.Event = ''Cancel'' THEN h.Quantity
    								END AS Quantity,
    								h.FuturesPrice AS Futures,
    								h.NetBasis AS Basis,
									h.PushBasis,
    								h.FreightPrice AS Freight,
    								h.Fees1 + h.Fees2 AS fees,
    								h.Price AS FlatPrice,
    								ISNULL(h.Number,'''') AS OrderNo,
    								cu.FirstName + '' '' + cu.LastName AS customer,
    								ISNULL(cu.Number,'''') as CustNo,
    								h.Comments AS Comment,
    								l.Name AS Destination
									From Historical.Contract h
    								Inner Join Transactions.Contract c On (c.Id = h.ContractId)
    								Full Outer Join Transactions.Offer o On (o.Id = c.OfferId)
        							Inner Join Catalogs.ContractType ct on h.ContractTypeId = ct.Id  
        							Inner Join Settings.[Location] l on (h.LocationId = l.Id)
        							Inner Join Settings.Employee e on  h.EmployeeId = e.Id
        							Inner Join Settings.Customer cu on h.CustomerId = cu.Id
        							Inner Join Settings.Commodity co on h.CommodityId = co.Id
    							Where (c.TenantId=@tenantid)
    							And h.Event = ''Edit''
    							And (o.Status = ''Filled'' Or o.Status Is Null)
    						And (@destination	Is Null Or h.LocationId	In (select Id from @tDestination))
    						And (@employeeD		Is Null Or h.EmployeeId	In (select Id from @tEmployee))
    						And (@commodityD	is Null Or h.CommodityId	In (select Id from @tCommodity))
    						And (@contractType	Is Null Or h.ContractTypeId	In (select Id from @tContractType))
    						And (@cropYear      Is Null Or h.CropYear      In (SELECT Id FROM @tCropYear))
    						And (@transaction   Is Null Or h.Event	In (SELECT Id FROM @tTransaction))
    						And (h.CreatedOn > @startdate)
    						AND (h.CreatedOn < @enddate)
                            AND	(@userEmail     Is Null Or h.LocationId in (SELECT Id FROM @tEmployeeLocations))
    						ORDER BY l.Name, h.CreatedOn
    					End

                		Declare curse cursor for
                    	SELECT
                		    t.id,
                    		t.CreatedOn AS Created,
                			e.FirstName + '' '' + e.LastName AS Employee,
                			CASE WHEN t.IsSell = 0 THEN ''P'' ELSE ''S'' END AS PS,
                			c.Name AS contractType,
                			t.TransactionEvent AS tEvent,
                			co.Name AS Product,
                			t.cropyear AS Crop,
                			CASE WHEN t.IsSell = 0 AND t.TransactionEvent <> ''Cancel'' THEN t.Quantity 
                			WHEN t.IsSell = 0 AND t.TransactionEvent = ''Cancel'' THEN t.Quantity * -1
                			WHEN t.IsSell = 1 AND t.TransactionEvent <> ''Cancel'' THEN t.Quantity * -1
                			WHEN t.IsSell = 1 AND t.TransactionEvent = ''Cancel'' THEN t.Quantity
                			END AS Quantity,
                			con.FuturesPrice AS Futures,
                			con.NetBasis AS Basis,
							con.PushBasis,
                			con.FreightPrice AS Freight,
                			con.Fees1 + con.Fees2 AS fees,
                			con.Price AS FlatPrice,
                			ISNULL(con.Number,'''') AS OrderNo,
                			cu.FirstName + '' '' + cu.LastName AS customer,
                			ISNULL(cu.Number,'''') as CustNo,
                			con.Comments AS Comment,
                			l.Name AS Destination
                    	from Transactions.[Transaction] t
                    	inner join Catalogs.ContractType c on c.Id = t.ContractTypeId
                    	inner join Settings.Employee e on e.Id = t.EmployeeId
                    	inner join Settings.Customer cu on t.CustomerId = cu.Id
                    	inner join Settings.Commodity co on t.CommodityId = co.Id
                    	inner join Settings.[Location] l on t.LocationId = l.Id
                		INNER JOIN Transactions.[Contract] con ON t.ContractId = con.Id
                    	where t.TenantId=@tenantid and t.ContractId IS NOT NULL
                    		and (@destination	is null or t.LocationId	in (select Id from @tDestination))
                    		and (@employeeD		is null or t.EmployeeId			in (select Id from @tEmployee))
                    		and (@commodityD		is null or t.CommodityId		in (select Id from @tCommodity))
                			and (@contractType	is null or t.ContractTypeId		in (select Id from @tContractType))
                			AND (@cropYear      IS NULL OR t.CropYear           IN (SELECT id FROM @tCropYear))
                			AND (@transaction   IS NULL OR t.TransactionEvent   IN (SELECT id FROM @tTransaction))
                			AND ( c.Name = ''Flat Price'' AND t.TransactionEvent IN (''Create'', ''Edit'',''Cancel'') 
                				 OR c.Name = ''HTA'' AND t.TransactionEvent IN (''Create'',''Edit'', ''Roll'', ''Cancel'',''Price'') 
                				 OR c.Name = ''Basis'' AND t.TransactionEvent IN (''Price'',''Cancel'',''Create'',''Edit'',''Roll''))	
                            AND	(@userEmail     Is Null Or t.LocationId in (SELECT Id FROM @tEmployeeLocations))
                			AND t.TransactionType <> ''Hedge''
                			AND (t.CreatedOn > @startdate)
                			AND (t.CreatedOn < @enddate)
							AND con.PushBasis <> 0
							AND con.PushBasis is not null
    					Union All 
    					Select * From @tResultsEdit
                    	order by l.Name,t.CreatedOn
                	    
                		OPEN curse
                        fetch next from curse into @id ,@Created,@EmployeeC ,@PS,@contractTypeC ,@tEvent ,@Product ,@Crop ,@Quantity ,@Futures ,@Basis, @PushBasis ,@Freight,@fees ,@FlatPrice ,@OrderNo ,@customer,@CustNo,@Comment,@Destination
                  set @currentDestination = @Destination;

                  while @@FETCH_STATUS = 0
                  BEGIN
                	
                	 IF @currentDestination <> @Destination
                	 BEGIN
                	
                		INSERT INTO @tResults(id,Created,Employee,
                		    PS,contractType,tEvent,Product,Crop,Quantity,Futures,Basis,PushBasis,Freight,fees,FlatPrice,OrderNo,customer,CustNo,Comment, Destination,HrvstEdgeIsSubTotal, HrvstEdgeIsTotal)
                		VALUES(NEWID(),null, '''', '''','''','''' ,'''','''' ,@qtyDestinationBushels ,null ,null, null ,null,null ,null,'''' ,'''','''','''', @currentDestination + '' Total'',1,0)
                		 SET  @qtyDestinationBushels = 0
                	 END

                	 SET  @qtyDestinationBushels =  @qtyDestinationBushels + @Quantity
                	 INSERT INTO @tResults(id,Created,Employee,
                		    PS,contractType,tEvent,Product,Crop,Quantity,Futures,Basis,PushBasis,Freight,fees,FlatPrice,OrderNo,customer,CustNo,Comment, Destination,HrvstEdgeIsSubTotal, HrvstEdgeIsTotal)
                		VALUES(@id,@Created, @EmployeeC, @PS,@contractTypeC,@tEvent,@Product ,@Crop ,@Quantity ,CASE WHEN @Futures = 0 THEN NULL ELSE @Futures end ,@Basis,CASE WHEN @PushBasis = 0 THEN NULL ELSE @PushBasis end,CASE WHEN @Freight = 0 THEN null ELSE @Freight end,CASE WHEN @fees = 0 THEN NULL ELSE @fees end ,CASE WHEN @FlatPrice = 0 THEN NULL ELSE @FlatPrice end ,@OrderNo ,@customer,@CustNo,@Comment, @Destination,0,0)


                	 SET @currentDestination = @Destination;

                	 fetch next from curse into @id ,@Created,@EmployeeC ,@PS,@contractTypeC ,@tEvent ,@Product ,@Crop ,@Quantity ,@Futures ,@Basis ,@PushBasis ,@Freight,@fees ,@FlatPrice ,@OrderNo ,@customer,@CustNo,@Comment,@Destination
                  END

                   IF @@CURSOR_ROWS > 0
                   BEGIN

                       INSERT INTO @tResults(id,Created,Employee,PS,contractType,tEvent,Product,Crop,Quantity,Futures,Basis,PushBasis,Freight,fees,FlatPrice,OrderNo,customer,CustNo,Comment, Destination,HrvstEdgeIsSubTotal, HrvstEdgeIsTotal)
                	   VALUES(NEWID(),null, '''', '''','''','''' ,'''','''' ,@qtyDestinationBushels ,null,null,null,null,null ,null,'''' ,'''','''','''', @currentDestination + '' Total'',1,0)
                	END
                	CLOSE curse
                     deallocate curse


                   SELECT id ,Created,Employee ,PS,contractType ,tEvent ,Product ,Crop ,Quantity ,Futures ,Basis,PushBasis ,Freight,fees ,FlatPrice ,OrderNo ,customer,CustNo,Comment,Destination, HrvstEdgeIsSubTotal, HrvstEdgeIsTotal FROM @tResults
END'
GO

exec sp_executesql N'CREATE OR ALTER procedure [Logs].[SPReportDailyActivity](
                    	@tenantid		uniqueidentifier,
                    	@startdate		DATETIME,	
                    	@enddate		DATETIME,
						@userEmail		VARCHAR(MAX) = null,
                		@contractType   varchar(max) = null,
                		@cropYear       varchar(max) = null,
                    	@commodityD		varchar(max) = null,
                    	@destination	varchar(max) = null,
                    	@employeeD		varchar(max) = NULL,
                		@transaction    VARCHAR(MAX) = null
                    )
                    As
                    begin
                    	declare @tDestination	    table(Id uniqueidentifier);
                    	declare @tCommodity		    table(Id uniqueidentifier);
                    	declare @tEmployee		    table(Id uniqueidentifier);
                    	declare @tContractType	    table(Id UNIQUEIDENTIFIER);
                    	declare @tCropYear		    table(Id smallint);
                		DECLARE @tTransaction       TABLE(Id VARCHAR(MAX));
                        declare @tEmployeeLocations	TABLE(Id uniqueidentifier);
                		DECLARE @tResults           TABLE(id UNIQUEIDENTIFIER,
                										Created DATETIME,
                										Employee VARCHAR(MAX),
                										PS VARCHAR(1),
                										contractType VARCHAR(MAX),
                										tEvent VARCHAR(MAX),
                										Product VARCHAR(MAX),
                										Crop VARCHAR(MAX),
                										Quantity DECIMAL(18,4) null,
                										Futures DECIMAL(18,4) null,
                										Basis DECIMAL(18,4) null,
                										Freight DECIMAL(18,4) null,
                										fees DECIMAL(18,4) null,
                										FlatPrice DECIMAL(18,4) null,
                										OrderNo VARCHAR(MAX),
                										customer VARCHAR(MAX),
                										CustNo VARCHAR(MAX),
                										Comment VARCHAR(MAX),
                										Destination VARCHAR(MAX),
                										HrvstEdgeIsSubTotal bit,
                                                        HrvstEdgeIsTotal bit)						
                		DECLARE @tResultsEdit	    TABLE(id UNIQUEIDENTIFIER,
                										Created DATETIME,
                										Employee VARCHAR(MAX),
                										PS VARCHAR(1),
                										contractType VARCHAR(MAX),
                										tEvent VARCHAR(MAX),
                										Product VARCHAR(MAX),
                										Crop VARCHAR(MAX),
                										Quantity DECIMAL(18,4) null,
                										Futures DECIMAL(18,4) null,
                										Basis DECIMAL(18,4) null,
                										Freight DECIMAL(18,4) null,
                										fees DECIMAL(18,4) null,
                										FlatPrice DECIMAL(18,4) null,
                										OrderNo VARCHAR(MAX),
                										customer VARCHAR(MAX),
                										CustNo VARCHAR(MAX),
                										Comment VARCHAR(MAX),
                										Destination VARCHAR(MAX))

                DECLARE @id UNIQUEIDENTIFIER,@Created DATETIME,@EmployeeC VARCHAR(MAX),@PS VARCHAR(1),@contractTypeC VARCHAR(MAX),@tEvent VARCHAR(MAX),
                @Product VARCHAR(MAX),@Crop VARCHAR(MAX),@Quantity decimal,@Futures DECIMAL(18,4),@Basis DECIMAL(18,4),@Freight DECIMAL(18,4),
                @fees DECIMAL(18,4),@FlatPrice DECIMAL(18,4),@OrderNo VARCHAR(MAX),@customer VARCHAR(MAX),@CustNo VARCHAR(MAX),@Comment VARCHAR(MAX),
                 @currentDestination varchar(max), @qtyDestinationBushels decimal = 0
                        IF @userEmail is not null
            			BEGIN
                			INSERT INTO @tEmployeeLocations
                				SELECT LocationId AS Id FROM Settings.LocationEmployee WHERE EmployeeId = 
									(SELECT TOP(1) Id FROM Settings.Employee WHERE Email = @userEmail)
   								AND (CanBuy = 1 OR CanSell = 1) AND IsActive = 1
                		END
                        IF @startdate = @enddate
            			BEGIN
            				SET @enddate = DATEADD(DAY, 1, @enddate)
            			END
                		IF @transaction IS NOT NULL AND LEN(@transaction) > 0
                		BEGIN
                			INSERT INTO @tTransaction
                			select value from string_split(@transaction,'','')
                		END
                		ELSE
                        BEGIN
                			SET @transaction = null
                		END

                    	if @destination is not null and len(@destination) > 0
                    	begin
                    		insert into @tDestination
                    		select value from string_split(@destination,'','')
                    	end
                    	else
                    	begin
                    		set @destination = null;
                    	end

                    	if @commodityD is not null and len(@commodityD) > 0
                    	begin
                    		insert into @tCommodity
                    		select value from string_split(@commodityD,'','')
                    	end
                    	else
                    	begin
                    		set @commodityD = null;
                    	end

                    	if @employeeD is not null and len(@employeeD) > 0
                    	begin
                    		insert into @tEmployee
                    		select value from string_split(@employeeD,'','')
                    	end
                    	else
                    	begin
                    		set @employeeD = null;
                    	end

                    	if @contractType is not null and len(@contractType) > 0
                    	begin
                    		insert into @tContractType
                    		select value from string_split(@contractType,'','')
                    	end
                    	else
                    	begin
                    		set @contractType = null;
                    	end
                    	
                		if @cropYear is not null and len(@cropYear) > 0
                    	begin
                    		insert into @tCropYear
                    		select value from string_split(@cropYear,'','')
                    	end
                    	else
                    	begin
                    		set @cropYear= null;
                    	end
    					
    					If (Select Count(Id) From @tTransaction Where Id = ''Edit'') > 0 
    					Begin
    						Insert Into @tResultsEdit
    						SELECT 	h.Id,
    								h.CreatedOn AS Created,
    								e.FirstName + '' '' + e.LastName AS Employee,
    								CASE WHEN h.IsSell = 0 THEN ''P'' ELSE ''S'' END AS PS,
    								ct.Name AS contractType,
    								h.Event AS tEvent,
    								co.Name AS Product,
    								h.cropyear AS Crop,
    								CASE WHEN h.IsSell = 0 AND h.Event <> ''Cancel'' THEN h.Quantity 
    								WHEN h.IsSell = 0 AND h.Event = ''Cancel'' THEN h.Quantity * -1
    								WHEN h.IsSell = 1 AND h.Event <> ''Cancel'' THEN h.Quantity * -1
    								WHEN h.IsSell = 1 AND h.Event = ''Cancel'' THEN h.Quantity
    								END AS Quantity,
    								h.FuturesPrice AS Futures,
    								h.PostedBasis AS Basis,
    								h.FreightPrice AS Freight,
    								h.Fees1 + h.Fees2 AS fees,
    								h.Price AS FlatPrice,
    								ISNULL(h.Number,'''') AS OrderNo,
    								cu.FirstName + '' '' + cu.LastName AS customer,
    								ISNULL(cu.Number,'''') as CustNo,
    								h.Comments AS Comment,
    								l.Name AS Destination   
    							From Historical.Contract h
    								Inner Join Transactions.Contract c On (c.Id = h.ContractId)
    								Full Outer Join Transactions.Offer o On (o.Id = c.OfferId)
        							Inner Join Catalogs.ContractType ct on h.ContractTypeId = ct.Id  
        							Inner Join Settings.[Location] l on (h.LocationId = l.Id)
        							Inner Join Settings.Employee e on  h.EmployeeId = e.Id
        							Inner Join Settings.Customer cu on h.CustomerId = cu.Id
        							Inner Join Settings.Commodity co on h.CommodityId = co.Id
    							Where (c.TenantId=@tenantid)
    							And h.Event = ''Edit''
    							And (o.Status = ''Filled'' Or o.Status Is Null)
    						And (@destination	Is Null Or h.LocationId	In (select Id from @tDestination))
    						And (@employeeD		Is Null Or h.EmployeeId	In (select Id from @tEmployee))
    						And (@commodityD	is Null Or h.CommodityId	In (select Id from @tCommodity))
    						And (@contractType	Is Null Or h.ContractTypeId	In (select Id from @tContractType))
    						And (@cropYear      Is Null Or h.CropYear      In (SELECT Id FROM @tCropYear))
    						And (@transaction   Is Null Or h.Event	In (SELECT Id FROM @tTransaction))
                            AND	(@userEmail     Is Null Or h.LocationId in (SELECT Id FROM @tEmployeeLocations))
    						And (h.CreatedOn > @startdate)
    						AND (h.CreatedOn < @enddate)
    						ORDER BY l.Name, h.CreatedOn
    					End

                		Declare curse cursor for
                    	SELECT
                		    t.id,
                    		t.CreatedOn AS Created,
                			e.FirstName + '' '' + e.LastName AS Employee,
                			CASE WHEN t.IsSell = 0 THEN ''P'' ELSE ''S'' END AS PS,
                			c.Name AS contractType,
                			t.TransactionEvent AS tEvent,
                			co.Name AS Product,
                			t.cropyear AS Crop,
                			CASE WHEN t.IsSell = 0 AND t.TransactionEvent <> ''Cancel'' THEN t.Quantity 
                			WHEN t.IsSell = 0 AND t.TransactionEvent = ''Cancel'' THEN t.Quantity * -1
                			WHEN t.IsSell = 1 AND t.TransactionEvent <> ''Cancel'' THEN t.Quantity * -1
                			WHEN t.IsSell = 1 AND t.TransactionEvent = ''Cancel'' THEN t.Quantity
                			END AS Quantity,
                			con.FuturesPrice AS Futures,
                			con.PostedBasis AS Basis,
                			con.FreightPrice AS Freight,
                			con.Fees1 + con.Fees2 AS fees,
                			con.Price AS FlatPrice,
                			ISNULL(con.Number,'''') AS OrderNo,
                			cu.FirstName + '' '' + cu.LastName AS customer,
                			ISNULL(cu.Number,'''') as CustNo,
                			con.Comments AS Comment,
                			l.Name AS Destination  
                    	from Transactions.[Transaction] t
                    	inner join Catalogs.ContractType c on c.Id = t.ContractTypeId
                    	inner join Settings.Employee e on e.Id = t.EmployeeId
                    	inner join Settings.Customer cu on t.CustomerId = cu.Id
                    	inner join Settings.Commodity co on t.CommodityId = co.Id
                    	inner join Settings.[Location] l on t.LocationId = l.Id
                		INNER JOIN Transactions.[Contract] con ON t.ContractId = con.Id
                    	where t.TenantId=@tenantid and t.ContractId IS NOT NULL
                    		and (@destination	is null or t.LocationId	in (select Id from @tDestination))
                    		and (@employeeD		is null or t.EmployeeId			in (select Id from @tEmployee))
                    		and (@commodityD		is null or t.CommodityId		in (select Id from @tCommodity))
                			and (@contractType	is null or t.ContractTypeId		in (select Id from @tContractType))
                			AND (@cropYear      IS NULL OR t.CropYear           IN (SELECT id FROM @tCropYear))
                			AND (@transaction   IS NULL OR t.TransactionEvent   IN (SELECT id FROM @tTransaction))
                			AND	(@userEmail     Is Null Or t.LocationId in (SELECT Id FROM @tEmployeeLocations))
                            AND ( c.Name = ''Flat Price'' AND t.TransactionEvent IN (''Create'', ''Edit'',''Cancel'') 
                				 OR c.Name = ''HTA'' AND t.TransactionEvent IN (''Create'',''Edit'', ''Roll'', ''Cancel'',''Price'') 
                				 OR c.Name = ''Basis'' AND t.TransactionEvent IN (''Price'',''Cancel'',''Create'',''Edit'',''Roll''))	
                			AND t.TransactionType <> ''Hedge''
                			AND (t.CreatedOn > @startdate)
                			AND (t.CreatedOn < @enddate)
    					Union All 
    					Select * From @tResultsEdit
                    	order by l.Name,t.CreatedOn
                	    
                		OPEN curse
                        fetch next from curse into @id ,@Created,@EmployeeC ,@PS,@contractTypeC ,@tEvent ,@Product ,@Crop ,@Quantity ,@Futures ,@Basis ,@Freight,@fees ,@FlatPrice ,@OrderNo ,@customer,@CustNo,@Comment,@Destination
                  set @currentDestination = @Destination;

                  while @@FETCH_STATUS = 0
                  BEGIN
                	
                	 IF @currentDestination <> @Destination
                	 BEGIN
                	
                		INSERT INTO @tResults(id,Created,Employee,
                		    PS,contractType,tEvent,Product,Crop,Quantity,Futures,Basis,Freight,fees,FlatPrice,OrderNo,customer,CustNo,Comment, Destination,HrvstEdgeIsSubTotal, HrvstEdgeIsTotal)
                		VALUES(NEWID(),null, '''', '''','''','''' ,'''','''' ,@qtyDestinationBushels ,null ,null ,null,null ,null,'''' ,'''','''','''', @currentDestination + '' Total'',1,0)
                		 SET  @qtyDestinationBushels = 0
                	 END

                	 SET  @qtyDestinationBushels =  @qtyDestinationBushels + @Quantity
                	 INSERT INTO @tResults(id,Created,Employee,
                		    PS,contractType,tEvent,Product,Crop,Quantity,Futures,Basis,Freight,fees,FlatPrice,OrderNo,customer,CustNo,Comment, Destination,HrvstEdgeIsSubTotal, HrvstEdgeIsTotal)
                		VALUES(@id,@Created, @EmployeeC, @PS,@contractTypeC,@tEvent,@Product ,@Crop ,@Quantity ,CASE WHEN @Futures = 0 THEN NULL ELSE @Futures end ,@Basis,CASE WHEN @Freight = 0 THEN null ELSE @Freight end,CASE WHEN @fees = 0 THEN NULL ELSE @fees end ,CASE WHEN @FlatPrice = 0 THEN NULL ELSE @FlatPrice end ,@OrderNo ,@customer,@CustNo,@Comment, @Destination,0,0)


                	 SET @currentDestination = @Destination;

                	 fetch next from curse into @id ,@Created,@EmployeeC ,@PS,@contractTypeC ,@tEvent ,@Product ,@Crop ,@Quantity ,@Futures ,@Basis ,@Freight,@fees ,@FlatPrice ,@OrderNo ,@customer,@CustNo,@Comment,@Destination
                  END

                   IF @@CURSOR_ROWS > 0
                   BEGIN

                       INSERT INTO @tResults(id,Created,Employee,PS,contractType,tEvent,Product,Crop,Quantity,Futures,Basis,Freight,fees,FlatPrice,OrderNo,customer,CustNo,Comment, Destination,HrvstEdgeIsSubTotal, HrvstEdgeIsTotal)
                	   VALUES(NEWID(),null, '''', '''','''','''' ,'''','''' ,@qtyDestinationBushels ,null,null ,null,null ,null,'''' ,'''','''','''', @currentDestination + '' Total'',1,0)
                	END
                	CLOSE curse
                     deallocate curse


                   SELECT id ,Created,Employee ,PS,contractType ,tEvent ,Product ,Crop ,Quantity ,Futures ,Basis ,Freight,fees ,FlatPrice ,OrderNo ,customer,CustNo,Comment,Destination, HrvstEdgeIsSubTotal, HrvstEdgeIsTotal FROM @tResults
END'
GO