exec sp_executesql N'ALTER    PROCEDURE [Logs].[SPReportDailyActivity](
                                	@tenantid		uniqueidentifier,
                                	@startdate		DATETIME,	
                                	@enddate		DATETIME,
            						@userEmail		VARCHAR(MAX) = null,
                            		@contractType   varchar(max) = null,
                            		@cropYear       varchar(max) = null,
                                	@commodityD		varchar(max) = null,
                                	@destination	varchar(max) = null,
                                	@employeeD		varchar(max) = NULL,
                            		@transaction    VARCHAR(MAX) = null,
    								@region			VARCHAR(MAX) = null,
    								@isGroupedPermissionEnabled BIT = 0
                                )
                                As
                                begin
                                	declare @tDestination	    table(Id uniqueidentifier);
                                	declare @tCommodity		    table(Id uniqueidentifier);
                                	declare @tEmployee		    table(Id uniqueidentifier);
                                	declare @tContractType	    table(Id UNIQUEIDENTIFIER);
                                	declare @tCropYear		    table(Id smallint);
                            		DECLARE @tTransaction       TABLE(Id VARCHAR(MAX));
                                    declare @tEmployeeLocations	TABLE(Id uniqueidentifier);
    								DECLARE @tGroupedEmployeeLocations TABLE (
    									Id uniqueidentifier,
    									DestinationId uniqueidentifier,
    									CanBuy bit,
    									CanSell bit,
    									ContractLocationId uniqueidentifier,
    									RegionId uniqueidentifier
    								);
    								declare @tRegion		table(Id uniqueidentifier);
                            		DECLARE @tResults           TABLE(id UNIQUEIDENTIFIER,
                            										Created DATETIME,
                            										Employee VARCHAR(MAX),
                            										PS VARCHAR(1),
                            										contractType VARCHAR(MAX),
                            										tEvent VARCHAR(MAX),
                            										Product VARCHAR(MAX),
                            										Crop VARCHAR(MAX),
                            										Quantity DECIMAL(18,4) null,
                            										Futures DECIMAL(18,4) null,
                            										Basis DECIMAL(18,4) null,
                            										Freight DECIMAL(18,4) null,
                            										fees DECIMAL(18,4) null,
                            										FlatPrice DECIMAL(18,4) null,
                            										OrderNo VARCHAR(MAX),
                            										customer VARCHAR(MAX),
                            										CustNo VARCHAR(MAX),
                            										Comment VARCHAR(MAX),
                            										Destination VARCHAR(MAX),
                            										HrvstEdgeIsSubTotal bit,
                                                                    HrvstEdgeIsTotal bit,
        															theirContract VARCHAR(MAX),
    																Region varchar(max))						
                            		DECLARE @tResultsEdit	    TABLE(id UNIQUEIDENTIFIER,
                            										Created DATETIME,
                            										Employee VARCHAR(MAX),
                            										PS VARCHAR(1),
                            										contractType VARCHAR(MAX),
                            										tEvent VARCHAR(MAX),
                            										Product VARCHAR(MAX),
                            										Crop VARCHAR(MAX),
                            										Quantity DECIMAL(18,4) null,
                            										Futures DECIMAL(18,4) null,
                            										Basis DECIMAL(18,4) null,
                            										Freight DECIMAL(18,4) null,
                            										fees DECIMAL(18,4) null,
                            										FlatPrice DECIMAL(18,4) null,
                            										OrderNo VARCHAR(MAX),
                            										customer VARCHAR(MAX),
                            										CustNo VARCHAR(MAX),
                            										Comment VARCHAR(MAX),
                            										Destination VARCHAR(MAX),
        															theirContract VARCHAR(MAX),
    																Region varchar(max))

                            DECLARE @id UNIQUEIDENTIFIER,@Created DATETIME,@EmployeeC VARCHAR(MAX),@PS VARCHAR(1),@contractTypeC VARCHAR(MAX),@tEvent VARCHAR(MAX),
                            @Product VARCHAR(MAX),@Crop VARCHAR(MAX),@Quantity decimal,@Futures DECIMAL(18,4),@Basis DECIMAL(18,4),@Freight DECIMAL(18,4),
                            @fees DECIMAL(18,4),@FlatPrice DECIMAL(18,4),@OrderNo VARCHAR(MAX),@customer VARCHAR(MAX),@CustNo VARCHAR(MAX),@Comment VARCHAR(MAX),
                             @currentDestination varchar(max), @qtyDestinationBushels decimal = 0, @theirContract VARCHAR(MAX),@RegionName varchar(max)
                                    IF @userEmail is not null
                        			BEGIN
                            			INSERT INTO @tEmployeeLocations
                            				SELECT LocationId AS Id FROM Settings.LocationEmployee WHERE EmployeeId = 
            									(SELECT TOP(1) Id FROM Settings.Employee WHERE Email = @userEmail)
               								AND (CanBuy = 1 OR CanSell = 1) AND IsActive = 1
                            		INSERT INTO @tGroupedEmployeeLocations
    									SELECT DISTINCT
    										gle.Id,
    										gl.DestinationLocationId AS DestinationId,
    										gle.CanBuy,
    										gle.CanSell,
    										gl.ContractLocationId AS ContractLocationId,
    										gl.RegionId AS RegionId
    									FROM Settings.GroupedLocationEmployee gle
    									INNER JOIN Settings.GroupedLocation gl ON gle.GroupedLocationId = gl.Id
    									WHERE 
    										(gle.CanBuy = 1 OR gle.CanSell = 1) AND gle.IsActive = 1
    										AND EmployeeId = (SELECT TOP(1) Id FROM Settings.Employee WHERE Email = @userEmail)          
    								END
                       				SET @enddate = DATEADD(DAY, 1, @enddate)
                            		IF @transaction IS NOT NULL AND LEN(@transaction) > 0
                            		BEGIN
                            			INSERT INTO @tTransaction
                            			select value from string_split(@transaction,'','')
                            		END
                            		ELSE
                                    BEGIN
                            			SET @transaction = null
                            		END

                                	if @destination is not null and len(@destination) > 0
                                	begin
                                		insert into @tDestination
                                		select value from string_split(@destination,'','')
                                	end
                                	else
                                	begin
                                		set @destination = null;
                                	end

                                	if @commodityD is not null and len(@commodityD) > 0
                                	begin
                                		insert into @tCommodity
                                		select value from string_split(@commodityD,'','')
                                	end
                                	else
                                	begin
                                		set @commodityD = null;
                                	end

                                	if @employeeD is not null and len(@employeeD) > 0
                                	begin
                                		insert into @tEmployee
                                		select value from string_split(@employeeD,'','')
                                	end
                                	else
                                	begin
                                		set @employeeD = null;
                                	end

                                	if @contractType is not null and len(@contractType) > 0
                                	begin
                                		insert into @tContractType
                                		select value from string_split(@contractType,'','')
                                	end
                                	else
                                	begin
                                		set @contractType = null;
                                	end
                                	
                            		if @cropYear is not null and len(@cropYear) > 0
                                	begin
                                		insert into @tCropYear
                                		select value from string_split(@cropYear,'','')
                                	end
                                	else
                                	begin
                                		set @cropYear= null;
                                	end
                					if @region is not null and len(@region) > 0
    									begin
    										insert into @tRegion
    										select value from string_split(@region,'','')
    									end
    								else
    									begin
    										set @region = null;
    									end

                					If (Select Count(Id) From @tTransaction Where Id = ''Edit'') > 0 
                					BEGIN
    									Insert Into @tResultsEdit
                						SELECT 	
    										h.Id,
                							h.CreatedOn AS Created,
                							e.FirstName + '' '' + e.LastName AS Employee,
    										CASE WHEN h.IsSell = 0 THEN ''P'' ELSE ''S'' END AS PS,
    										ct.Name AS contractType,
    										h.Event AS tEvent,
    										co.Name AS Product,
    										h.cropyear AS Crop,
    										CASE WHEN h.IsSell = 0 AND h.Event <> ''Cancel'' THEN h.Quantity 
    										WHEN h.IsSell = 0 AND h.Event = ''Cancel'' THEN h.Quantity * -1
    										WHEN h.IsSell = 1 AND h.Event <> ''Cancel'' THEN h.Quantity * -1
    										WHEN h.IsSell = 1 AND h.Event = ''Cancel'' THEN h.Quantity
    										END AS Quantity,
    										h.FuturesPrice AS Futures,
                							h.NetBasis AS Basis,
                							h.FreightPrice AS Freight,
                							h.Fees1 + h.Fees2 AS fees,
                							h.Price AS FlatPrice,
                							ISNULL(h.Number,'''') AS OrderNo,
                							cu.FirstName + '' '' + cu.LastName AS customer,
                							ISNULL(cu.Number,'''') as CustNo,
                							h.Comments AS Comment,
                							l.Name AS Destination,
        									h.TheirContract,
    										rr.Name as Region
    									FROM 
    										Historical.Contract h
    									INNER JOIN 
    										Transactions.Contract c ON (c.Id = h.ContractId)
    									FULL OUTER JOIN 
    										Transactions.Offer o ON (o.Id = c.OfferId)
    									INNER JOIN 
    										Catalogs.ContractType ct ON h.ContractTypeId = ct.Id  
    									INNER JOIN 
    										Settings.[Location] l ON (h.LocationId = l.Id)
    									INNER JOIN 
    										Settings.Employee e ON h.EmployeeId = e.Id
    									INNER JOIN 
    										Settings.Customer cu ON h.CustomerId = cu.Id
    									INNER JOIN 
    										Settings.Commodity co ON h.CommodityId = co.Id
    									LEFT OUTER JOIN 
    										Settings.Region rr ON rr.Id = c.RegionId
    									WHERE 
    										c.TenantId=@tenantid
    										And h.Event = ''Edit''
    										AND (o.Status = ''Filled'' Or o.Status Is Null)
    										AND (@destination IS NULL OR h.LocationId IN (SELECT Id FROM @tDestination))
    										AND (@employeeD IS NULL OR h.EmployeeId IN (SELECT Id FROM @tEmployee))
    										AND (@commodityD IS NULL OR h.CommodityId IN (SELECT Id FROM @tCommodity))
    										AND (@contractType IS NULL OR h.ContractTypeId IN (SELECT Id FROM @tContractType))
    										AND (@cropYear IS NULL OR h.CropYear IN (SELECT Id FROM @tCropYear))
    										AND (@transaction IS NULL OR h.Event IN (SELECT Id FROM @tTransaction))
    										AND (
    											(@isGroupedPermissionEnabled = 0 AND (@userEmail IS NULL OR h.LocationId IN (SELECT Id FROM @tEmployeeLocations)))
    											OR 
    											(
    												@isGroupedPermissionEnabled = 1
    												AND EXISTS (
    													SELECT 1
    													FROM @tGroupedEmployeeLocations gle
    													WHERE 
    														(@userEmail IS NULL OR gle.DestinationId = h.DeliveryLocationId)
    														AND (@userEmail IS NULL OR gle.ContractLocationId = h.LocationId)
    														AND (@userEmail IS NULL OR gle.RegionId = c.RegionId)
    												)
    											)
    										)
    										AND (@region IS NULL OR c.RegionId IN (SELECT Id FROM @tRegion))
    										AND (h.CreatedOn >= @startdate)
    										AND (h.CreatedOn < @enddate)
    									ORDER BY 
    										l.Name, h.CreatedOn;
                					End

                            		DECLARE curse CURSOR FOR
    										SELECT
    										t.id,
    										t.CreatedOn AS Created,
    										e.FirstName + '' '' + e.LastName AS Employee,
    										CASE WHEN t.IsSell = 0 THEN ''P'' ELSE ''S'' END AS PS,
    										c.Name AS contractType,
    										t.TransactionEvent AS tEvent,
    										co.Name AS Product,
    										t.cropyear AS Crop,
    										CASE WHEN t.IsSell = 0 AND t.TransactionEvent <> ''Cancel'' THEN t.Quantity 
    										WHEN t.IsSell = 0 AND t.TransactionEvent = ''Cancel'' THEN t.Quantity * -1
    										WHEN t.IsSell = 1 AND t.TransactionEvent <> ''Cancel'' THEN t.Quantity * -1
    										WHEN t.IsSell = 1 AND t.TransactionEvent = ''Cancel'' THEN t.Quantity
    										END AS Quantity,
    										con.FuturesPrice AS Futures,
    										con.NetBasis AS Basis,
    										con.FreightPrice AS Freight,
    										con.Fees1 + con.Fees2 AS fees,
    										con.Price AS FlatPrice,
    										ISNULL(con.Number,'''') AS OrderNo,
    										cu.FirstName + '' '' + cu.LastName AS customer,
    										ISNULL(cu.Number,'''') as CustNo,
    										con.Comments AS Comment,
    										l.Name AS Destination,
    										con.TheirContract,
    										rr.Name as Region
    										from Transactions.[Transaction] t
    										inner join Catalogs.ContractType c ON c.Id = t.ContractTypeId
    										inner join Settings.Employee e ON e.Id = t.EmployeeId
    										inner join Settings.Customer cu ON t.CustomerId = cu.Id
    										inner join Settings.Commodity co ON t.CommodityId = co.Id
    										inner join Settings.[Location] l ON t.LocationId = l.Id
    										INNER JOIN Transactions.[Contract] con ON t.ContractId = con.Id
    										LEFT OUTER JOIN Settings.Region rr on rr.Id = t.RegionId
    										WHERE t.TenantId=@tenantid and t.ContractId IS NOT NULL
    										and (@destination	is null or t.LocationId	in (SELECT Id FROM @tDestination))
    										and (@employeeD		is null or t.EmployeeId			in (SELECT Id FROM @tEmployee))
    										and (@commodityD		is null or t.CommodityId		in (SELECT Id FROM @tCommodity))
    										and (@contractType	is null or t.ContractTypeId		in (SELECT Id FROM @tContractType))
    										AND (@cropYear      IS NULL OR t.CropYear           IN (SELECT id FROM @tCropYear))
    										AND (@transaction   IS NULL OR t.TransactionEvent   IN (SELECT id FROM @tTransaction))
    										AND (
    											(@isGroupedPermissionEnabled = 0 AND (@userEmail IS NULL OR t.LocationId IN (SELECT Id FROM @tEmployeeLocations)))
    											OR 
    										    	(
    														@isGroupedPermissionEnabled = 1
    														AND EXISTS (
    															SELECT 1
    															FROM @tGroupedEmployeeLocations gle
    															WHERE 
    																(@userEmail IS NULL OR gle.DestinationId = con.DeliveryLocationId)
    																AND (@userEmail IS NULL OR gle.ContractLocationId = con.LocationId)
    																AND (@userEmail IS NULL OR gle.RegionId = t.RegionId)
    														)
    													)
    												)
    												AND ( c.Name = ''Flat Price'' AND t.TransactionEvent IN (''Create'', ''Edit'',''Cancel'') 
    													 OR c.Name = ''HTA'' AND t.TransactionEvent IN (''Create'',''Edit'', ''Roll'', ''Cancel'',''Price'') 
    													 OR c.Name = ''Basis'' AND t.TransactionEvent IN (''Price'',''Cancel'',''Create'',''Edit'',''Roll''))
    												AND t.TransactionType <> ''Hedge''
    												AND (@region		IS NULL OR t.RegionId		IN (select Id from @tRegion))
    												AND (t.CreatedOn >= @startdate)
    												AND (t.CreatedOn < @enddate)
    											Union All 
    										SELECT * FROM @tResultsEdit
    										ORDER BY l.Name, t.CreatedOn;
        
                            		OPEN curse
                                    fetch next from curse into @id ,@Created,@EmployeeC ,@PS,@contractTypeC ,@tEvent ,@Product ,@Crop ,@Quantity ,@Futures ,@Basis ,@Freight,@fees ,@FlatPrice ,@OrderNo ,@customer,@CustNo,@Comment,@Destination,@theirContract, @RegionName
                              set @currentDestination = @Destination;

                              while @@FETCH_STATUS = 0
                              BEGIN
                            	
                            	 IF @currentDestination <> @Destination
                            	 BEGIN
                            	
                            		INSERT INTO @tResults(id,Created,Employee,
                            		    PS,contractType,tEvent,Product,Crop,Quantity,Futures,Basis,Freight,fees,FlatPrice,OrderNo,customer,CustNo,Comment, Destination,HrvstEdgeIsSubTotal, HrvstEdgeIsTotal,theirContract, Region)
                            		VALUES(NEWID(),null, '''', '''','''','''' ,'''','''' ,@qtyDestinationBushels ,null ,null ,null,null ,null,'''' ,'''','''','''', @currentDestination + '' Total'',1,0,@theirContract, @RegionName)
                            		 SET  @qtyDestinationBushels = 0
                            	 END

                            	 SET  @qtyDestinationBushels =  @qtyDestinationBushels + @Quantity
                            	 INSERT INTO @tResults(id,Created,Employee,
                            		    PS,contractType,tEvent,Product,Crop,Quantity,Futures,Basis,Freight,fees,FlatPrice,OrderNo,customer,CustNo,Comment, Destination,HrvstEdgeIsSubTotal, HrvstEdgeIsTotal,theirContract,Region)
                            		VALUES(@id,@Created, @EmployeeC, @PS,@contractTypeC,@tEvent,@Product ,@Crop ,@Quantity ,CASE WHEN @Futures = 0 THEN NULL ELSE @Futures end ,@Basis,CASE WHEN @Freight = 0 THEN null ELSE @Freight end,CASE WHEN @fees = 0 THEN NULL ELSE @fees end ,CASE WHEN @FlatPrice = 0 THEN NULL ELSE @FlatPrice end ,@OrderNo ,@customer,@CustNo,@Comment, @Destination,0,0,@theirContract,@RegionName)


                            	 SET @currentDestination = @Destination;

                            	 fetch next from curse into @id ,@Created,@EmployeeC ,@PS,@contractTypeC ,@tEvent ,@Product ,@Crop ,@Quantity ,@Futures ,@Basis ,@Freight,@fees ,@FlatPrice ,@OrderNo ,@customer,@CustNo,@Comment,@Destination,@theirContract, @RegionName
                              END

                               IF @@CURSOR_ROWS > 0
                               BEGIN

                                   INSERT INTO @tResults(id,Created,Employee,PS,contractType,tEvent,Product,Crop,Quantity,Futures,Basis,Freight,fees,FlatPrice,OrderNo,customer,CustNo,Comment, Destination,HrvstEdgeIsSubTotal, HrvstEdgeIsTotal,theirContract, Region)
                            	   VALUES(NEWID(),null, '''', '''','''','''' ,'''','''' ,@qtyDestinationBushels ,null,null ,null,null ,null,'''' ,'''','''','''', @currentDestination + '' Total'',1,0,@theirContract,@RegionName)
                            	END
                            	CLOSE curse
                                 deallocate curse


                               SELECT id ,Created,Employee ,PS,contractType ,tEvent ,Product ,Crop ,Quantity ,Futures ,Basis ,Freight,fees ,FlatPrice ,OrderNo ,customer,CustNo,Comment,Destination, HrvstEdgeIsSubTotal, HrvstEdgeIsTotal,theirContract, Region FROM @tResults
            END'