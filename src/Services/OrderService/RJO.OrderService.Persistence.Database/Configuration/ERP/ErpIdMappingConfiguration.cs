using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RJO.OrderService.Domain.ERP;

namespace RJO.OrderService.Persistence.Database.Configuration.ERP;

public class ErpIdMappingConfiguration : IEntityTypeConfiguration<ErpIdMapping>
{
	public void Configure(EntityTypeBuilder<ErpIdMapping> builder)
	{
		builder.ConfigureBase();
		builder.ConfigureAuditable();
		builder.ToTable("ErpIdMapping", SchemaName.Erp);

		builder.Property(x => x.ErpName).IsRequired().HasMaxLength(80);
		builder.Property(x => x.MappingType).IsRequired().HasMaxLength(100);
		builder.Property(x => x.SourceId).IsRequired();
		builder.Property(x => x.DestinationValue).IsRequired().HasMaxLength(100);

		builder.HasIndex(e => new { e.ErpName, e.MappingType, e.SourceId }).IsUnique();
	}
}
