using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using RJO.OrderService.Domain.ERP;

namespace RJO.OrderService.Persistence.Database.Configuration.ERP;

public class AgvantageContractConfiguration : IEntityTypeConfiguration<AgvantageContract>
{
	public void Configure(EntityTypeBuilder<AgvantageContract> builder)
	{
		builder.ToTable("AgvantageContract", SchemaName.Transaction);
		builder.<PERSON><PERSON>ey(e => e.Id);

		builder.Property(e => e.ContractId).IsRequired();
		builder.Property(e => e.AgvantageId).IsRequired().HasMaxLength(80);

		builder.HasIndex(x => x.ContractId).IsUnique();
	}
}
