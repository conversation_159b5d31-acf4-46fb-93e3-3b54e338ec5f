using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RJO.OrderService.Domain.Views;

namespace RJO.OrderService.Persistence.Database.Configuration.Views;

public class LocalMonitoredSymbolsConfiguration : IEntityTypeConfiguration<LocalMonitoredSymbols>
{
	public void Configure(EntityTypeBuilder<LocalMonitoredSymbols> builder) => builder.ToView(nameof(LocalMonitoredSymbols), SchemaName.Cqg);
}
