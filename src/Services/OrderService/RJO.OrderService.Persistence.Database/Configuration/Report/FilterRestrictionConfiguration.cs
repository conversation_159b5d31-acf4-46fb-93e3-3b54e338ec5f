using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RJO.OrderService.Domain.Reports;

namespace RJO.OrderService.Persistence.Database.Configuration.Report;

public class FilterRestrictionConfiguration : IEntityTypeConfiguration<ReportFilterRestriction>
{
	public void Configure(EntityTypeBuilder<ReportFilterRestriction> builder)
	{
		builder.ToTable("FilterRestriction", SchemaName.Report);
		builder.HasKey(o => o.Id);
		builder.Property(x => x.FilterId).IsRequired();
		builder.Property(x => x.IsRequired).IsRequired();
		builder.Property(x => x.MinValue).HasMaxLength(150);
		builder.Property(x => x.MaxValue).HasMaxLength(150);
	}
}
