using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RJO.OrderService.Domain.Settings;

namespace RJO.OrderService.Persistence.Database.Configuration.Setting;

public class UserDefaultValuesConfiguration : IEntityTypeConfiguration<UserDefaultValues>
{
	public void Configure(EntityTypeBuilder<UserDefaultValues> builder)
	{
		builder.ConfigureBase();
		builder.ConfigureAuditable();
		builder.ToTable("UserDefaultValues", SchemaName.Setting);
		builder.Property(x => x.Email).IsRequired().HasMaxLength(50);
		builder.Property(x => x.CommodityId);
		builder.Property(x => x.LocationId);
		builder.Property(x => x.DeliveryLocationId);
		builder.Property(x => x.ContractTypeId);
		builder.Property(x => x.CropYear);
		builder.Property(x => x.FuturesMonth).HasMaxLength(24);
		builder.Property(x => x.DeliveryEndDate);
		builder.Property(x => x.DeliveryStartDate);
		builder.Property(x => x.TransactionTypeId);
		builder.Property(x => x.IsDeliveryDateCustom);
	}
}
