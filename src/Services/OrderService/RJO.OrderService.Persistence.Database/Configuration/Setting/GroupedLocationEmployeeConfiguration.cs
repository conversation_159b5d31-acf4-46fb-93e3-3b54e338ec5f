using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RJO.MultiTenancyServer.Core.Options;
using RJO.OrderService.Domain.Settings;

namespace RJO.OrderService.Persistence.Database.Configuration.Setting;

class GroupedLocationEmployeeConfiguration : IEntityTypeConfiguration<GroupedLocationEmployee>
{
	public void Configure(EntityTypeBuilder<GroupedLocationEmployee> builder)
	{
		builder.ConfigureBase();
		builder.ConfigureAuditable();
		builder.ToTable(nameof(GroupedLocationEmployee), SchemaName.Setting);

		builder.HasOne(x => x.GroupedLocation)
			.WithMany()
			.HasForeignKey(x => x.GroupedLocationId)
			.OnDelete(DeleteBehavior.Cascade);

		builder.HasOne(x => x.Employee)
			.WithMany()
			.HasForeignKey(x => x.EmployeeId)
			.OnDelete(DeleteBehavior.Restrict);

		builder.HasIndex(nameof(GroupedLocationEmployee.GroupedLocationId), nameof(GroupedLocationEmployee.EmployeeId), new TenantReferenceOptions().ReferenceName)
			.IncludeProperties(x => new { x.CanBuy, x.CanSell })
			.IsUnique();
		
		builder.HasIndex(new TenantReferenceOptions().ReferenceName, nameof(GroupedLocationEmployee.CanBuy), nameof(GroupedLocationEmployee.CanSell))
			.IncludeProperties(x => new { x.GroupedLocationId, x.EmployeeId });
	}
}
