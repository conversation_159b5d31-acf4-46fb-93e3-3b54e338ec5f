using Magneto;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using RJO.BuildingBlocks.Common;
using RJO.OrderService.Domain.MultiTenancyServer;
using RJO.OrderService.Persistence.Database;
using System.Text.Json;
using WebMotions.Fake.Authentication.JwtBearer;

namespace RJO.OrderService.WebApi.Testing;

class FakeAuthenticationMiddleware
{
	readonly RequestDelegate _next;

	public FakeAuthenticationMiddleware(RequestDelegate next) => _next = next;

	public async Task InvokeAsync(HttpContext context, IConfiguration configuration, IMagneto magneto)
	{
		if (configuration.UseTestEnvironment())
		{
			var fakeUsers = await magneto.QueryAsync(new FakeUsersByTenant(TestEnvironment.FakeTenant), CacheOption.Default);
			var fakeUser = fakeUsers.Get(TestEnvironment.FakeUser);
			var token = JsonSerializer.Serialize(fakeUser.ToToken());

			context.Request.Headers.Authorization = $"{FakeJwtBearerDefaults.AuthenticationScheme} {token}";
		}

		await _next(context);
	}

	class FakeUsersByTenant : AsyncTransformedCachedQuery<AppDbContext, MemoryCacheEntryOptions, IReadOnlyCollection<ApplicationTenant>, FakeUsers>
	{
		readonly string _canonicalName;

		public FakeUsersByTenant(string canonicalName) => _canonicalName = canonicalName;

		protected override void CacheKey(ICache cache) => cache.VaryByNothing();

		protected override MemoryCacheEntryOptions CacheEntryOptions(AppDbContext context) => new MemoryCacheEntryOptions().SetAbsoluteExpiration(5.Minutes());

		protected override async Task<IReadOnlyCollection<ApplicationTenant>> Query(AppDbContext context, CancellationToken cancellationToken) =>
			await context.Tenants.ToListAsync(cancellationToken);

		protected override Task<FakeUsers> TransformCachedResult(IReadOnlyCollection<ApplicationTenant> cachedResult, CancellationToken cancellationToken)
		{
			var applicationTenant = cachedResult.First(x => x.CanonicalName == _canonicalName);
			var fakeUsers = new FakeUsers(applicationTenant);
			return Task.FromResult(fakeUsers);
		}
	}
}
