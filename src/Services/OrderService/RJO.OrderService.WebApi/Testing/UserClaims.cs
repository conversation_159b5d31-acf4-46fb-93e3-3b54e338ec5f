using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.WebCommon.Authorization;
using System.Security.Claims;

namespace RJO.OrderService.WebApi.Testing;

static class UserClaims
{
	// ReSharper disable InconsistentNaming

	public static Claim MobileAppEnable { get; } = new(CustomClaimTypes.Grant, OperationsConstants.MobileAppEnable);
	public static Claim UseSalesTrading { get; } = new(CustomClaimTypes.Grant, OperationsConstants.UseSalesTrading);
	public static Claim ContractActivateDNH { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractActivateDNH);
	public static Claim ContractBasisCreate { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractBasisCreate);
	public static Claim ContractBasisEditNonQuantity { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractBasisEditNonQuantity);
	public static Claim ContractBasisEditQuantity { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractBasisEditQuantity);
	public static Claim ContractFlatPriceCreate { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractFlatPriceCreate);
	public static Claim ContractFlatPriceEditNonQuantity { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractFlatPriceEditNonQuantity);
	public static Claim ContractFlatPriceEditQuantity { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractFlatPriceEditQuantity);
	public static Claim ContractHTACreate { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractHTACreate);
	public static Claim ContractHTAEditNonQuantity { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractHTAEditNonQuantity);
	public static Claim ContractHTAEditQuantity { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractHTAEditQuantity);
	public static Claim ContractNTCCreate { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractNTCCreate);
	public static Claim ContractNTCEditNonQuantity { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractNTCEditNonQuantity);
	public static Claim ContractNTCEditQuantity { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractNTCEditQuantity);
	public static Claim ContractNTCConvertQuantity { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractNTCConvertQuantity);
	public static Claim ResendToErp { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ResendToErp);
	public static Claim ContractCancel { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ContractCancel);
	public static Claim AllowNTCView { get; } = new(CustomClaimTypes.Grant, OperationsConstants.AllowNTCView);
	public static Claim OfferBasisCreate { get; } = new(CustomClaimTypes.Grant, OperationsConstants.OfferBasisCreate);
	public static Claim OfferBasisEdit { get; } = new(CustomClaimTypes.Grant, OperationsConstants.OfferBasisEdit);
	public static Claim OfferFlatPriceCreate { get; } = new(CustomClaimTypes.Grant, OperationsConstants.OfferFlatPriceCreate);
	public static Claim OfferFlatPriceEdit { get; } = new(CustomClaimTypes.Grant, OperationsConstants.OfferFlatPriceEdit);
	public static Claim OfferHTACreate { get; } = new(CustomClaimTypes.Grant, OperationsConstants.OfferHTACreate);
	public static Claim OfferHTAEdit { get; } = new(CustomClaimTypes.Grant, OperationsConstants.OfferHTAEdit);
	public static Claim LiveLedgerView { get; } = new(CustomClaimTypes.Grant, OperationsConstants.LiveLedgerView);
	public static Claim ReviewAndReleaseView { get; } = new(CustomClaimTypes.Grant, OperationsConstants.ReviewAndReleaseView);
	public static Claim SettingsAdmin { get; } = new(CustomClaimTypes.Grant, OperationsConstants.SettingsAdmin);
	public static Claim OMSOnly { get; } = new(CustomClaimTypes.Grant, OperationsConstants.OMSOnly);
	public static Claim UploadBidsheet { get; } = new(CustomClaimTypes.Grant, OperationsConstants.UploadBidsheet);
	public static Claim FutureFirst { get; } = new(CustomClaimTypes.Grant, OperationsConstants.FutureFirst);
	public static Claim IsMobile { get; } = new(CustomClaimTypes.IsMobile, "true");
	public static Claim Scope { get; } = new("scope", "api.agris");

	public static List<Claim> AllGrants => new()
	{
		MobileAppEnable,
		UseSalesTrading,
		ContractActivateDNH,
		ContractBasisCreate,
		ContractBasisEditNonQuantity,
		ContractBasisEditQuantity,
		ContractFlatPriceCreate,
		ContractFlatPriceEditNonQuantity,
		ContractFlatPriceEditQuantity,
		ContractHTACreate,
		ContractHTAEditNonQuantity,
		ContractHTAEditQuantity,
		ContractNTCCreate,
		ContractNTCEditNonQuantity,
		ContractNTCEditQuantity,
		ContractNTCConvertQuantity,
		ResendToErp,
		ContractCancel,
		AllowNTCView,
		OfferBasisCreate,
		OfferBasisEdit,
		OfferFlatPriceCreate,
		OfferFlatPriceEdit,
		OfferHTACreate,
		OfferHTAEdit,
		LiveLedgerView,
		ReviewAndReleaseView,
		SettingsAdmin,
		FutureFirst,
		UploadBidsheet
	};

	public static List<Claim> AllGrantsPlusOmsOnly => AllGrants.Concat([OMSOnly]).ToList();
	public static List<Claim> AllGrantsPlusIsMobile => AllGrants.Concat([IsMobile]).ToList();

	public static List<Claim> AllGrantsExceptSettingsAdmin => AllGrants.Except([SettingsAdmin]).ToList();

	public static List<Claim> AllGrantsExceptUploadBidsheet => AllGrants.Except([UploadBidsheet]).ToList();

	public static List<Claim> AllGrantsExceptFutureFirst => AllGrants.Except([FutureFirst]).ToList();

	public static List<Claim> AllGrantsExceptSettingsAdminPlusOmsOnly => AllGrantsExceptSettingsAdmin.Concat([OMSOnly]).ToList();
}
