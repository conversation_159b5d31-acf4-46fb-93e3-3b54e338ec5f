using Hangfire;
using Hangfire.Pro.Redis;
using Hangfire.Tags;
using Hangfire.Tags.Pro.Redis;
using Hangfire.Throttling;
using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.OpenApi.Models;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Common.Hangfire;
using RJO.BuildingBlocks.EventBus;
using RJO.BuildingBlocks.EventBus.Abstractions;
using RJO.OrderService.Common;
using RJO.OrderService.Domain.Settings;
using RJO.OrderService.Persistence;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Persistence.Repositories.Logs;
using RJO.OrderService.Persistence.Repositories.Metadata;
using RJO.OrderService.Persistence.Repositories.Report;
using RJO.OrderService.Persistence.Repositories.Settings;
using RJO.OrderService.Services.ErpIntegration.AgrisServices.Helpers;
using RJO.OrderService.Services.ErpIntegration.AgTraxServices.Helpers;
using RJO.OrderService.Services.ErpIntegration.OaklandServices.Helpers;
using RJO.OrderService.Services.Helper;
using RJO.OrderService.Services.Jobs;
using RJO.OrderService.Services.Jobs.DeleteJobs;
using RJO.OrderService.Services.Jobs.Handlers.EventHandlers;
using RJO.OrderService.Services.Jobs.Jobs;
using RJO.OrderService.Services.Jobs.RecurrentJobs;
using RJO.OrderService.Services.Services;
using RJO.OrderService.Services.Services.OfferProcess;
using RJO.OrderService.Services.Services.OrderProcess;
using RJO.OrderService.WebApi.Authorization;

namespace RJO.OrderService.WebApi.Core.Configuration.Extensions;

public static class ConfigurationExtensions
{
	public static IServiceCollection ConfigureOptions(this IServiceCollection services, IConfiguration configuration)
	{
		services.Configure<Settings>(configuration.GetSection("Settings"));
		return services;
	}

	public static void ConfigureHangfireServices(this IServiceCollection services, IConfiguration configuration)
	{
		var redisConnectionString = configuration.RedisConnectionString();
		var dataShardingPrefix = configuration.DataShardingPrefix();
		var redisStorageOptions = new RedisStorageOptions { Prefix = $"{{{dataShardingPrefix}hrvyst}}:" };

		services.AddSingleton(redisStorageOptions);
		services.AddHostedService<RecurringJobsInitializer>();

		services.AddHangfireApplicationInsightsTelemetry();
		services.AddHangfireElasticApmTelemetry();
		services.AddHangfireMetricsTelemetry();

		services.AddHangfire((serviceProvider, globalConfiguration) => globalConfiguration
			.SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
			.UseRecommendedSerializerSettings()
			.UseRedisStorage(redisConnectionString, redisStorageOptions)
			.UseBatches()
			.UseThrottling(ThrottlingAction.RetryJob, 1.Seconds())
			.UseTagsWithRedis(new() { TagsListStyle = TagsListStyle.Dropdown }, redisStorageOptions)
			.UseHangfireApplicationInsightsTelemetry(serviceProvider)
			.UseHangfireElasticApmTelemetry(serviceProvider)
			.UseHangfireMetricsTelemetry(serviceProvider));	

		services.AddHangfireServer((provider, options) =>
		{
			options.Activator = new HangfireJobActivator(provider);
			options.WorkerCount = Environment.ProcessorCount * 10;
			options.HeartbeatInterval = 10.Seconds();
			options.SchedulePollingInterval = 1.Seconds();
			options.Queues = ["critical", "default", "notifications"];
			options.ServerName = Environment.MachineName;
		});
	}

	public static IServiceCollection ConfigureSwagger(this IServiceCollection services, string version, string title, string description, string xmlPath)
	{
		services.AddSwaggerGen(c =>
		{
			c.CustomSchemaIds(x => x.FullName);
			c.SwaggerDoc("v1", new()
			{
				Version = version,
				Title = title,
				Description = description,
				Contact = new()
				{
					Name = "RJO",
					Email = "<EMAIL>",
					Url = new("https://www.rjobrien.com/")
				}
			});
			c.AddSecurityDefinition("Bearer", new()
			{
				Description = @"JWT Authorization header using the Bearer scheme. \r\n\r\n
                      Enter 'Bearer' [space] and then your token in the text input below.
                      \r\n\r\nExample: 'Bearer 12345abcdef'",
				Name = "Authorization",
				In = ParameterLocation.Header,
				Type = SecuritySchemeType.ApiKey,
				BearerFormat = "JWT",
				Scheme = "Bearer"
			});
			c.AddSecurityRequirement(new()
			{
				{
					new()
					{
						Reference = new()
						{
							Type = ReferenceType.SecurityScheme,
							Id = "Bearer"
						},
						Scheme = "oauth2",
						Name = "Bearer",
						In = ParameterLocation.Header
					},
					new List<string>()
				}
			});
			c.IncludeXmlComments(xmlPath);
			
			c.TagActionsBy(api =>
			{
				if (api.GroupName != null)
				{
					return new[] { api.GroupName };
				}

				if (api.ActionDescriptor is ControllerActionDescriptor controllerActionDescriptor)
				{
					return new[] { controllerActionDescriptor.ControllerName };
				}

				throw new InvalidOperationException("Unable to determine tag for endpoint.");
			});
			c.DocInclusionPredicate((_, _) => true);
		});

		return services;
	}

	public static IApplicationBuilder ConfigureJobs(this IApplicationBuilder app)
	{
		IThrottlingManager manager = new ThrottlingManager();
		manager.AddOrUpdateSemaphore("fill-localmonitor-offer", new(10, "fill-localmonitor-offer"));
		manager.AddOrUpdateDynamicWindow("fill-localmonitor-offer", new(
			10,
			TimeSpan.FromSeconds(1),
			1));
		return app;
	}

	public static IServiceCollection AddEventBus(this IServiceCollection services, IConfiguration configuration)
	{
		services.AddSingleton<IEventBus, EventBusServiceBus>(provider =>
		{
			var subscriptionClientName = "realtime-marketdata";
			var serviceScopeFactory = provider.GetRequiredService<IServiceScopeFactory>();
			var logger = provider.GetRequiredService<ILogger<EventBusServiceBus>>();
			var eventBusSubscriptionManager = provider.GetRequiredService<InMemoryEventBusSubscriptionsManager>();
			var telemetryClient = provider.GetRequiredService<TelemetryClient>();
			return new(logger, eventBusSubscriptionManager, subscriptionClientName, serviceScopeFactory, telemetryClient, configuration);
		});
		services.AddHostedService<EventBusSubscription>();

		services.AddSingleton<InMemoryEventBusSubscriptionsManager>();

		// Event Handlers Subscriptions:
		services.AddScoped<HighAndLowsEventHandler>();
		//Order Statuses from CQG
		//Filled
		services.AddScoped<FilledOrderEventHandler>();
		//Working
		services.AddScoped<WorkingOrderEventHandler>();
		//Rejected
		services.AddScoped<RejectedOrderEventHandler>();
		//Expired
		services.AddScoped<ExpiredOrderEventHandler>();
		//Cancel Rejected
		services.AddScoped<CancelRejectedEventHandler>();
		//Edit Rejected
		services.AddScoped<EditRejectedEventHandler>();
		//Cancel
		services.AddScoped<CancelOrderEventHandler>();

		return services;
	}

	public static IServiceCollection AddDomainServices(this IServiceCollection services)
	{
		services.AddScoped<OfferWorkflowContext>();
		services.AddScoped<ContractWorkflowContext>();
		services.AddScoped<MarketTransactionDomainService>();
		services.AddScoped<BidSheetHelper>();
		services.AddScoped<IdentityDomainService>();
		services.AddScoped<HedgeMappingHelper>();
		services.AddScoped<ContractHelper>();
		services.AddScoped<ContractOfferValidationHelper>();
		services.AddScoped<MetadataDomainService>();
		services.AddScoped<HedgeAccountHelper>();
		services.AddScoped<FutureMonthsHelper>();
		services.AddScoped<FuturePriceHelper>();
		services.AddScoped<FrontMonthPriceHelper>();
		services.AddScoped<Tag50GlobalAccountHelper>();
		services.AddScoped<MarketStatusService>();
		services.AddScoped<Tag50ResolveService>();
		services.AddScoped<ErpIntegrationAgent>();
		services.AddScoped<AgtraxContractHelper>();
		services.AddScoped<AgrisContractHelper>();
		services.AddScoped<OaklandContractHelper>();
		services.AddScoped<PreHedgeService>();
		services.AddScoped<RegionHelper>();
		services.AddScoped<HedgeMapService>();
		services.AddScoped<EmployeeLocationService>();
		return services;
	}

	public static IServiceCollection AddRepositories(this IServiceCollection services)
	{
		services.AddScoped<UnitOfWork>();
		services.AddScoped<ContractTypeRepository>();
		services.AddScoped<ExtendedContractTypeRepository>();
		services.AddScoped<TransactionTypeRepository>();
		services.AddScoped<LocationRepository>();
		services.AddScoped<ContractRepository>();
		services.AddScoped<BidsheetRepository>();
		services.AddScoped<CommodityRepository>();
		services.AddScoped<MarketTransactionRepository>();
		services.AddScoped<SettingRepository>();
		services.AddScoped<BucketBalanceRepository>();
		services.AddScoped<CustomerRepository>();
		services.AddScoped<DoNotHedgeLogRepository>();
		services.AddScoped<LiveLedgerRepository>();
		services.AddScoped<BalanceSnapshotRepository>();
		services.AddScoped<EmployeeRepository>();
		services.AddScoped<ErpLogRepository>();
		services.AddScoped<CashTransactionRepository>();
		services.AddScoped<OfferRepository>();
		services.AddScoped<LogWorkflowRepository>();
		services.AddScoped<LogOfferWorkflowRepository>();
		services.AddScoped<LogTimerRepository>();
		services.AddScoped<ContractMigrationRepository>();
		services.AddScoped<TimerConfigurationRepository>();
		services.AddScoped<ReportMetadataRepository>();
		services.AddScoped<ReportBodyRepository>();
		services.AddScoped<ReportBodyColumnRepository>();
		services.AddScoped<ReportFilterRepository>();
		services.AddScoped<ReportFilterRestrictionRepository>();
		services.AddScoped<ReportSectionRepository>();
		services.AddScoped<ReportSectionColumnRepository>();
		services.AddScoped<ReportSectionColumnChildrenRepository>();
		services.AddScoped<ProductRepository>();
		services.AddScoped<HedgeAccountRepository>();
		services.AddScoped<BrokerMappingRepository>();
		services.AddScoped<HedgeMappingRepository>();
		services.AddScoped<BucketBalanceContractRepository>();
		services.AddScoped<BidsheetFileRepository>();
		services.AddScoped<EmployeeLocationRepository>();
		services.AddScoped<Persistence.Repositories.Historical.BidsheetRepository>();
		services.AddScoped<Persistence.Repositories.Historical.OfferRepository>();
		services.AddScoped<Persistence.Repositories.Historical.ContractRepository>();
		services.AddScoped<Persistence.Repositories.Historical.MarketTransactionRepository>();
		services.AddScoped<Persistence.Repositories.Staging.BidsheetRepository>();
		services.AddScoped<ContractOfferRoleRepository>();
		services.AddScoped<LocationRoleRepository>();
		services.AddScoped<OfferMonitoringRepository>();
		services.AddScoped<FuturesMonthRepository>();
		services.AddScoped<FuturesPriceSnapshotRepository>();
		services.AddScoped<HighAndLowSnapshotRepository>();
		services.AddScoped<ServiceFeeRepository>();
		services.AddScoped<ServiceFeeMonthRepository>();
		services.AddScoped<TenantSettingRepository>();
		services.AddScoped<QuoteCounterRepository>();
		services.AddScoped<RoundingRuleRepository>();
		services.AddScoped<RoundingTypeRepository>();
		services.AddScoped<AgrisContractTypeRepository>();
		services.AddScoped<AgtraxPositionTypeRepository>();
		services.AddScoped<OaklandContractTypeRepository>();
		services.AddScoped<ErpLogRepository>();
		services.AddScoped<UserDefaultValuesRespository>();
		services.AddScoped<ServiceFeeTypeRepository>();
		services.AddScoped<BasisTransactionRepository>();
		services.AddScoped<OrderMetadataItemRepository>();
		services.AddScoped<ContractMetadataRepository>();
		services.AddScoped<OfferMetadataRepository>();
		services.AddScoped<OrderMetadataConfigurationRepository>();
		services.AddScoped<AgrisSettingRepository>();
		services.AddScoped<AgtraxSettingRepository>();
		services.AddScoped<ServiceBusEventRepository>();
		services.AddScoped<MarketDataSubscriptionRepository>();
		return services;
	}

	public static IServiceCollection AddJobs(this IServiceCollection services)
	{
		// Jobs.Jobs
		services.AddScoped<CancelOrderJob>();
		services.AddScoped<CancelRejectedJob>();
		services.AddScoped<EditRejectedJob>();
		services.AddScoped<ExpiredOrderJob>();
		services.AddScoped<FilledOrderJob>();
		services.AddScoped<HighAndLowsJob>();
		services.AddScoped<RejectedOrderJob>();
		services.AddScoped<SpotTradeJob>();
		services.AddScoped<WorkingOrderJob>();

		// Jobs.Recurrent
		services.AddScoped<CloseExpiredOffersJob>();
		services.AddScoped<CloseLiveLedgerJob>();
		services.AddScoped<OaklandCustomersJob>();
		services.AddScoped<AgvantageCustomersJob>();
		services.AddScoped<AgvantageLocationsJob>();
		services.AddScoped<RollSpotBidsAndOffersJob>();

		// Jobs
		services.AddScoped<AccumulationRequestJob>();
		services.AddScoped<CancelOrderRequestJob>();
		services.AddScoped<CreateLimitOrderRequestJob>();
		services.AddScoped<CreateMarketOrderRequestJob>();
		services.AddScoped<EditOrderRequestJob>();
		services.AddScoped<ErpStatusChangeJob>();
		services.AddScoped<FillLocalMonitorOfferJob>();
		services.AddScoped<SendErpHedgesJob>();
		services.AddScoped<ChangeJobScheduleJob>();
		services.AddSingleton<IHrvystClock, SystemClock>();
		services.AddScoped<DeleteScheduledJob>();

		return services;
	}

	public static IServiceCollection AddAuthorizationHandlers(this IServiceCollection services)
	{
		services.AddSingleton<IAuthorizationHandler, ContractAuthorizationHandler>();
		services.AddSingleton<IAuthorizationHandler, OfferAuthorizationHandler>();
		return services;
	}

	public static IServiceCollection AddServicesHttpClients(this IServiceCollection services)
	{
		services.AddHttpClient<MarketStatusService>();
		return services;
	}
}
