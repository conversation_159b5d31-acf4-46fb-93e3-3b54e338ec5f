using MediatR;
using Microsoft.AspNetCore.Mvc;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.WebCommon;
using RJO.BuildingBlocks.WebCommon.Authorization;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.OrderService.Services.DTO.Import.Contract;
using RJO.OrderService.Services.Handlers.Import;
using RJO.OrderService.Services.Handlers.Logs;

namespace RJO.OrderService.WebApi.Controllers;

[Produces("application/json")]
[Route("api/import")]
[ApiController]
public class ImportController(IMediator mediator) : BaseController
{
	/// <summary>
	///     Action to retrieve all logs from contract importation
	/// </summary>
	/// <returns>Returns a list of contract logs</returns>
	/// <response code="200">Returned if the list was retrieved</response>
	/// <response code="400">Returned if the elements could not be retrieved</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpGet("contracts")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<IList<ImportResultItemDto>>>> GetContractReports() => Result(await mediator.Send(new GetAllContractMigrationQuery()));

	/// <summary>
	///     Action to create a template to be filled by the user and then, be loaded in the impor contract process
	/// </summary>
	/// <returns>Returns an excel file with the template to fill with contract information</returns>
	/// <response code="200">Returned if the file was created and returned</response>
	/// <response code="400">Returned if there was an error</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpGet("contracttemplate")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<FileResult> ContractTemplate()
	{
		var data = await mediator.Send(new CreateImportTemplateCommand());
		return ResultExcelFile(data.Data, data.TenantName);
	}

	/// <summary>
	///     Action to import contracts to the system.
	/// </summary>
	/// <returns>A list of elements with the result of the process</returns>
	/// <response code="200">Returned if the file was accepted and correctly validated.</response>
	/// <response code="400">Returned if there was an error with the errors list</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPost("importcontracts")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<ImportResultDto>>> ImportContracts([FromForm] ImportDto importInformation)
	{
		var hasBushelsOnlySetup = User.HasClaim(c => c is { Type: CustomClaimTypes.Grant, Value: OperationsConstants.AllowNTCView });
		return Result(await mediator.Send(new ProcessImportTemplateCommand { ImportInformation = importInformation, HasBushelsOnlySetup = hasBushelsOnlySetup }));
	}
}
