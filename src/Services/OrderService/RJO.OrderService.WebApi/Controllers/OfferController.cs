using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.CustomExceptions.SecurityExceptions;
using RJO.BuildingBlocks.WebCommon;
using RJO.BuildingBlocks.WebCommon.Authorization;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.OrderService.Services.DTO.Catalogs.FutureMonths;
using RJO.OrderService.Services.DTO.Common;
using RJO.OrderService.Services.DTO.Offer;
using RJO.OrderService.Services.Handlers;
using RJO.OrderService.Services.Handlers.Offers;

namespace RJO.OrderService.WebApi.Controllers;

[Produces("application/json")]
[Route("api/Offers")]
[ApiController]
public class OfferController : BaseController
{
	readonly IMediator _mediator;
	readonly IAuthorizationService _authorizationService;

	public OfferController(IMediator mediator, IAuthorizationService authorizationService)
	{
		_mediator = mediator;
		_authorizationService = authorizationService;
	}

	// POST api/<OfferController>
	/// <summary>
	///     Action to create a new Offer in the database.
	/// </summary>
	/// <remarks>
	/// Sample request:
	///     PUT /OfferDto
	///     {
	///         "transactionTypeId": "0070AE1A-5FA4-4AB2-8C45-41873B092B3B",
	///         "contractTypeId": "86007747-FA94-4E15-BDC1-0EE91EE2C9A2",
	///         "isSell": true,
	///         "commodityId": "4C9AE0A3-FFF8-4119-A510-5E4625F8E30B",
	///         "locationId": "916004A7-A2FD-4E3E-B76B-0A3977CC0D16",
	///         "deliveryLocationId": "B58AFFA3-5951-4A53-AD95-1C4F84809DEC",
	///         "deliveryStartDate": "2020-06-01T00:00:00",
	///         "deliveryEndDate": "2020-06-30T00:00:00",
	///         "cropTypeId": "F760BC61-4FC2-49F1-808F-DF402137CEEB",
	///         "customerId": "2C0B0ACD-D072-B426-E360-013914D4A578",
	///         "buyerId": "5A34D083-B7D6-AD21-6532-2DF6BCE05248",
	///         "futuresMonth": "N20",
	///         "futuresPrice": 8.5,
	///         "postedBasis": -0.42,
	///         "pushBasis": -0.21,
	///         "netBasis": -0.63,
	///         "freightPrice": -0.01,
	///         "fees1": 3.5,
	///         "fees2": 0,
	///         "price": 3.6,
	///         "quantity": 560,
	///         "comments": "xxx",
	///         "Gtc": 0,
	///         "Expiration": "2020-06-30T00:00:00",
	///     }
	/// </remarks>
	/// <param name="offer">Model to create a new Offer</param>
	/// <returns>Returns the number of created Offer</returns>
	/// <response code="201">Returned if the Offer was created</response>
	/// <response code="400">Returned if the model couldn't be parsed or saved</response>
	/// <response code="422">Returned when the validation failed</response>
	[ProducesResponseType(StatusCodes.Status201Created)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[ProducesResponseType(StatusCodes.Status422UnprocessableEntity)]
	[HttpPost]
	public async Task<ActionResult<Result<string>>> Create([FromBody] OfferCreateDto offer)
	{
		var isAuthorized = await _authorizationService.AuthorizeAsync(User, offer, Operations.Create);
		if (!isAuthorized.Succeeded)
		{
			throw new SecurityException();
		}

		return Result(await _mediator.Send(new CreateOfferCommand(offer, User.IsMobile(), User.GetFlagContext())));
	}

	/// <summary>
	///     Action to retrieve Offers filtered by certain criteria.
	/// </summary>
	/// <returns>Returns a list of all paid Offers or an empty list</returns>
	/// <response code="200">Returned if the list of Offers was retrieved</response>
	/// <response code="400">Returned if the Offers could not be retrieved</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPost("filter")]
	public async Task<ActionResult<Result<ListDto<OfferItemDto>>>> Filters([FromBody] OfferFilterDto filter) => Result(await _mediator.Send(new GetAllOffersQuery( filter, User.GetFlagContext() )));

	// GET api/<OfferController>/5
	/// <summary>
	///     Action to retrieve a contract in detail.
	/// </summary>
	/// <param name="id"></param>
	/// <returns>Returns an contract</returns>
	/// <response code="200">Returned if the contract exists</response>
	/// <response code="400">Returned if the Contracts does not exists</response>
	[HttpGet("{id}")]
	public async Task<ActionResult<Result<OfferInfoDto>>> Get(Guid id) => Result(await _mediator.Send(new GetOfferByIdQuery(id, User.GetFlagContext())));

	// GET api/<OfferController>/5
	/// <summary>
	///     Action to retrieve a contract in detail with related entities.
	/// </summary>
	/// <param name="id"></param>
	/// <returns>Returns an contract</returns>
	/// <response code="200">Returned if the Contract exists</response>
	/// <response code="400">Returned if the Contracts does not exists</response>
	[HttpGet("detail/{id}")]
	public async Task<ActionResult<Result<OfferDetailDto>>> GetOfferDetail(Guid id) => Result(await _mediator.Send(new GetOfferDetailQuery { Id = id }));

	/// <summary>
	///     Action to update an Offer.
	/// </summary>
	/// <remarks>
	/// Sample request:
	/// 
	///     PUT edit/{id}
	///     {
	///       "freightPrice": 0,
	///       "fees1": 0,
	///       "fees2": 0,
	///       "gtc": true,
	///       "comments": "Example Edition of HTA",
	///       "customerId": "73d6d651-3d4d-e8b6-2ae0-8a4e10bce088",
	///       "employeeId": "7b492891-ccfd-ab5c-38fe-e0333fda6af4",
	///       "futuresMonth": "N21",
	///       "futuresPrice": 9,
	///       "cashSettlement": false
	///     }
	/// 
	/// </remarks>
	/// <param name="id">The guid of the offer to be updated</param>
	/// <param name="offer">Information of the offer to be updated</param>
	/// <returns></returns>
	/// <response code="200">Returned if the offer was updated</response>
	/// <response code="400">Returned if the offer could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut]
	public async Task<ActionResult<Result<string>>> Update(Guid id, [FromBody] OfferUpdateDto offer) => 
		Result(await _mediator.Send(new UpdateOfferCommand(id, offer, User.IsMobile(), User.GetFlagContext())));

	/// <summary>
	/// Action to cancel an Offer.
	/// </summary>
	/// <param name="id"></param>
	/// <returns></returns>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("cancel/{id}")]
	public async Task<ActionResult<Result<string>>> CancelOffer(Guid id) => Result(await _mediator.Send(new CancelOfferCommand(id, User.GetFlagContext())));

	/// <summary>
	///     Action to retrieve Contracts filtered by certain criteria.
	/// </summary>
	/// <returns>Returns a list of all paid Contracts or an empty list</returns>
	/// <response code="200">Returned if the list of Contracts was retrieved</response>
	/// <response code="400">Returned if the Contracts could not be retrieved</response>
	[ProducesResponseType(typeof(Result<IReadOnlyCollection<FutureMonthsItemDto>>), StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpGet("futuresmonths")]
	public async Task<ActionResult<Result<IReadOnlyCollection<FutureMonthsItemDto>>>> FuturesMonths() => Result(await _mediator.Send(new GetAvailableFuturesMonthForOffersQuery()));

	/// <summary>
	///     Action to retrieve orphan offers filtered by certain criteria.
	/// </summary>
	/// <returns>Returns a list of all orphan offers or an empty list</returns>
	/// <response code="200">Returned if the list of orphan offers was retrieved</response>
	/// <response code="400">Returned if the orphan offers could not be retrieved</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPost("orphans")]
	public async Task<ActionResult<Result<ListDto<OrphanOfferItemDto>>>> GetOrphanOffers([FromBody] OrphanOfferFilterDto filter) => Result(await _mediator.Send(new GetAllOrphanOffersQuery(filter, User.GetFlagContext())));

	/// <summary>
	///     Action to retrieve orphan offers counted by certain criteria.
	/// </summary>
	/// <returns>Returns a number of orphan offers or zero</returns>
	/// <response code="200">Returned if there any of orphan offers was retrieved</response>
	/// <response code="400">Returned if there any orphan offers could not be retrieved</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpGet("orphanscount")]
	public async Task<ActionResult<Result<int>>> GetOrphanOffersCount() => Result(await _mediator.Send(new GetAllOrphanOffersCountQuery(User.GetFlagContext())));

	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPost("rollorphans")]
	public async Task<ActionResult<Result<RollOrphanResponseDto>>> RollOrphans([FromBody] RollOrphanDto data, CancellationToken cancellationToken) => Result(await _mediator.Send(new RollOrphanOfferCommand(data), cancellationToken));

	/// <summary>
	/// Action to book an Offer.
	/// </summary>
	/// <param name="id"></param>
	/// <returns></returns>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("book/{id}")]
	public async Task<ActionResult<Result<string>>> BookOffer(Guid id) => Result(await _mediator.Send(new BookOfferCommand(id, User.GetFlagContext())));

	/// <summary>
	/// Action to complete an Offer.
	/// </summary>
	/// <param name="id"></param>
	/// <returns></returns>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("complete/{id}")]
	public async Task<ActionResult<Result<string>>> CompleteOffer(Guid id) => Result(await _mediator.Send(new CompleteOfferCommand { OfferId = id }));

	/// <summary>
	///     Action to approve a transaction.
	/// </summary>
	/// <param name="ids">The list of guid to be updated</param>
	/// <response code="200">Returned if the process was updated</response>
	/// <response code="400">Returned if the process could not be processed</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("bookoffers")]
	public async Task<ActionResult<Result<BookOfferDto>>> BookMultipleOffers([FromBody] Guid[] ids) => Result(await _mediator.Send(new BookMultipleOffersCommand { Ids = ids, FlagContext = User.GetFlagContext() }));
}
