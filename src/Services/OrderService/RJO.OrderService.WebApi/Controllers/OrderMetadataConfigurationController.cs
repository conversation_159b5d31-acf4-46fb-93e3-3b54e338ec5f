using MediatR;
using Microsoft.AspNetCore.Mvc;
using RJO.BuildingBlocks.WebCommon;
using RJO.BuildingBlocks.WebCommon.Authorization;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.OrderService.Services.DTO.Metadata;
using RJO.OrderService.Services.Handlers.Metadata;

namespace RJO.OrderService.WebApi.Controllers;

[Produces("application/json")]
[Route("api/OrderMetadataConfigurations")]
[ApiController]
public class OrderMetadataConfigurationController : BaseController
{
	readonly IMediator _mediator;
	public OrderMetadataConfigurationController(IMediator mediator) => _mediator = mediator;

	/// <summary>
	///     Action to retrieve all custom fields configuration
	/// </summary>
	/// <returns>Returns a list of elements or an empty list</returns>
	/// <response code="200">Returned if the list was retrieved</response>
	/// <response code="400">Returned if the elements could not be retrieved</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpGet]
	public async Task<ActionResult<Result<GlobalConfigurationDto>>> GetAll() => Result(await _mediator.Send(new GetAllOrderMetadataConfigurationQuery()));

	/// <summary>
	///     Action to create/updated the configuration 
	/// </summary>
	/// <returns>Returns a list of ContractMetadataConfiguration elements or an empty list</returns>
	/// <response code="200">Returned if information changed</response>
	/// <response code="400">Returned if the elements could not be saved</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	[HttpPost]
	public async Task<ActionResult<Result<Unit>>> Create(GlobalConfigurationDto data) => Result(await _mediator.Send(new CreateOrUpdateOrderConfigurationCommand { Data = data }));
}
