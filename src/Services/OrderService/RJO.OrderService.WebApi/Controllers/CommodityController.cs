using MediatR;
using Microsoft.AspNetCore.Mvc;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.WebCommon;
using RJO.BuildingBlocks.WebCommon.Authorization;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.OrderService.Services.DTO.Catalogs.Commodity;
using RJO.OrderService.Services.DTO.Common;
using RJO.OrderService.Services.Handlers.Catalogs.Commodity;

namespace RJO.OrderService.WebApi.Controllers;

[Produces("application/json")]
[Route("api/commodities")]
[ApiController]
public class CommodityController : BaseController
{
	readonly IMediator _mediator;

	public CommodityController(IMediator mediator) => _mediator = mediator;

	/// <summary>
	///     Action to retrieve all Commodity.
	/// </summary>
	/// <returns>Returns a list of Commodity elements or an empty list</returns>
	/// <param name="filter">Body parameters. Name of property (FilterBy) to filter and value (Value)</param>
	/// <response code="200">Returned if the list was retrieved</response>
	/// <response code="400">Returned if the elements could not be retrieved</response>
	[HttpGet]
	public async Task<ActionResult<Result<CommodityItemResultToSelectDto>>> GetAll([FromQuery] CommodityFilterToSelectDto filter) => Result(await _mediator.Send(new GetAllCommodityToSelectQuery { Filter = filter }));

	/// <summary>
	///     Action to retrieve all Commodity to display the DNH option.
	/// </summary>
	/// <returns>Returns a list of Commodity elements or an empty list</returns>
	/// <response code="200">Returned if the list was retrieved</response>
	/// <response code="400">Returned if the elements could not be retrieved</response>
	[HttpGet("dnhlist")]
	[ClaimAuthorize(OperationsConstants.ContractActivateDNH)]
	public async Task<ActionResult<Result<List<CommodityItemResultToDnhDto>>>> GetToDnh() => Result(await _mediator.Send(new GetAllCommodityToDnhQuery(User.GetFlagContext())));

	[HttpPost("dnh")]
	[ClaimAuthorize(OperationsConstants.ContractActivateDNH)]
	public async Task<ActionResult<Result<Unit>>> ChangeDnhOptions([FromBody] IList<CommodityDnhChangesDto> changes) => Result(await _mediator.Send(new ChangeDnhCommand { Changes = changes }));

	[HttpGet("filter")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<ListDto<CommodityItemDto>>>> Filter([FromQuery] CommodityFilterDto filter) => Result(await _mediator.Send(new GetAllCommodityQuery { Filter = filter }));

	[HttpGet("{id}")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<CommodityDto>>> Get(Guid id) => Result(await _mediator.Send(new GetCommodityByIdQuery { Id = id }));

	/// <summary>
	///     Action to create a new commodity in the database.
	/// </summary>
	/// <remarks>
	/// Sample request:
	/// 
	///     POST 
	/// {
	/// "name": "White Corn",
	/// "priceControl": 0.2,
	/// "basisControl": 0.3,
	/// "number": "EEE555",
	/// "hedgeFutures": "ZC - GBX Corn",
	/// "autoHedge": true
	/// }
	/// 
	/// </remarks>
	/// <param name="commodity">Model to create a new commodity</param>
	/// <returns>Returns the created commodity</returns>
	/// <response code="201">Returned if the commodity was created</response>
	/// <response code="400">Returned if the model couldn't be parsed or saved</response>
	/// <response code="422">Returned when the validation failed</response>
	[ProducesResponseType(StatusCodes.Status201Created)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[ProducesResponseType(StatusCodes.Status422UnprocessableEntity)]
	[HttpPost]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<CommodityResponseDto>>> Create([FromBody] CommodityDto commodity) => Result(await _mediator.Send(new CreateCommodityCommand(commodity, User.GetFlagContext())));

	/// <summary>
	///     Action to update a Commodity.
	/// </summary>
	/// <remarks>
	/// sample request
	/// /PUT
	/// {
	///   "priceControl": 0.22,
	///   "basisControl": 0.33
	/// }
	/// </remarks>
	/// <param name="id">The guid of the commodity to be updated</param>
	/// <param name="commodity">Information of the commodity to be updated</param>
	/// <returns>Returns the updated contract</returns>
	/// <response code="200">Returned if the commodity was updated</response>
	/// <response code="400">Returned if the commodity could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<CommodityResponseDto>>> Update(Guid id, [FromBody] CommodityDto commodity) =>
		Result(await _mediator.Send(new UpdateCommodityCommand(id, commodity, User.GetFlagContext())));

	/// <summary>
	///     Action to Activate a Commodity.
	/// </summary>
	/// <param name="id">The guid of the commodity to be updated</param>
	/// <returns>Returns the updated commodity</returns>
	/// <response code="200">Returned if the commodity was updated</response>
	/// <response code="400">Returned if the commodity could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("Activate")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Unit>>> Activate(Guid id) => Result(await _mediator.Send(new ActivateCommodityCommand { CommodityId = id }));

	/// <summary>
	///     Action to Deactivate a Commodity.
	/// </summary>
	/// <param name="id">The guid of the commodity to be updated</param>
	/// <returns>Returns the updated commodity</returns>
	/// <response code="200">Returned if the commodity was updated</response>
	/// <response code="400">Returned if the commodity could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("Deactivate")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Unit>>> Deactivate(Guid id) => Result(await _mediator.Send(new DeactivateCommodityCommand { CommodityId = id }));
}
