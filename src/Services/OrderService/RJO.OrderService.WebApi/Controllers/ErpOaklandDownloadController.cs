using MediatR;
using Microsoft.AspNetCore.Mvc;
using RJO.BuildingBlocks.WebCommon;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.MultiTenancyServer.Core;
using RJO.OrderService.Domain.MultiTenancyServer;
using RJO.OrderService.Services.ErpIntegration.OaklandServices.Commands;

namespace RJO.OrderService.WebApi.Controllers;

[Produces("application/json")]
[Route("api/download")]
[ApiController]
public class ErpOaklandDownloadController : BaseController
{
	readonly IMediator _mediator;
	readonly ITenancyContext<ApplicationTenant> _tenancyContext;

	public ErpOaklandDownloadController(IMediator mediator, ITenancyContext<ApplicationTenant> tenancyContext)
	{
		_mediator = mediator;
		_tenancyContext = tenancyContext;
	}

	/// <summary>
	///     Action to get the futures price for a instrument
	/// </summary>
	/// <remarks>
	/// </remarks>
	/// <param name="startTime">download Customer information</param>
	/// <returns>Returns the futures price information</returns>
	/// <response code="201">Returned if the information is available</response>
	/// <response code="400">Returned if the service is down</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[Route("customer/oakland")]
	[HttpPost]
	public async Task<ActionResult<Result<string>>> DownloadCustomers([FromQuery] DateTime? startTime)
	{
		var tenantIdString = _tenancyContext?.Tenant?.Id;

		if (!tenantIdString.HasValue)
			throw new InvalidOperationException("TenantId was not available");

		await _mediator.Send(new DownloadCustomerCommand
		{
			StartTime = startTime,
			TenantId = tenantIdString.Value
		});

		return Result(string.Empty);
	}
}
