using MediatR;
using Microsoft.AspNetCore.Mvc;
using RJO.BuildingBlocks.WebCommon;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.OrderService.Services.DTO;
using RJO.OrderService.Services.Handlers;

namespace RJO.OrderService.WebApi.Controllers;

[Produces("application/json")]
[Route("api/Dashboard")]
public class DashboardController : BaseController
{
	readonly IMediator _mediator;

	public DashboardController(IMediator mediator) => _mediator = mediator;

	/// <summary>
	///     Action to retrieve Long and Short summary.
	/// </summary>
	/// <returns>Returns the Long and Short summary</returns>
	/// <response code="200">Returned if the Long and Short summary was retrieved</response>
	/// <response code="400">Returned if the Long and Short summary could not be retrieved</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpGet("LongAndShortSummary")]
	public async Task<ActionResult<Result<LongAndShortSummaryResultDto>>> GetLongAndShortSummary([FromQuery] LongAndShortSummaryFilterDto filter) =>
		Result(await _mediator.Send(new GetLongAndShortSummaryQuery { Filter = filter }));

	/// <summary>
	///     Action to retrieve Long and Short recap.
	/// </summary>
	/// <returns>Returns the Long and Short recap</returns>
	/// <response code="200">Returned if the Long and Short recap was retrieved</response>
	/// <response code="400">Returned if the Long and Short recap could not be retrieved</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpGet("LongAndShortRecap")]
	public async Task<ActionResult<Result<LongAndShortRecapResultDto>>> GetLongAndShortRecap([FromQuery] LongAndShortRecapFilterDto filter) =>
		Result(await _mediator.Send(new GetLongAndShortRecapQuery { Filter = filter }));
}
