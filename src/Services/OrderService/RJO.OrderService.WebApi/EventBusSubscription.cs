using RJO.BuildingBlocks.EventBus.Abstractions;
using RJO.IntegrationEvents.Commons.Events;
using RJO.OrderService.Services.Jobs.Handlers.EventHandlers;

namespace RJO.OrderService.WebApi;

public class EventBusSubscription : BackgroundService
{
	readonly IEventBus _eventBus;

	public EventBusSubscription(IEventBus eventBus) => _eventBus = eventBus;

	protected override async Task ExecuteAsync(CancellationToken stoppingToken)
	{
		await _eventBus.EnsureSubscriptionIsCreated();

		await _eventBus.Subscribe<HighAndLowsEvent, HighAndLowsEventHandler>();
		await _eventBus.Subscribe<CancelOrderEvent, CancelOrderEventHandler>();
		await _eventBus.Subscribe<FilledOrderEvent, FilledOrderEventHandler>();
		await _eventBus.Subscribe<WorkingOrderEvent, WorkingOrderEventHandler>();
		await _eventBus.Subscribe<RejectedOrderEvent, RejectedOrderEventHandler>();
		await _eventBus.Subscribe<ExpiredOrderEvent, ExpiredOrderEventHandler>();
		await _eventBus.Subscribe<CancelRejectedEvent, CancelRejectedEventHandler>();
		await _eventBus.Subscribe<EditRejectedEvent, EditRejectedEventHandler>();

		await _eventBus.StartProcessing(stoppingToken);
	}
}
