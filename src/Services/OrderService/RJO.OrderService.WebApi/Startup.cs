using Hangfire;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Common.Azure.ApplicationInsights;
using RJO.BuildingBlocks.Common.HealthChecks;
using RJO.BuildingBlocks.WebCommon;
using RJO.BuildingBlocks.WebCommon.Configuration;
using RJO.BuildingBlocks.Common.Extensions;
using RJO.BuildingBlocks.Common.Hangfire;
using RJO.MultiTenancyServer.AspNetCore.Extensions;
using RJO.MultiTenancyServer.EFCore.Extensions;
using RJO.OrderService.Domain; 
using RJO.OrderService.Domain.MultiTenancyServer;
using RJO.OrderService.Domain.Settings;
using RJO.OrderService.Persistence;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.ErpIntegration;
using RJO.OrderService.Services.Resilience;
using RJO.OrderService.Services.Services;
using RJO.OrderService.Services.Features.Observability;
using RJO.OrderService.WebApi.Converters;
using RJO.OrderService.WebApi.Core.Configuration.Extensions;
using RJO.OrderService.WebApi.Testing;
using System.Reflection;
using System.Security.Claims;
using OpenTelemetry.Metrics;

namespace RJO.OrderService.WebApi;

static class Startup
{
	const string SwaggerVersion = "S12.0.0.1";
	const string CorsName = "AllowAll";

	public static IServiceCollection ConfigureApplication(this IServiceCollection services, IConfiguration configuration, IHostEnvironment hostEnvironment)
	{
		services.AddAllElasticApm();
		services.AddInfrastructureServices(configuration, hostEnvironment);
		services.AddLaunchDarkly(configuration);
		services.AddHttpContextAccessor();
		services.AddLocalization();
		services.AddDataProtection().SetApplicationName("RJOHrvystEdge"); //we should use data protection strategy here
		services.Configure<RouteOptions>(config => { config.LowercaseUrls = true; });
		services.AddCustomApplicationInsightsTelemetry(configuration, hostEnvironment);
		services
			.AddHealthChecks()
			.AddApplicationHealthChecks<AppDbContext>(configuration);

		services.Configure<CQGSettings>(configuration.GetSection("CQG"));
		services.AddSingleton(resolver => resolver.GetRequiredService<IOptions<CQGSettings>>().Value);
		services.Configure<RJOSettings>(configuration.GetSection("RJO"));
		services.AddSingleton(resolver => resolver.GetRequiredService<IOptions<RJOSettings>>().Value);
		services.Configure<MarketSettings>(configuration.GetSection("Market"));
		services.AddSingleton(resolver => resolver.GetRequiredService<IOptions<MarketSettings>>().Value);
		services.Configure<GeneralSettings>(configuration.GetSection("General"));
		services.AddSingleton(resolver => resolver.GetRequiredService<IOptions<GeneralSettings>>().Value);
		services.AddSingleton<BackgroundTaskChannel>();
		services.AddHostedService<QueuedHostedService>();
		
		services.ConfigureOptions(configuration);

		services.AddMemoryCache();
		services.AddControllers().ConfigureModelBindingExceptionHandling();

		var connectionString = configuration.DatabaseDefaultConnectionString();
		services.AddDbContext<AppDbContext>(
			options =>
			{
				options.UseSqlServer(connectionString, x =>
				{
					x.MigrationsHistoryTable("EFMigration", "Historical")
						.CommandTimeout(10000);
					x.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
				});
				options.EnableSensitiveDataLogging();
			});

		DefaultConnectionString.Connection = connectionString;

		services
			.AddJobs()
			.AddEventBus(configuration)
			.AddRepositories()
			.AddDomainServices();

		var xmlPath = Path.Combine(AppContext.BaseDirectory, $"{Assembly.GetExecutingAssembly().GetName().Name}.xml");
		services.ConfigureSwagger(SwaggerVersion, "RJO Contract Service", "RJO Web Services", xmlPath);
		services.ConfigureHangfireServices(configuration);

		services.AddAuthentication().AddAzureAD(options =>
		{
			options.Instance = configuration["HangfireAuth:Instance"];
			options.Domain = configuration["HangfireAuth:Domain"];
			options.TenantId = configuration["HangfireAuth:TenantId"];
			options.ClientId = configuration["HangfireAuth:ClientId"];
			options.CallbackPath = configuration["HangfireAuth:CallbackPath"];
		});

		services.AddAuthentication(configuration);
		services.AddResilience(configuration);
		services.AddAuthorization(options =>
		{
			options.AddPolicy("api.agris", policy => policy.RequireClaim("scope", "api.agris", "api.agris.pointtsettrice"));
			options.AddPolicy("api.agtrax", policy => policy.RequireClaim("scope", "api.agtrax"));
			options.AddPolicy("api.oakland", policy => policy.RequireClaim("scope", "api.oakland"));
			options.AddPolicy("HangfirePolicy", builder =>
			{
				builder
					.AddAuthenticationSchemes("AzureAD")
					.RequireAuthenticatedUser();
			});
		});
		services.AddAuthorizationHandlers();
		services.AddServicesHttpClients();
		services.AddMultiTenancy<ApplicationTenant, Guid>()
			.AddClaimParser(CustomClaimTypes.Tenant)
			.AddEntityFrameworkStore<AppDbContext, ApplicationTenant, Guid>();

		services.AddMvc()
			.AddApplicationPart(typeof(IErpIntegration).Assembly).AddControllersAsServices()
			.AddJsonOptions(options => options.JsonSerializerOptions.Converters.Add(new DBNullConverter()));

		services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
		services.AddTransient<CurrentUser>(s =>
		{
			var contextAccessor = s.GetService<IHttpContextAccessor>();
			var user = contextAccessor?.HttpContext?.User;
			if (user == null)
			{
				return new(CurrentUser.InternalUser, Guid.Empty.ToString());
			}

			return new(
				user.Claims.FirstOrDefault(x => x.Type == ClaimTypes.Email)?.Value,
				user.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value
			);
		});

		// Insert this rather than just adding, to ensure that it runs first
		services.InsertHostedService<ContractIdentityGeneratorInitializer>();

		services.AddCors(options =>
		{
			options.AddPolicy(CorsName, policyBuilder =>
			{
				policyBuilder
					.SetIsOriginAllowed(_ => true) // David Fowler: Using this is required instead of AllowAnyOrigin for Azure SignalR Service.
					.AllowCredentials()
					.AllowAnyMethod()
					.AllowAnyHeader();
			});
		});
		services.AddNotification(configuration, hostEnvironment);

		services.AddCacheSettlement();
		services.AddPhoneNumberHandler();

		services.AddApplicationInsightsPayloadLogging();
		services.AddObservability();
		services.AddOpenTelemetry()
			.WithMetrics(builder =>
			{
				builder.AddAspNetCoreInstrumentation()
					.AddHangfireInstrumentation()
					.AddOrderServiceInstrumentation()
					.AddPrometheusExporter();
			});
		
		services.AddHttpClient("SpreadService", (serviceProvider, client) =>
			{
				var configuration = serviceProvider.GetRequiredService<IConfiguration>();
				var baseUrl = "https://localhost:44368";
				client.BaseAddress = new Uri(baseUrl);
			})
			.ConfigurePrimaryHttpMessageHandler((serviceProvider) =>
			{
				var configuration = serviceProvider.GetRequiredService<IConfiguration>();
				var bypassSsl = true;
    
				var handler = new HttpClientHandler();
				if (bypassSsl)
				{
					handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
				}
				return handler;
			});


		return services;
	}

	public static IApplicationBuilder ConfigureApplication(this IApplicationBuilder app)
	{
		var env = app.ApplicationServices.GetRequiredService<IHostEnvironment>();

		if (env.IsLocal())
		{
			app.UseDeveloperExceptionPage();
		}

		app.UseHttpsRedirection();

		app.UseCors(CorsName);
		app.UseRouting();
		
		app.UseApplicationInsightsPayloadLogging();
		app.ConfigureExceptionHandler();

		app.UseMiddleware<FakeAuthenticationMiddleware>();
		app.UseAuthentication();
		app.UseAuthorization();
		app.UseMultiTenancy<ApplicationTenant>();

		if (env.IsDevelopment() || env.IsLocal())
		{
			app.UseSwagger();
			app.UseSwaggerUI(c =>
			{
				c.SwaggerEndpoint("/swagger/v1/swagger.json", "RJO Contract Service " + SwaggerVersion);
				c.RoutePrefix = string.Empty;
			});
		}

		app.UseEndpoints(endpoints =>
		{
			endpoints.MapHealthChecks("/health", new() { ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse });
			endpoints.Map("/", async context =>
			{
				context.Response.StatusCode = 200;
			});
			endpoints.MapControllers();
			endpoints.MapHangfireDashboardWithLock()
				.RequireAuthorization("HangfirePolicy");
			endpoints.MapNotification();
			endpoints.MapObservability();
			endpoints.MapDefaultControllerRoute();
		});

		app.ConfigureJobs();

		return app;
	}
}
