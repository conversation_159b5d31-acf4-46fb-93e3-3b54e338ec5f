using Autofac;
using Autofac.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.ApplicationInsights;
using RJO.BuildingBlocks.Common;
using RJO.OrderService.WebApi;
using RJO.OrderService.WebApi.Core.Configuration.Extensions;
using RJO.OrderService.WebApi.Testing;

StartupLogger.Current.Info("Building OrderService ...");

var builder = WebApplication
	.CreateBuilder(args)
	.AddLocalUserSecrets(typeof(Program).Assembly)
	.MaybeAddAzureAppConfiguration(StartupLogger.Current);

builder.Logging
	.AddApplicationInsights(builder.Configuration.ApplicationInsightsInstrumentationKey(builder.Environment))
	.AddFilter<ApplicationInsightsLoggerProvider>(typeof(Program).FullName, LogLevel.Trace)
	.AddFilter<ApplicationInsightsLoggerProvider>(typeof(Startup).FullName, LogLevel.Trace);

await builder.MaybeUseTestEnvironment();

if (!builder.Environment.IsLocal())
{ 
	builder.WebHost
		.UseSentry(o =>
		{
			o.MinimumEventLevel = LogLevel.Error;
			o.MinimumBreadcrumbLevel = LogLevel.Error;
		});
}

builder.Host
	.UseServiceProviderFactory(new AutofacServiceProviderFactory())
	.ConfigureContainer<ContainerBuilder>((_, containerBuilder) => containerBuilder.ConfigureComponents());

builder.Services
	.ConfigureApplication(builder.Configuration, builder.Environment);

var app = builder.Build();

StartupLogger.Current.Info("OrderService built.");

app.LogUnhandledExceptions();

app.ConfigureApplication();

StartupLogger.Current.Info("Starting OrderService ...");

app.Run();

// Expose a type for use with WebApplicationFactory<T>
public abstract record OrderServiceEntryPoint;
