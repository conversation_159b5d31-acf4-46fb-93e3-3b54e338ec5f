using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.Resources;
using RJO.OrderService.Domain.Contracts;
using RJO.OrderService.Domain.Settings;

namespace RJO.OrderService.Domain;

public class HedgeMapping : Entity, IAuditable, ITenantable
{
	HedgeMapping() { }

	public HedgeMapping(Guid commodityId, short cropYear)
	{
		CommodityId = commodityId;
		CropYear = cropYear;
	}

	public HedgeMapping(Guid commodityId, bool isSell, Guid contractTypeId, short cropYear, string futureMonth, byte month, short shortYear, Guid? regionId)
	{
		AssertionConcern.ArgumentIsNotNull(commodityId, GeneralResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(contractTypeId, GeneralResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(cropYear, GeneralResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentStringNotNullOrEmpty(futureMonth, GeneralResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(month, GeneralResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(shortYear, GeneralResources.RequiredValueIsNotPresent);

		CommodityId = commodityId;
		IsSell = isSell;
		ContractTypeId = contractTypeId;
		CropYear = cropYear;
		FutureMonth = futureMonth;
		Month = month;
		ShortYear = shortYear;
		RegionId = regionId;

		Id = IdentityGenerator.NewSequentialGuid();
		IsActive = true;
	}

	public Guid CommodityId { get; private set; }
	public bool IsSell { get; private set; }
	public Guid ContractTypeId { get; private set; }
	public Guid? RegionId { get; private set; }
	public Region Region { get; set; }
	public short CropYear { get; private set; }
	public string FutureMonth { get; private set; }
	public byte Month { get; private set; }
	public short ShortYear { get; private set; }
	
	public void ChangeRegionId(Guid regionId) => RegionId = regionId;
}
