using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Domain.Contracts;
using RJO.OrderService.Domain.Enumeration;

namespace RJO.OrderService.Domain.Metadata;

public class OrderMetadataConfiguration : Entity, IAuditable, ITenantable
{
	public EOrderMetadataFieldType Type { get; set; }
	public string Label { get; set; }
	public short Order { get; set; }
	public string ErpField { get; set; }
	public string DefaultValue { get; set; }
	OrderMetadataConfiguration() { }

	public OrderMetadataConfiguration(EOrderMetadataFieldType type, string label, short order, bool isActive, string erpField = "", string defaultValue = "")
	{
		if (isActive)
		{
			AssertionConcern.ArgumentStringNotNullOrEmpty(label, "Label is required");
			AssertionConcern.ArgumentIsNotEquals(type, EOrderMetadataFieldType.Undefined, "Undefined is not a value option");
		}
		else
		{
			type = EOrderMetadataFieldType.Undefined;
		}

		AssertionConcern.ArgumentIsBiggerOrEqualThan(order, 0, "Order is invalid");
		AssertionConcern.ArgumentIsTrue(Enum.IsDefined(typeof(EOrderMetadataFieldType), type), "Type is invalid");
		Type = type;
		Label = label ?? string.Empty;
		Order = order;
		Id = IdentityGenerator.NewSequentialGuid();
		IsActive = isActive;
		ErpField = erpField;
		DefaultValue = defaultValue;
	}

	public void ChangeErpField(string field) => ErpField = field ?? string.Empty;
	public void ChangeDefaultValue(string value) => DefaultValue = value ?? string.Empty;

	public void ChangeOrder(short order)
	{
		AssertionConcern.ArgumentIsBiggerOrEqualThan(order, 0, "Order is invalid");
		Order = order;
	}

	public void ChangeLabel(string label)
	{
		if (IsActive)
		{
			AssertionConcern.ArgumentStringNotNullOrEmpty(label, "Label is required");
		}

		Label = label ?? string.Empty;
	}

	public void ChangeType(EOrderMetadataFieldType type)
	{
		if (IsActive)
		{
			AssertionConcern.ArgumentIsTrue(Enum.IsDefined(typeof(EOrderMetadataFieldType), type), "Type is invalid");
		}

		Type = type;
	}
}
