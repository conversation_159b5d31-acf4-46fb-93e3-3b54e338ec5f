using JetBrains.Annotations;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.BusinessExceptions;
using RJO.OrderService.Common;
using RJO.OrderService.Common.Exceptions;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain.Contracts;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.Events;
using RJO.OrderService.Domain.Settings;

namespace RJO.OrderService.Domain;

public class Contract : AggregateRoot, IAuditable, ITenantable, ICloneable
{
	[UsedImplicitly]
	Contract() { }

	public Contract(Guid transactionTypeId,
		Guid contractTypeId,
		bool isSell,
		Guid commodityId,
		Guid locationId,
		Guid deliveryLocationId,
		bool isDeliveryDatesCustom,
		DateTime deliveryStartDate,
		DateTime deliveryEndDate,
		short cropYear,
		Guid customerId,
		Guid employeeId,
		string futuresMonth,
		decimal? futuresPrice,
		decimal? postedBasis,
		decimal? pushBasis,
		decimal freightPrice,
		decimal fees1,
		decimal fees2,
		decimal quantity,
		string comments,
		bool passFill,
		bool doNotHedge,
		bool cashSettlement,
		DateTime? expiration,
		string source,
		Guid? regionId,
		EContractEvent contractEvent = EContractEvent.Create,
		bool isBookedPartialFill = false,
		string internalCode = null)
	{
		AssertionConcern.ArgumentIsNotEmpty(transactionTypeId, ContractResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotEmpty(contractTypeId, ContractResources.RequiredValueIsNotPresent);
		ValidateFieldsPerContractType(contractTypeId, futuresMonth, futuresPrice, postedBasis, pushBasis, passFill, doNotHedge, cashSettlement);
		AssertionConcern.ArgumentIsNotEmpty(commodityId, ContractResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotEmpty(locationId, ContractResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotEmpty(deliveryLocationId, ContractResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotEmpty(employeeId, ContractResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(comments, ContractResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsBiggerThan(quantity, 0, ContractResources.QuantityMustBePositive);
		AssertionConcern.ArgumentIsNotEmpty(customerId, ContractResources.RequiredValueIsNotPresent);
		if (source != ContractSource.Migration && source != ContractSource.Migrated)
		{
			// TODO: 3147 - We have to exclude accumulation because it can result in average price being not exactly divisible by quarter cents.
			// But this can presumably be for a contract executed as a market order too, so what do we do about that?
			// And exclude ERP spot trades
			if (source != ContractSource.Accumulation && source != ContractSource.ERP)
			{
				ContractAssertionConcern.ValidateQuarterCent(futuresPrice, false);
			}
			ContractAssertionConcern.CropYearIsValid(cropYear);
		}

		if (contractTypeId == ContractTypeDictionary.NTC && futuresMonth == null)
		{
			//updating future month if it is a null for ntc
			futuresMonth = "";
		}

		Event = contractEvent.ToString();
		ValidateDeliveryDates(deliveryStartDate, deliveryEndDate);
		IsDeliveryDatesCustom = isDeliveryDatesCustom;
		TransactionTypeId = transactionTypeId;
		ContractTypeId = contractTypeId;
		IsSell = isSell;
		CommodityId = commodityId;
		LocationId = locationId;
		DeliveryLocationId = deliveryLocationId;
		DeliveryStartDate = deliveryStartDate;
		DeliveryEndDate = deliveryEndDate;
		CropYear = cropYear;
		RealCropYear = cropYear;
		CustomerId = customerId;
		EmployeeId = employeeId;
		UpdatedEmployeeId = employeeId;
		FuturesMonth = futuresMonth;
		FuturesPrice = contractTypeId == ContractTypeDictionary.Basis ? null : futuresPrice;
		PostedBasis = postedBasis;
		PushBasis = pushBasis;
		FreightPrice = freightPrice;
		Fees1 = fees1;
		Fees2 = fees2;
		Quantity = quantity;
		LastTransactionQuantity = quantity;
		if (ContractTypeId == ContractTypeDictionary.FlatPrice)
		{
			RemainingBalance = 0;
			Status = new(EContractState.Priced);
		}
		else
		{
			RemainingBalance = quantity;
			Status = new(EContractState.Open);
		}
		GrossRemainingBalance = quantity;
		Comments = comments;
		PassFill = passFill;
		Price = passFill ? null : 0;
		DoNotHedge = doNotHedge;
		CashSettlement = cashSettlement;
		Expiration = expiration;
		Id = IdentityGenerator.NewSequentialGuid();
		InternalCode = !string.IsNullOrEmpty(internalCode) ? internalCode : ContractIdentityGenerator.NewContractNumber(ContractTypeId, IsSell);
		IsActive = true;
		ChildCount = 0;
		ChildOffersCount = 0;
		Source = source;
		CalculateNetBasisValue();
		CalculatePrice();
		if (source == ContractSource.Migration)
		{
			if ((ContractTypeId == ContractTypeDictionary.Basis || ContractTypeId == ContractTypeDictionary.HTA)
				&& futuresPrice.HasValue && futuresPrice.Value != 0 && postedBasis.HasValue)
			{
				FuturesPrice = futuresPrice;
				PostedBasis = postedBasis;
				Price = FuturesPrice.Value + (NetBasis ?? 0) + FreightPrice + Fees1 + Fees2;
				Status = new(EContractState.Priced);
				RemainingBalance = 0;
			}
		}
		CreateHistoricEvent(EContractEvent.Create);
		_isBookedPartialFill = isBookedPartialFill;
		CreateNotificationEvent(EContractState.Open);
		RegionId = regionId;
	}

	public ContractState Status { get; private set; }
	public string InternalCode { get; private set; }
	public string Event { get; private set; }
	public Guid TransactionTypeId { get; private set; }
	public Guid ContractTypeId { get; private set; }
	public Guid ExtendedContractTypeId { get; private set; }
	public bool IsDeliveryDatesCustom { get; private set; }
	public bool IsSell { get; private set; }
	public Guid CommodityId { get; private set; }
	public Guid LocationId { get; private set; }
	public Guid DeliveryLocationId { get; private set; }
	public DateTime DeliveryStartDate { get; private set; }
	public DateTime DeliveryEndDate { get; private set; }
	public short CropYear { get; private set; }
	public short RealCropYear { get; private set; }
	public Guid CustomerId { get; private set; }
	public Guid EmployeeId { get; private set; }
	public Guid UpdatedEmployeeId { get; private set; }
	public string FuturesMonth { get; private set; }
	public decimal? FuturesPrice { get; private set; }
	public decimal? PostedBasis { get; private set; }
	public decimal? PushBasis { get; private set; }
	public decimal? NetBasis { get; private set; }
	public decimal FreightPrice { get; private set; }
	public decimal Fees1 { get; private set; }
	public decimal Fees2 { get; private set; }
	public decimal? Price { get; private set; }
	public decimal Quantity { get; private set; }
	public decimal LastTransactionQuantity { get; private set; }
	public string Comments { get; private set; }
	public string Number { get; private set; }
	public bool PassFill { get; private set; }
	public bool DoNotHedge { get; private set; }
	public bool CashSettlement { get; private set; }
	public int ChildCount { get; private set; }
	public int ChildOffersCount { get; private set; }
	public Guid? RootParentId { get; private set; }
	public Guid? ParentId { get; private set; }
	public decimal RemainingBalance { get; private set; }
	public decimal GrossRemainingBalance { get; private set; }
	public Guid? OfferId { get; private set; }
	public ErpStatus ErpStatus { get; private set; }
	public string ErpMessage { get; private set; }
	public string Source { get; private set; }
	public DateTime? Expiration { get; private set; }
	public long SeqId { get; private set; }
	public string TheirContract { get; private set; }
	public Guid? RegionId { get; private set; }
	public Guid? GroupContractId { get; private set; }

	private bool _isBookedPartialFill;

	public TransactionType TransactionType { get; set; }
	public ContractType ContractType { get; set; }
	public Commodity Commodity { get; set; }
	public Location Location { get; set; }
	public Location DeliveryLocation { get; set; }
	public Customer Customer { get; set; }
	public Employee Employee { get; set; }
	public Employee UpdatedEmployee { get; set; }
	public Region Region { get; set; }
	public Contract Parent { get; set; }
	public ICollection<Offer> LinkedOffers { get; } = new List<Offer>();
	public bool? IsAppliedLoad { get; set; }

	public bool IsBookedPartialFill() => _isBookedPartialFill;

	public object Clone() => MemberwiseClone();

	[System.Diagnostics.CodeAnalysis.SuppressMessage("Naming", "CA1721:Property names should not match get methods", Justification = "Required to work in configurations")]
	public decimal AvailableQuantity =>
		Status.Value switch
		{
			EContractState.Open => RemainingBalance,
			EContractState.Priced => GrossRemainingBalance,
			EContractState.Canceled => 0,
			EContractState.Convert => GrossRemainingBalance,
			_ => 0
		};
	public decimal GetAvailableQuantity(EContractState originalStatus) =>
		originalStatus switch
		{
			EContractState.Open => RemainingBalance,
			EContractState.Priced => GrossRemainingBalance,
			EContractState.Canceled => 0,
			EContractState.Convert => GrossRemainingBalance,
			_ => 0
		};

	public int ErpMemberChanged(Contract c)
	{
		var cnt = 0;
		if (Number != c.Number)
			cnt++;
		if (IsDeliveryDatesCustom != c.IsDeliveryDatesCustom)
			cnt++;
		if (LocationId != c.LocationId)
			cnt++;
		if (CommodityId != c.CommodityId)
			cnt++;
		if (CropYear != c.CropYear)
			cnt++;

		if (DeliveryLocationId != c.DeliveryLocationId)
			cnt++;
		if (!Expiration.Equals(c.Expiration))
			cnt++;
		if (DeliveryStartDate != c.DeliveryStartDate)
			cnt++;
		if (DeliveryEndDate != c.DeliveryEndDate)
			cnt++;

		if (!Fees1.Equals(c.Fees1))
			cnt++;
		if (!Fees2.Equals(c.Fees2))
			cnt++;
		if (!FreightPrice.Equals(c.FreightPrice))
			cnt++;
		if (!PostedBasis.Equals(c.PostedBasis))
			cnt++;
		if (!PushBasis.Equals(c.PushBasis))
			cnt++;
		if (!FuturesMonth.Equals(c.FuturesMonth, StringComparison.Ordinal))
			cnt++;
		if (!FuturesPrice.Equals(c.FuturesPrice))
			cnt++;

		if (!Comments.Equals(c.Comments, StringComparison.Ordinal))
			cnt++;
		if (EmployeeId != c.EmployeeId)
			cnt++;
		if (UpdatedEmployeeId != c.UpdatedEmployeeId)
			cnt++;
		return cnt;
	}

	#region Operations

	public Contract CreateChild(decimal quantity, EContractEvent operation, bool reduceParentGrossRemainingBalance = true) => CreateChild(quantity, operation, EmployeeId, null, reduceParentGrossRemainingBalance);
	public Contract CreateChild(decimal quantity, EContractEvent operation, Guid? newContractTypeId, bool reduceParentGrossRemainingBalance) => CreateChild(quantity, operation, EmployeeId, newContractTypeId, reduceParentGrossRemainingBalance);

	public Contract CreateChild(decimal quantity, EContractEvent operation, Guid employeeId, Guid? ConvertContractTypeId = null, bool reduceParentGrossRemainingBalance = true)
	{
		var child = new Contract(TransactionTypeId, ConvertContractTypeId.HasValue ? ConvertContractTypeId.Value : ContractTypeId, IsSell, CommodityId, LocationId, DeliveryLocationId, IsDeliveryDatesCustom,
			DeliveryStartDate, DeliveryEndDate, RealCropYear, CustomerId, employeeId, FuturesMonth, FuturesPrice, PostedBasis, PushBasis, FreightPrice,
			Fees1,
			Fees2, quantity, Comments, PassFill, DoNotHedge, CashSettlement, Expiration, ContractSource.Child, RegionId, operation);
		child.RootParentId = RootParentId ?? Id;
		child.ParentId = Id;
		ChildCount++;
		if (reduceParentGrossRemainingBalance)
			ReduceGrossRemainingBalance(quantity);
		return child;
	}

	public Contract CreateChild(EContractEvent operation, Contract request, bool reduceParentGrossRemainingBalance = true)
	{
		if (request.TransactionTypeId == TransactionTypeDictionary.BushelOnly)
		{
			request.TransactionTypeId = TransactionTypeDictionary.CashContract;
		}

		var child = new Contract(request.TransactionTypeId, request.ContractTypeId, request.IsSell, request.CommodityId, request.LocationId, request.DeliveryLocationId, request.IsDeliveryDatesCustom,
			request.DeliveryStartDate, request.DeliveryEndDate, request.RealCropYear, request.CustomerId, request.EmployeeId, request.FuturesMonth, request.FuturesPrice, request.PostedBasis, request.PushBasis,
			request.FreightPrice, request.Fees1, request.Fees2, request.Quantity, request.Comments, request.PassFill, request.DoNotHedge, request.CashSettlement, request.Expiration, ContractSource.Child,
			RegionId, operation);
		child.RootParentId = RootParentId ?? Id;
		child.ParentId = Id;
		ChildCount++;

		if (reduceParentGrossRemainingBalance)
			ReduceGrossRemainingBalance(request.Quantity);
		return child;
	}

	public Offer CreateHTAOffer(decimal quantity, bool gtc, DateTime? expiration, decimal futuresPrice, Guid employeeId)
	{
		var child = new Offer(TransactionTypeId, ContractTypeDictionary.HTA, IsSell, IsDeliveryDatesCustom, CommodityId, LocationId, DeliveryLocationId,
			DeliveryStartDate, DeliveryEndDate, RealCropYear, CustomerId, employeeId, FuturesMonth, futuresPrice, null, null, null,
			FreightPrice, Fees1,
			Fees2, Price ?? SimulatePrice(), quantity, Comments, gtc, expiration, CashSettlement, RegionId);
		child.ChangePushBasis(PushBasis);
		child.ChangePostedBasis(PostedBasis ?? 0);
		child.AsignContractParent(Id);
		//child.DefineRealCropYear(RealCropYear);
		ChildOffersCount++;
		return child;
	}

	public Offer CreateBasisOffer(decimal quantity, bool gtc, DateTime? expiration, decimal postedBasis, decimal pushBasis, Guid employeeId)
	{
		var child = new Offer(TransactionTypeId, ContractTypeDictionary.Basis, IsSell, IsDeliveryDatesCustom, CommodityId, LocationId, DeliveryLocationId,
			DeliveryStartDate, DeliveryEndDate, RealCropYear, CustomerId, employeeId, FuturesMonth, FuturesPrice, postedBasis, pushBasis, pushBasis + postedBasis,
			FreightPrice, Fees1,
			Fees2, Price ?? SimulatePrice(), quantity, Comments, gtc, expiration, CashSettlement, RegionId);
		//child.ChangePushBasis(pushBasis);
		//child.ChangePostedBasis(postedBasis);
		child.AsignContractParent(Id);
		//child.DefineRealCropYear(RealCropYear);
		ChildOffersCount++;
		return child;
	}

	public void ReduceRemainingBalance(decimal quantity)
	{
		AssertionConcern.ArgumentIsBiggerOrEqualThan(RemainingBalance, quantity, ContractResources.RemaninigBalanceIsInvalid);
		RemainingBalance -= quantity;
	}

	public void AddGrossRemainingBalance(decimal quantity)
	{
		AssertionConcern.ArgumentIsSmallerOrEqualThan(GrossRemainingBalance + quantity, Quantity, ContractResources.RemaninigBalanceIsInvalid);
		GrossRemainingBalance += quantity;
	}

	public void ReduceGrossRemainingBalance(decimal quantity)
	{
		AssertionConcern.ArgumentIsSmallerOrEqualThan(quantity, GrossRemainingBalance, ContractResources.RemaninigBalanceIsInvalid);
		GrossRemainingBalance -= quantity;
	}

	public void ReturningBalanceFromChild(decimal quantity, EContractState originalStatus)
	{
		if (ContractTypeId == ContractTypeDictionary.NTC)
		{
			return;
		}

		AssertionConcern.ArgumentIsBiggerThan(quantity, 0, ContractResources.QuantityMustBePositive);
		if (Quantity == 0)
			Quantity += quantity;

		AssertionConcern.ArgumentIsSmallerOrEqualThan(GrossRemainingBalance + quantity, Quantity, ContractResources.RemaninigBalanceIsInvalid);
		GrossRemainingBalance += quantity;

		if (ContractTypeId == ContractTypeDictionary.FlatPrice || originalStatus == EContractState.Priced)
		{
			Status = Status.ChangeStatusToPriced();
		}
		else
		{
			Status = Status.ChangeStatusToOpen();
			RemainingBalance += quantity;
		}
	}

	public void ApplyNameId(decimal quantity)
	{
		RemainingBalance -= quantity;
		if (RemainingBalance <= 0)
		{
			RemainingBalance = 0;
			Status = Status.ChangeStatusToPriced();
		}
	}

	public void CancelRemainingBalance() => RemainingBalance = 0;
	public void CancelGrossRemainingBalance() => GrossRemainingBalance = 0;

	#endregion

	#region Change properties

	public void ChangeRegionId(Guid regionId) => RegionId = regionId;

	public void ChangeGroupContractId(Guid groupContractId) => GroupContractId = groupContractId;

	public void ValidatePrice()
	{
		if (Status.Is(EContractState.Priced) && NetBasis.HasValue && FuturesPrice.HasValue)
		{
			Price = FuturesPrice + NetBasis + FreightPrice + Fees1 + Fees2;
		}
		else if (PassFill && ContractTypeId != ContractTypeDictionary.Basis)
		{
			Price = FuturesPrice != null && Price == null ? 0 : Price;
		}
	}

	public void AssignOffer(Guid? offerId) => OfferId = offerId;

	public void AssignContractNumber(string number)
	{
		if (string.IsNullOrEmpty(number)) return;
		AddDomainEvent(new ContractLedgerTransactionNotification(Id, EContractEvent.Edit.ToString(), number));
		Number = number;
	}

	public void AssignExtendedContractTypeId(Guid? extendedContractTypeId)
	{
		if (extendedContractTypeId.HasValue)
		{
			ExtendedContractTypeId = extendedContractTypeId.Value;
		}
	}

	public void AssignContractNumberWithOutInformIt(string number)
	{
		if (string.IsNullOrEmpty(number)) return;
		Number = number;
	}

	public void AssignTheirContract(string theirContract)
	{
		if (string.IsNullOrEmpty(theirContract)) return;
		AssertionConcern.ArgumentIsNotTrue(theirContract.Length > 25, "'Their Contract' field is too large, only 25 characteres or less are allowed");
		TheirContract = theirContract;
	}

	public bool IsCanceled() => Status.Value == EContractState.Canceled;

	public void SetConvertViaEFPContractType(Guid contractTypeId)
	{
		TransactionTypeId = TransactionTypeDictionary.CashContract;

		if (contractTypeId.Equals(Guid.Empty) || contractTypeId.Equals(ContractTypeId))
			return;

		ContractTypeId = contractTypeId;
		if (ContractTypeId == ContractTypeDictionary.FlatPrice)
		{
			Status = new(EContractState.Priced);
			CancelRemainingBalance();
		}
		else
			Status = new(EContractState.Open);
	}

	public void ChangeLocation(Guid locationId)
	{
		if (locationId.Equals(Guid.Empty) || locationId.Equals(LocationId))
			return;
		LocationId = locationId;
	}

	public void ChangeDeliveryLocation(Guid deliveryLocationId)
	{
		if (deliveryLocationId.Equals(Guid.Empty) || deliveryLocationId.Equals(DeliveryLocationId))
			return;
		DeliveryLocationId = deliveryLocationId;
	}

	public void ChangeFuturesMonth(string futuresMonth)
	{
		if (string.IsNullOrEmpty(futuresMonth) || FuturesMonth.Equals(futuresMonth, StringComparison.OrdinalIgnoreCase))
			return;
		FuturesMonth = futuresMonth;
	}

	public void ChangeDeliveryDates(DateTime? deliveryStartDate, DateTime? deliveryEndDate)
	{
		if (!deliveryStartDate.HasValue || deliveryStartDate.Value == default)
			return;
		if (!deliveryEndDate.HasValue || deliveryEndDate.Value == default)
			return;
		ValidateDeliveryDates(deliveryStartDate.Value, deliveryEndDate.Value);
		DeliveryStartDate = deliveryStartDate.Value;
		DeliveryEndDate = deliveryEndDate.Value;
	}

	public void ChangeIsDeliveryDatesCustom(bool? isDeliveryDatesCustom)
	{
		if (!isDeliveryDatesCustom.HasValue || isDeliveryDatesCustom == IsDeliveryDatesCustom)
			return;
		IsDeliveryDatesCustom = isDeliveryDatesCustom.Value;
	}

	public void ChangeCropYear(short cropYear)
	{
		if (cropYear == 0 || cropYear.Equals(CropYear))
			return;
		ContractAssertionConcern.CropYearIsValid(cropYear);
		CropYear = cropYear;
		RealCropYear = cropYear;
	}

	public void ChangeAndVerifyPrice(string futuresMonth,
		decimal? futuresPrice,
		decimal? postedBasis,
		decimal? pushBasis,
		decimal? freightPrice,
		decimal? fees1,
		decimal? fees2,
		EContractEvent? eventName = null)
	{
		SetCommonValues(fees1, fees2, freightPrice);
		if (ContractTypeId == ContractTypeDictionary.FlatPrice)
		{
			ValidateFlatPriceFieldRelatedValues(futuresMonth, futuresPrice, postedBasis, pushBasis);
			FuturesMonth = futuresMonth;
			FuturesPrice = futuresPrice;
			PostedBasis = postedBasis;
			PushBasis = pushBasis;
			CalculateNetBasisValue();
			CalculatePrice();
		}
		else if (ContractTypeId == ContractTypeDictionary.HTA)
		{
			ValidateHtaFieldRelatedValues(futuresMonth, futuresPrice, postedBasis, pushBasis, eventName.ToString());
			FuturesMonth = futuresMonth;
			FuturesPrice = futuresPrice;
			PostedBasis = postedBasis;
			PushBasis = pushBasis;
		}
		else if (ContractTypeId == ContractTypeDictionary.Basis)
		{
			ValidateBasisFieldRelatedValues(futuresMonth, futuresPrice, postedBasis, pushBasis);
			PostedBasis = postedBasis;
			PushBasis = pushBasis;
			FuturesPrice = futuresPrice;
			CalculateNetBasisValue();
		}
		else
		{
			PostedBasis = postedBasis;
			PushBasis = pushBasis;
			FuturesPrice = futuresPrice;
		}
	}

	public void ChangeFreightPrice(decimal? freightPrice)
	{
		if (freightPrice == null)
		{
			return;
		}

		FreightPrice = freightPrice.Value;
	}

	public void ReloadPriceAsFlatPrice()
	{
		if (ContractTypeId != ContractTypeDictionary.HTA && ContractTypeId != ContractTypeDictionary.Basis)
			return;
		CalculateNetBasisValue();
		Price = (FuturesPrice ?? 0) + (NetBasis ?? 0) + FreightPrice + Fees1 + Fees2;
	}

	public decimal SimulatePrice() => (FuturesPrice ?? 0) + (NetBasis ?? 0) + FreightPrice + Fees1 + Fees2;

	public void ChangePushBasis(decimal? pushBasis)
	{
		if (pushBasis == null)
		{
			return;
		}

		PushBasis = pushBasis;
		CalculateNetBasisValue();
	}

	public void ChangePostedBasis(decimal? value)
	{
		if (value == null)
		{
			return;
		}

		PostedBasis = value;
		CalculateNetBasisValue();
	}

	/// <summary>
	/// TODO: Shayne - Shouldn't this also update the <see cref="Price"/> property? If not, why?
	/// </summary>
	public void ChangeFuturesPrice(decimal? futurePrice)
	{
		// Why is this ignoring FlatPrice etc. when it is normally checked in the constructor for all types?
		if (ContractTypeId == ContractTypeDictionary.HTA || ContractTypeId == ContractTypeDictionary.Basis)
		{
			ContractAssertionConcern.ValidateQuarterCent(futurePrice);
		}

		FuturesPrice = futurePrice;
	}

	public void SetFillQuantityAndWeightedAverageFillPrice(decimal fillQuantity, decimal weightedAverageFillPrice)
	{
		ChangeQuantityAndBalance(fillQuantity);
		FuturesPrice = weightedAverageFillPrice;
		CalculatePrice();
	}

	public void ChangeComments(string comments)
	{
		if (comments == null || comments.Equals(Comments, StringComparison.OrdinalIgnoreCase))
			return;
		Comments = comments;
	}

	public void ChangeExpirationDate(DateTime? expirationDate)
	{
		if ((Event == EContractEvent.Create.ToString() || Event == EContractEvent.Edit.ToString() || Event == EContractEvent.Roll.ToString() || Event == EContractEvent.Price.ToString()) && expirationDate.HasValue)
		{
			if (Expiration != expirationDate.Value)
				Expiration = expirationDate.Value;
		}
	}

	public void ChangeQuantityAndBalance(decimal? quantity)
	{
		var avaliableQuantity = AvailableQuantity;
		if (!quantity.HasValue || quantity.Value == avaliableQuantity)
		{
			return;
		}

		var quantityValue = quantity.Value;
		//      AssertionConcern.ArgumentIsBiggerThan(quantityValue, 0, ContractResources.QuantityMustBePositive);
		if (quantityValue > avaliableQuantity) //Adding
		{
			var diference = quantityValue - avaliableQuantity;
			Quantity += diference;
			RemainingBalance += diference;
		}
		else
		{
			var diference = avaliableQuantity - quantityValue;
			Quantity -= diference;
			RemainingBalance -= diference;
			if (RemainingBalance < 0)
			{
				RemainingBalance = 0;
			}
		}
	}

	public void ChangeQuantity(decimal quantity, bool isAddition)
	{
		if (isAddition)
		{
			Quantity += quantity;
		}
		else
		{
			Quantity -= quantity;
		}
	}

	public void ChangeRemainingBalance(decimal quantity, bool isAddition)
	{
		if (isAddition)
		{
			RemainingBalance += quantity;
		}
		else
		{
			if (RemainingBalance > quantity)
			{
				RemainingBalance -= quantity;
			}
			else
			{
				RemainingBalance = 0;
			}
		}
	}

	public void AddRemainingBalance(decimal quantityToAdd)
	{
		AssertionConcern.ArgumentIsBiggerOrEqualThan(quantityToAdd, 0, ContractResources.RemaninigBalanceIsInvalid);
		RemainingBalance += quantityToAdd;
	}

	public void ChangeEmployee(Guid employeeId)
	{
		if (employeeId.Equals(Guid.Empty) || employeeId.Equals(CustomerId))
			return;
		UpdatedEmployeeId = employeeId;
	}

	public void ChangeFees(decimal? fees1, decimal? fees2)
	{
		if (fees1.HasValue && fees1.Value != Fees1)
		{
			Fees1 = fees1.Value;
		}

		if (fees2.HasValue && fees2.Value != Fees2)
		{
			Fees2 = fees2.Value;
		}
	}

	public void ChangeNetBasis(decimal netBasis) => NetBasis = netBasis;

	public void ChangePassFill(bool passFill)
	{
		if (IsOnStatus(EContractState.Canceled))
		{
			throw new OperationNotPermittedException("This contract must have the correct status to do this action.");
		}

		PassFill = passFill;
	}

	public void ChangeErpStatus(ErpStatus status) => ErpStatus = status;

	public void ValidateAndUpdateErpStatus(ErpStatus status, string errorMsg = "", bool success = false)
	{
		ErpStatus = success ? status : ErpStatus.Fail;
		if (ErpStatus == ErpStatus.Fail || ErpStatus == ErpStatus.TimeOut)
			ErpMessage = errorMsg;
		else
			ErpMessage = string.Empty;
	}

	public void ChangePrice(decimal? price)
	{
		if (IsOnStatus(EContractState.Canceled))
		{
			throw new OperationNotPermittedException("This contract must have the correct status to do this action.");
		}

		Price = price;
	}

	public void ChangeTheirContract(string theirContract) => TheirContract = theirContract;

	/// <summary>
	/// TODO: Shayne - Why is the quarter-cent validation not happening here?
	/// </summary>
	public void ChangeFuturesPriceAndReloadPrice(decimal futurePrice)
	{
		if (futurePrice == 0)
			return;
		FuturesPrice = futurePrice;
		if (IsOnStatus(EContractState.Priced))
		{
			Price = (FuturesPrice ?? 0) + (NetBasis ?? 0) + FreightPrice + Fees1 + Fees2;
		}
		else if (PassFill && ContractTypeId == ContractTypeDictionary.HTA)
		{
			Price = Price == null ? 0 : Price;
		}
	}

	public void DefineLastOperationQuantity(decimal quantity)
	{
		LastTransactionQuantity = quantity;
	}

	#endregion

	#region Status operations

	public bool ShouldSendFillErp()
	{
		if (TransactionTypeDictionary.Adjustment == TransactionTypeId)
			return false;

		if (Source == ContractSource.Offer)
			return true;
		if (string.IsNullOrEmpty(Number) || ErpStatus == ErpStatus.Fail || ErpStatus == ErpStatus.NotSent || ErpStatus == ErpStatus.TimeOut)
			return true;

		return true;
	}

	public bool ShouldSendContractErp(EContractEvent? contractEvent = null,  string message = "")
	{
		if (TransactionTypeDictionary.Adjustment == TransactionTypeId)
			return false;

		// Contract.Event is not changed (such as Created), we need the operation
		var eventString = contractEvent == null ? Event : contractEvent.ToString();
		if (eventString is nameof(EContractEvent.Undo) or nameof(EContractEvent.Cancel))
		{
			if (!string.IsNullOrEmpty(message) && ErpStatus is ErpStatus.Pending or ErpStatus.Fail)
				throw new BusinessException(message);
			return ErpStatus == ErpStatus.Success;
		}
		
		if (!PassFill)
			return ShouldSendFillErp();

		return FuturesPrice.HasValue;
	}

	public void StartEdition() => Event = EContractEvent.Edit.ToString();

	public void Cancel(decimal cancelQuantity, bool forceCancelOfGrossRemainingBalance = false)
	{
		Quantity = 0;
		RemainingBalance = 0;
		if (IsAParentBasisOrHTA() && !forceCancelOfGrossRemainingBalance)
		{
			ReduceGrossRemainingBalance(cancelQuantity);
		}
		else
		{
			CancelGrossRemainingBalance();
			AddDomainEvent(new ContractStatusChangedToCanceled(Id, IsSell, cancelQuantity, RealCropYear, CommodityId, ContractTypeId, LocationId));
		}
		Event = EContractEvent.Cancel.ToString();
		Status = Status.Cancel();
	}

	public void Undo(decimal cancelQuantity, bool forceCancelOfGrossRemainingBalance = false)
	{
		Quantity = 0;
		RemainingBalance = 0;
		if (IsAParentBasisOrHTA() && !forceCancelOfGrossRemainingBalance)
		{
			ReduceGrossRemainingBalance(cancelQuantity);
		}
		else
		{
			CancelGrossRemainingBalance();
			AddDomainEvent(new ContractStatusChangedToCanceled(Id, IsSell, cancelQuantity, RealCropYear, CommodityId, ContractTypeId, LocationId));
		}
		Event = EContractEvent.Undo.ToString();
		Status = Status.Cancel();
	}

	public void Open()
	{
		Status = Status.ChangeStatusToOpen();
		Price = 0;
	}

	public void Converted()
	{
		Status = Status.ChangeStatusToConverted();
		Event = EContractEvent.Convert.ToString();
	}

	public void Priced()
	{
		if (ContractTypeId == ContractTypeDictionary.FlatPrice)
		{
			Price = (FuturesPrice ?? 0) + (NetBasis ?? 0) + FreightPrice + Fees1 + Fees2;
		}
		else if (ContractTypeId == ContractTypeDictionary.HTA)
		{
			Price = (FuturesPrice ?? 0) + (NetBasis ?? 0) + FreightPrice + Fees1 + Fees2;
		}
		else
		{
			Price = (FuturesPrice ?? 0) + (NetBasis ?? 0) + FreightPrice + Fees1 + Fees2;
		}
		Status = Status.ChangeStatusToPriced();
	}

	public void Priced(IQueryable<Contract> childs, bool hasWorkingOffers = false, bool isCancel = false)
	{
		Status = Status.ChangeStatusToPriced();
		if (IsAbleToPrice(childs, isCancel))
			Priced();
	}

	public bool IsOnStatus(EContractState status) => Status.Is(status);
	public bool ComesFromEvent(EContractEvent @event) => Event == @event.ToString();

	public bool IsRootContract() => RootParentId == null;

	public bool ComesFromOffer() => OfferId.HasValue;



	public decimal CancellableQuantity()
	{
		if (IsOnStatus(EContractState.Canceled))
		{
			return 0;
		}

		if (ContractTypeId == ContractTypeDictionary.FlatPrice)
		{
			return CancellableQuantityFlatPrice();
		}

		if (ContractTypeId == ContractTypeDictionary.HTA)
		{
			return CancellableQuantityHTA();
		}

		if (ContractTypeId == ContractTypeDictionary.Basis)
		{
			return CancellableQuantityBasis();
		}

		throw new("Contract type is not defined");
	}

	decimal CancellableQuantityHTA() => string.Equals(Event, EContractEvent.Price.ToString(), StringComparison.Ordinal) ? Quantity : RemainingBalance;

	decimal CancellableQuantityBasis() => string.Equals(Event, EContractEvent.Price.ToString(), StringComparison.Ordinal) ? Quantity : RemainingBalance;

	decimal CancellableQuantityFlatPrice() => Quantity;

	public void ChangeCustomer(Guid customerId)
	{
		if (customerId.Equals(Guid.Empty) || customerId.Equals(CustomerId))
			return;
		CustomerId = customerId;
	}

	#endregion

	#region Business Validations

	void SetCommonValues(decimal? fees1, decimal? fees2, decimal? freightPrice)
	{
		Fees1 = fees1 ?? Fees1;
		Fees2 = fees2 ?? Fees2;
		FreightPrice = freightPrice ?? FreightPrice;
	}

	static void ValidatePassFillAndDoNotHedge(bool passFill, bool doNotHedge)
	{
		if (passFill && doNotHedge)
		{
			throw new PassFillDoNotHedgeException(ContractResources.PassFillDoNotHedgeNotBothTrue);
		}
	}

	static void ValidateBasisFields(string futuresMonth, decimal? futuresPrice, decimal? postedBasis,
		decimal? pushBasis, bool passFill, bool doNotHedge, bool cashSettlement)
	{
		AssertionConcern.ArgumentIsNotTrue(doNotHedge, ContractResources.ContractTypeValuesAreWrong);
		AssertionConcern.ArgumentIsNotTrue(cashSettlement, ContractResources.ContractTypeValuesAreWrong);
		ValidateBasisFieldRelatedValues(futuresMonth, futuresPrice, postedBasis, pushBasis);
	}

	static void ValidateBasisFieldRelatedValues(string futuresMonth, decimal? futuresPrice, decimal? postedBasis, decimal? pushBasis)
	{
		ValidateFuturesMonth(futuresMonth);
		AssertionConcern.ArgumentIsNotNull(postedBasis, ContractResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(pushBasis, ContractResources.RequiredValueIsNotPresent);
	}

	static void ValidateHtaFields(string futuresMonth, decimal? futuresPrice, decimal? postedBasis, decimal? pushBasis,
		bool passFill, bool doNotHedge, bool cashSettlement)
	{
		ValidatePassFillAndDoNotHedge(passFill, doNotHedge);
		AssertionConcern.ArgumentIsNotTrue(cashSettlement, ContractResources.ContractTypeValuesAreWrong);
		ValidateHtaFieldRelatedValues(futuresMonth, futuresPrice, postedBasis, pushBasis);
	}

	static void ValidateHtaFieldRelatedValues(string futuresMonth, decimal? futuresPrice, decimal? postedBasis, decimal? pushBasis, string eventName = null)
	{
		ValidateFuturesMonth(futuresMonth);
		AssertionConcern.ArgumentIsNotNull(futuresPrice, ContractResources.RequiredValueIsNotPresent);
		if (!string.IsNullOrEmpty(eventName) && eventName.Equals(EContractEvent.Price.ToString(), StringComparison.Ordinal))
		{
			AssertionConcern.ArgumentIsNotNull(postedBasis, ContractResources.RequiredValueIsNotPresent);
			AssertionConcern.ArgumentIsNotNull(pushBasis, ContractResources.RequiredValueIsNotPresent);
		}
	}

	static void ValidateFlatPriceFields(string futuresMonth, decimal? futuresPrice, decimal? postedBasis,
		decimal? pushBasis, bool passFill, bool doNotHedge)
	{
		ValidatePassFillAndDoNotHedge(passFill, doNotHedge);
		ValidateFlatPriceFieldRelatedValues(futuresMonth, futuresPrice, postedBasis, pushBasis);
	}

	static void ValidateFlatPriceFieldRelatedValues(string futuresMonth, decimal? futuresPrice, decimal? postedBasis, decimal? pushBasis)
	{
		ValidateFuturesMonth(futuresMonth);
		AssertionConcern.ArgumentIsNotNull(futuresPrice, ContractResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(postedBasis, ContractResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(pushBasis, ContractResources.RequiredValueIsNotPresent);
	}

	static void ValidateFieldsPerContractType(Guid contractTypeId, string futuresMonth, decimal? futuresPrice,
		decimal? postedBasis, decimal? pushBasis, bool passFill, bool doNotHedge, bool cashSettlement)
	{
		if (contractTypeId.Equals(ContractTypeDictionary.FlatPrice))
		{
			ValidateFlatPriceFields(futuresMonth, futuresPrice, postedBasis, pushBasis, passFill, doNotHedge);
		}
		else if (contractTypeId.Equals(ContractTypeDictionary.HTA))
		{
			ValidateHtaFields(futuresMonth, futuresPrice, postedBasis, pushBasis, passFill, doNotHedge, cashSettlement);
		}
		else if (contractTypeId.Equals(ContractTypeDictionary.Basis))
		{
			ValidateBasisFields(futuresMonth, futuresPrice, postedBasis, pushBasis, passFill, doNotHedge, cashSettlement);
		}
	}

	static void ValidateFuturesMonth(string futuresMonth)
	{
		AssertionConcern.ArgumentStringNotNullOrEmpty(futuresMonth, ContractResources.InvalidFutureMonth);
		if (futuresMonth.Length > 12)
		{
			throw new InvalidArgumentException(ContractResources.InvalidFutureMonth);
		}
	}

	static void ValidateDeliveryDates(DateTime deliveryStartDate, DateTime deliveryEndDate)
	{
		AssertionConcern.ArgumentDateNotDefault(deliveryStartDate, ContractResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentDateNotDefault(deliveryEndDate, ContractResources.RequiredValueIsNotPresent);
		if (deliveryEndDate.Date < deliveryStartDate.Date)
		{
			throw new InvalidArgumentException("The delivery end date is older than the delivery start date.");
		}
	}

	void CalculateNetBasisValue()
	{
		if (!PushBasis.HasValue && !PostedBasis.HasValue)
			return;

		NetBasis = (PushBasis ?? 0) + (PostedBasis ?? 0);
	}

	void CalculatePrice()
	{
		if (ContractTypeId == ContractTypeDictionary.FlatPrice)
		{
			Price = FuturesPrice.Value + (NetBasis ?? 0) + FreightPrice + Fees1 + Fees2;
		}
	}

	public bool IsAParentBasisOrHTA() =>
		(ChildCount > 0 || ChildOffersCount > 0)
		&& (ContractTypeId == ContractTypeDictionary.Basis || ContractTypeId == ContractTypeDictionary.HTA);

	#endregion

	#region Notifications

	public void CreateLedgerEvent(string eventName, decimal quantity, bool isAffectingBalance)
	{
		if (ContractTypeId == ContractTypeDictionary.NTC)
		{
			return;
		}

		AddDomainEvent(new ContractLedgerNotification(Id, eventName, quantity, isAffectingBalance, IsSell, RealCropYear, FuturesMonth, DeliveryStartDate, Number, RegionId));
	}

	public void CreateLedgerEvent(string eventName, decimal quantity)
	{
		if (ContractTypeId == ContractTypeDictionary.NTC)
		{
			return;
		}

		AddDomainEvent(new ContractLedgerNotification(Id, eventName, quantity, false, IsSell, RealCropYear, FuturesMonth, DeliveryStartDate, Number, RegionId));
	}

	public void CreateLedgerEventOpposite(string eventName, decimal quantity)
	{
		if (ContractTypeId == ContractTypeDictionary.NTC)
		{
			return;
		}

		AddDomainEvent(new ContractLedgerNotification(Id, eventName, quantity, false, !IsSell, RealCropYear, FuturesMonth, DeliveryStartDate, Number, RegionId));
	}

	public void CreateHistoricEvent(EContractEvent eventName, bool quantityChanged = false, decimal? lastTransactionQuantity = null, decimal quantity = 0, string erpNumber = "")
	{
		if (quantity == 0)
		{
			quantity = Quantity;
		}
		if (string.IsNullOrEmpty(erpNumber))
		{
			erpNumber = Number;
		}
		AddDomainEvent(new ContractHistoric(eventName, Id, null, null, quantityChanged, lastTransactionQuantity, quantity, erpNumber));
		if (quantityChanged) ChildCount++;
	}

	public void CreateOfferHistoricEvent(EContractEvent eventName, Guid offerId, bool quantityChanged = false, decimal? lastTransactionQuantity = null, decimal quantity = 0)
	{
		if (quantity == 0)
		{
			quantity = Quantity;
		}

		AddDomainEvent(new ContractHistoric(eventName, Id, null, offerId, quantityChanged, lastTransactionQuantity, quantity, Number));
	}

	public void CreateHistoricEventForParent(EContractEvent eventName, bool quantityChanged = false, decimal? lastTransactionQuantity = null, decimal quantity = 0)
	{
		if (quantity == 0)
		{
			quantity = Quantity;
		}

		AssertionConcern.ArgumentIsNotNull(ParentId, ContractResources.RequiredValueIsNotPresent);
		AddDomainEvent(new ContractHistoric(eventName, Id, ParentId.Value, null, quantityChanged, lastTransactionQuantity, quantity, Number));
		if (quantityChanged) ChildCount++;
	}

	public void CreateBasisSummaryEvent(decimal? cancelQuantity = null)
	{
		if (!ComesFromOffer())
		{
			if ((ContractTypeId == ContractTypeDictionary.HTA) & (Status.Value != EContractState.Priced))
			{
				return;
			}

			var quantity = (decimal)(cancelQuantity.HasValue ? cancelQuantity : Quantity);
			AddDomainEvent(new BasisSummaryChanged(quantity, IsSell, RealCropYear, CommodityId, DeliveryLocationId));
		}
	}

#pragma warning disable CA1309
	bool IsAbleToPrice(IQueryable<Contract> childs, bool IsCancel)
	{
		var allowableNumberOfRows = 1;
		var rows = 0;

		if (childs.Any())
		{
			rows = childs.Count(e => (e.ParentId == Id &&
									  e.Event.Equals(EContractEvent.Price.ToString())) ||
									 e.Event.Equals(EContractEvent.Roll.ToString()) ||
									 e.Event.Equals(EContractEvent.ApplyNameId.ToString()));
		}

		if (RemainingBalance == 0 ||
			rows > allowableNumberOfRows || IsCancel)
		{
			return false;
		}

		return true;
	}
#pragma warning restore CA1309
	
	public void CreateTransactionQuantityEvent()
	{
		AddDomainEvent(new AdjustContractTransactionQuantity(Id));
	}

	public void CreateNotificationEvent(EContractState state) => AddDomainEvent(new ContractNotificationEvent(state, Id));

	#endregion
}
