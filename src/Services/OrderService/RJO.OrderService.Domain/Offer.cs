using JetBrains.Annotations;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.BusinessExceptions;
using RJO.OrderService.Common;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain.Contracts;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.Events;
using RJO.OrderService.Domain.Notifications;
using RJO.OrderService.Domain.Settings;
using System.ComponentModel.DataAnnotations.Schema;

namespace RJO.OrderService.Domain;

public class Offer : AggregateRoot, IAuditable, ITenantable
{
	[UsedImplicitly]
	Offer() { }

	public Offer(Guid transactionTypeId,
		Guid contractTypeId,
		bool isSell,
		bool isDeliveryDatesCustom,
		Guid commodityId,
		Guid locationId,
		Guid deliveryLocationId,
		DateTime deliveryStartDate,
		DateTime deliveryEndDate,
		short cropYear,
		Guid customerId,
		Guid employeeId,
		string futuresMonth,
		decimal? futuresPrice,
		decimal? postedBasis,
		decimal? pushBasis,
		decimal? netBasis,
		decimal freightPrice,
		decimal fees1,
		decimal fees2,
		decimal price,
		decimal quantity,
		string comments,
		bool gtc,
		DateTime? expiration,
		bool cashSettlement,
		Guid? regionId)
	{
		AssertionConcern.ArgumentIsNotEmpty(transactionTypeId, OfferResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotEmpty(contractTypeId, OfferResources.RequiredValueIsNotPresent);
		ValidateFieldsPerContractType(contractTypeId, futuresMonth, futuresPrice, postedBasis, pushBasis, cashSettlement);

		AssertionConcern.ArgumentIsNotEmpty(commodityId, OfferResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotEmpty(locationId, OfferResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotEmpty(deliveryLocationId, OfferResources.RequiredValueIsNotPresent);
		ContractAssertionConcern.CropYearIsValid(cropYear);
		AssertionConcern.ArgumentIsNotEmpty(customerId, OfferResources.RequiredValueIsNotPresent);

		AssertionConcern.ArgumentIsNotEmpty(employeeId, OfferResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(comments, OfferResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsBiggerThan(quantity, 0, OfferResources.QuantityMustBePositive);

		ContractAssertionConcern.ValidateExpirationWithGtc(gtc, expiration);
		ContractAssertionConcern.ValidateQuarterCent(futuresPrice);

		ValidateDeliveryDates(deliveryStartDate, deliveryEndDate);
		IsDeliveryDatesCustom = isDeliveryDatesCustom;
		TransactionTypeId = transactionTypeId;
		ContractTypeId = contractTypeId;
		IsSell = isSell;
		CommodityId = commodityId;
		LocationId = locationId;
		DeliveryLocationId = deliveryLocationId;
		DeliveryStartDate = deliveryStartDate;
		DeliveryEndDate = deliveryEndDate;
		CropYear = cropYear;
		RealCropYear = cropYear;
		CustomerId = customerId;
		EmployeeId = employeeId;
		UpdatedEmployeeId = employeeId;
		FuturesMonth = futuresMonth;
		PostedBasis = postedBasis;
		PushBasis = pushBasis;
		NetBasis = netBasis;
		FreightPrice = freightPrice;
		Fees1 = fees1;
		Fees2 = fees2;
		Quantity = quantity;
		Comments = comments;
		CashSettlement = cashSettlement;
		RemainingBalance = quantity;
		RemainingBalanceOnProcess = 0;
		ChangeRemainingBalanceOnProcess = DateTime.MinValue;
		Status = new(EOfferState.Working);
		Event = ETransactionEvent.Creation;
		InternalStatus = new(EOfferInternalState.Created);
		Gtc = gtc;
		Expiration = expiration;
		FillCount = 0;
		CalculateNetBasisValue();
		if (ContractTypeId == ContractTypeDictionary.FlatPrice)
		{
			Price = price;
			FuturesPrice = Price - ((NetBasis ?? 0) + FreightPrice + Fees1 + Fees2);
		}
		else
		{
			FuturesPrice = futuresPrice;
			Price = 0;
		}

		Id = IdentityGenerator.NewSequentialGuid();
		Number = InternalCode = ContractIdentityGenerator.NewOfferNumber(ContractTypeId, IsSell);
		IsActive = true;
		IsOrphan = false;
		HasRejection = false;
		RegionId = regionId;
		CreateHistoricEvent(ETransactionEvent.Creation);
	}

	public override void CreateEntity(string createdBy)
	{
		base.CreateEntity(createdBy);
		if (Status.Value == EOfferState.Filled)
		{
			UpdatedOn = UpdatedOn ?? CreatedOn;
			UpdatedBy = UpdatedBy ?? CreatedBy;
		}
	}

	public bool IsChangingFuturesPrice(decimal? futuresPrice, decimal? postedBasis, decimal? pushBasis, decimal freightPrice, decimal fees1, decimal fees2, decimal price)
	{
		if (ContractTypeId == ContractTypeDictionary.FlatPrice)
		{
			return postedBasis != PostedBasis || pushBasis != PushBasis || freightPrice != FreightPrice || fees1 != Fees1 || fees2 != Fees2 || price != Price;
		}

		if (ContractTypeId == ContractTypeDictionary.HTA)
		{
			return futuresPrice != FuturesPrice;
		}

		return false;
	}

	public bool CanBeFilled() => !IsOnStatus(EOfferState.Rejected) && !IsOnStatus(EOfferState.Canceled) && !IsOnStatus(EOfferState.Filled);
	public decimal SimulatePrice() => (FuturesPrice ?? 0) + (NetBasis ?? 0) + FreightPrice + Fees1 + Fees2;

	public void ReloadPriceAsFlatPrice()
	{
		if (ContractTypeId == ContractTypeDictionary.FlatPrice) return;
		Price = (FuturesPrice ?? 0) + (NetBasis ?? 0) + FreightPrice + Fees1 + Fees2;
	}

	public void ChangeFuturesPrice(decimal? futurePrice)
	{
		if (ContractTypeId == ContractTypeDictionary.HTA || ContractTypeId == ContractTypeDictionary.FlatPrice)
		{
			ContractAssertionConcern.ValidateQuarterCent(futurePrice);
		}

		if (!futurePrice.HasValue || futurePrice.Value == FuturesPrice)
			return;
		FuturesPrice = futurePrice;
	}

	public void ChangeFees(decimal? fees1, decimal? fees2)
	{
		if (fees1.HasValue && fees1.Value != Fees1)
		{
			Fees1 = fees1.Value;
		}

		if (fees2.HasValue && fees2.Value != Fees2)
		{
			Fees2 = fees2.Value;
		}
	}

	public void SyncBalanceAndForceFill()
	{
		RemainingBalance += RemainingBalanceOnProcess;
		RemainingBalanceOnProcess = 0;
	}

	public void ChangeFreightPrice(decimal? freightPrice)
	{
		if (freightPrice == null)
		{
			return;
		}

		FreightPrice = freightPrice.Value;
	}

	public void ChangeDeliveryLocation(Guid deliveryLocationId)
	{
		if (deliveryLocationId.Equals(Guid.Empty) || deliveryLocationId.Equals(DeliveryLocationId))
			return;
		DeliveryLocationId = deliveryLocationId;
	}

	public string InternalCode { get; private set; }
	public ETransactionEvent Event { get; private set; }
	public OfferState Status { get; private set; }
	public OfferInternalState InternalStatus { get; private set; }
	public Guid TransactionTypeId { get; private set; }
	public Guid ContractTypeId { get; private set; }
	public bool IsSell { get; private set; }
	public bool IsDeliveryDatesCustom { get; private set; }
	public Guid CommodityId { get; private set; }
	public Guid LocationId { get; private set; }
	public Guid DeliveryLocationId { get; private set; }
	public DateTime DeliveryStartDate { get; private set; }
	public DateTime DeliveryEndDate { get; private set; }
	public short CropYear { get; private set; }
	public short RealCropYear { get; private set; }
	public short FillCount { get; private set; }
	public Guid CustomerId { get; private set; }
	public Guid EmployeeId { get; private set; }
	public Guid UpdatedEmployeeId { get; private set; }
	public string FuturesMonth { get; private set; }
	public decimal? FuturesPrice { get; private set; }
	public decimal? PostedBasis { get; private set; }
	public decimal? PushBasis { get; private set; }
	public decimal? NetBasis { get; private set; }
	public decimal FreightPrice { get; private set; }
	public decimal Fees1 { get; private set; }
	public decimal Fees2 { get; private set; }
	public decimal Price { get; private set; }
	public decimal Quantity { get; private set; }
	public string Comments { get; private set; }
	public string Number { get; private set; }
	public bool Gtc { get; private set; }
	public DateTime? Expiration { get; private set; }
	public bool CashSettlement { get; private set; }
	public bool IsInternal { get; private set; }
	public decimal RemainingBalance { get; private set; }
	public DateTime ChangeRemainingBalanceOnProcess { get; private set; }
	public decimal RemainingBalanceOnProcess { get; private set; }
	public Guid? ContractParentId { get; private set; }
	public bool IsOrphan { get; private set; }
	public bool HasRejection { get; private set; }
	public string Instrument { get; private set; }
	public string TheirContract { get; private set; }
	public Guid? RegionId { get; private set; }

	public Region Region { get; set; }
	public Commodity Commodity { get; set; }
	public ContractType ContractType { get; set; }
	public Customer Customer { get; set; }
	public Employee Employee { get; set; }
	public Employee UpdatedEmployee { get; set; }
	public Location DeliveryLocation { get; set; }
	public Location Location { get; set; }
	public ICollection<NotificationGroup> NotificationGroups { get; } = new HashSet<NotificationGroup>();

	[NotMapped]
	public OfferMetadata[] OfferMetadata { get; set; }

	#region Operations

	public void AsignInstrument(string instrument) => Instrument = instrument;

	public void AsignContractParent(Guid contractId)
	{
		AssertionConcern.ArgumentIsNull(ContractParentId, OfferResources.ContractAssigned);
		ContractParentId = contractId;
	}

	public void AssignTheirContract(string theirContract)
	{
		if (string.IsNullOrEmpty(theirContract)) return;
		AssertionConcern.ArgumentIsNotTrue(theirContract.Length > 25, "'Their Contract' field is too large, only 25 characteres or less are allowed");
		TheirContract = theirContract;
	}

	void CalculateNetBasisValue()
	{
		HasRejection = false;
		if (ContractTypeId == ContractTypeDictionary.HTA) return;
		NetBasis = PushBasis.Value + PostedBasis.Value;
	}

	public void ChangePostedBasis(decimal basis)
	{
		PostedBasis = basis;
		NetBasis = PostedBasis + PushBasis;
		HasRejection = false;
	}

	public void ReCalculateFuturesPrice()
	{
		if (ContractTypeId == ContractTypeDictionary.FlatPrice)
		{
			FuturesPrice = Price - ((NetBasis ?? 0) + FreightPrice + Fees1 + Fees2);
		}

		HasRejection = false;
	}

	static void ValidateBasisFields(string futuresMonth, decimal? futuresPrice, decimal? postedBasis, decimal? pushBasis, bool cashSettlement)
	{
		AssertionConcern.ArgumentIsNotTrue(cashSettlement, OfferResources.OfferTypeValuesAreWrong);
		ValidateBasisPriceFieldRelatedValues(futuresMonth, futuresPrice, postedBasis, pushBasis);
	}

	static void ValidateBasisPriceFieldRelatedValues(string futuresMonth, decimal? futuresPrice, decimal? postedBasis, decimal? pushBasis) => ValidateFuturesMonth(futuresMonth);

	public bool CanBeCanceled() =>
		IsOnStatus(EOfferState.Working) || IsOnStatus(EOfferState.PartiallyFilled) || IsOnStatus(EOfferState.Rejected)
		|| (IsOnStatus(EOfferState.Pending) && Event == ETransactionEvent.Creation); 

	public bool CanBeEdited() =>
		IsOnStatus(EOfferState.Working) || IsOnStatus(EOfferState.PartiallyFilled) || (IsOnStatus(EOfferState.Rejected) && ContractTypeId != ContractTypeDictionary.Basis)
		|| (IsOnStatus(EOfferState.Pending) && Event == ETransactionEvent.Creation);

	public bool CanBeBooked() => IsOnStatus(EOfferState.Working) || IsOnStatus(EOfferState.PartiallyFilled);

	static void ValidateHtaFields(string futuresMonth, decimal? futuresPrice, decimal? postedBasis, decimal? pushBasis, bool cashSettlement)
	{
		AssertionConcern.ArgumentIsNotTrue(cashSettlement, OfferResources.OfferTypeValuesAreWrong);
		ValidateHTAPriceFieldRelatedValues(futuresMonth, futuresPrice, postedBasis, pushBasis);
	}

	static void ValidateHTAPriceFieldRelatedValues(string futuresMonth, decimal? futuresPrice, decimal? postedBasis, decimal? pushBasis)
	{
		ValidateFuturesMonth(futuresMonth);
		AssertionConcern.ArgumentIsNotNull(futuresPrice, OfferResources.RequiredValueIsNotPresent);
		ContractAssertionConcern.ValidateQuarterCent(futuresPrice);
	}

	static void ValidateFlatPriceFields(string futuresMonth, decimal? futuresPrice, decimal? postedBasis, decimal? pushBasis) => ValidateFlatPriceFieldRelatedValues(futuresMonth, futuresPrice, postedBasis, pushBasis);

	static void ValidateFlatPriceFieldRelatedValues(string futuresMonth, decimal? futuresPrice, decimal? postedBasis, decimal? pushBasis)
	{
		ValidateFuturesMonth(futuresMonth);
		AssertionConcern.ArgumentIsNotNull(futuresPrice, OfferResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(postedBasis, OfferResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(pushBasis, OfferResources.RequiredValueIsNotPresent);
		ContractAssertionConcern.ValidateQuarterCent(futuresPrice);
	}

	static void ValidateFieldsPerContractType(Guid contractTypeId, string futuresMonth, decimal? futuresPrice,
		decimal? postedBasis, decimal? pushBasis, bool cashSettlement)
	{
		if (contractTypeId.Equals(ContractTypeDictionary.FlatPrice))
		{
			ValidateFlatPriceFields(futuresMonth, futuresPrice, postedBasis, pushBasis);
		}

		if (contractTypeId.Equals(ContractTypeDictionary.HTA))
		{
			ValidateHtaFields(futuresMonth, futuresPrice, postedBasis, pushBasis, cashSettlement);
		}

		if (contractTypeId.Equals(ContractTypeDictionary.Basis))
		{
			ValidateBasisFields(futuresMonth, futuresPrice, postedBasis, pushBasis, cashSettlement);
		}
	}

	static void ValidateFuturesMonth(string futuresMonth)
	{
		AssertionConcern.ArgumentIsNotNull(futuresMonth, OfferResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentStringNotNullOrEmpty(futuresMonth, OfferResources.OfferTypeValuesAreWrong);
		if (futuresMonth.Length > 12)
		{
			throw new InvalidArgumentException(OfferResources.OfferTypeValuesAreWrong);
		}
	}

	static void ValidateDeliveryDates(DateTime deliveryStartDate, DateTime deliveryEndDate)
	{
		AssertionConcern.ArgumentDateNotDefault(deliveryStartDate, OfferResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentDateNotDefault(deliveryEndDate, OfferResources.RequiredValueIsNotPresent);
		if (deliveryEndDate.Date < deliveryStartDate.Date)
		{
			throw new InvalidArgumentException("The delivery end date is older than the delivery start date.");
		}
	}

	public bool IsExpired() => IsOnStatus(EOfferState.Expired);


	public bool IsChildOffer() => ContractParentId.HasValue && ContractParentId != Guid.Empty;

	public void UpdateRemainingBalanceOnProcess(decimal value)
	{
		RemainingBalanceOnProcess = value;
	}

	public void UpdateRemainingBalance(decimal value)
	{
		RemainingBalance = value;
	}

	public void UpdateOfferStatus(OfferState value)
	{
		Status = value;
	}

	public void UpdateOfferEvent(ETransactionEvent value)
	{
		Event = value;
	}

	public void AccumulateQuantity(decimal quantity)
	{
		AssertionConcern.ArgumentIsSmallerOrEqualThan(RemainingBalanceOnProcess + quantity, Quantity, OfferResources.RemaninigBalanceIsInvalid);
		AssertionConcern.ArgumentIsNotTrue(IsInternal, OfferResources.IsLocallyMonitoring);
		AssertionConcern.ArgumentIsNotTrue(IsOnInternalStatus(EOfferInternalState.LocalMonitored), OfferResources.IsLocallyMonitoring);
		AssertionConcern.ArgumentIsNotTrue(IsOnInternalStatus(EOfferInternalState.Closed), OfferResources.CannotBePartiallyFilledOnClose);
		AssertionConcern.ArgumentIsNotTrue(IsOnStatus(EOfferState.Expired), OfferResources.ItsExpired);
		AssertionConcern.ArgumentIsNotTrue(IsOnStatus(EOfferState.Filled), OfferResources.CannotBePartiallyFilledOnFilled);
		AssertionConcern.ArgumentIsBiggerThan(quantity, 0, OfferResources.RemaninigBalanceIsInvalid);
		AssertionConcern.ArgumentIsNotTrue(IsExpired(), OfferResources.ItsExpired);
		RemainingBalanceOnProcess += quantity;
		RemainingBalance -= quantity;
		ChangeRemainingBalanceOnProcess = DateTime.Now;
		HasRejection = false;
	}

	public short PreFill(short marketFillCounts)
	{
		AssertionConcern.ArgumentIsBiggerThan(marketFillCounts, FillCount, "The partially fill message has to be newer than the actual processed");
		var oldFillCount = FillCount;
		FillCount = marketFillCounts;
		return oldFillCount;
	}

	public void AutoFill() => FillCount++;

	/// <summary>
	/// TODO: 3147 - We need to associate the new/existing contract with orphan fill records.
	/// This method should be updated to take in a <see cref="MarketTransaction"/>, removing the <paramref name="quantity"/> parameter.
	/// Then it needs to do the same thing as <see cref="FillFromAccumulation"/> where it calculates the average fill price and
	/// associates the contract with the orphan fill records. Then we can delete the <see cref="FillFromAccumulation"/> method and
	/// update callers to use this instead.
	/// </summary>
	public Contract Fill(decimal quantity, Contract contract = null)
	{
		AssertionConcern.ArgumentIsSmallerOrEqualThan(quantity, RemainingBalance, OfferResources.RemaninigBalanceIsInvalid);
		AssertionConcern.ArgumentIsBiggerThan(RemainingBalance, 0, OfferResources.RemaninigBalanceIsInvalid);
		AssertionConcern.ArgumentIsNotTrue(IsExpired(), OfferResources.ItsExpired);
		if (ContractTypeId.Equals(ContractTypeDictionary.Basis))
		{
			AssertionConcern.ArgumentIsEquals(quantity, RemainingBalance, OfferResources.NotPartialFillAllowed);
		}

		RemainingBalance -= quantity;
		if (contract == null)
		{
			contract = new(TransactionTypeId, ContractTypeId, IsSell, CommodityId, LocationId, DeliveryLocationId, IsDeliveryDatesCustom,
				DeliveryStartDate, DeliveryEndDate < DateTime.Now.Date ? DateTime.Now.Date : DeliveryEndDate, RealCropYear, CustomerId, EmployeeId, FuturesMonth, FuturesPrice, PostedBasis, PushBasis, FreightPrice,
				Fees1, Fees2, quantity, Comments, false, false, CashSettlement, Expiration, ContractSource.Offer, RegionId);
			contract.ChangeTheirContract(TheirContract);
		}

		contract.AssignOffer(Id);
		if (RemainingBalance == 0)
		{
			RemainingBalanceOnProcess = 0;
			if (InternalStatus.Is(EOfferInternalState.Booked))
			{
				Status = Status.ChangeStatusToFilledByBook();
			}
			else
			{
				Status = Status.ChangeStatusToFilled();
			}

			if (ContractTypeId == ContractTypeDictionary.HTA)
			{
				Price = FuturesPrice.Value + FreightPrice + Fees1 + Fees2;
			}
			else if (ContractTypeId == ContractTypeDictionary.Basis)
			{
				Price = NetBasis.Value + FreightPrice + Fees1 + Fees2;
			}

			CreateHistoricEvent(ETransactionEvent.Filled, quantity);
			CreateNotificationEvent(EOfferState.Filled);
			if (ContractTypeId != ContractTypeDictionary.HTA)
			{
				CreateBasisSummaryEvent();
			}
		}
		else
		{
			CreateHistoricEvent(ETransactionEvent.PartiallyFilled, quantity);
		}
		HasRejection = false;
		return contract;
	}

	public Contract FillFromAccumulation(Commodity commodity, MarketTransaction marketTransaction)
	{
		AssertionConcern.ArgumentIsBiggerThan(RemainingBalanceOnProcess, 0, OfferResources.RemaninigBalanceIsInvalid);
		AssertionConcern.ArgumentIsNotTrue(IsExpired(), OfferResources.ItsExpired);
		AssertionConcern.ArgumentIsNotTrue(ContractTypeId.Equals(ContractTypeDictionary.Basis), OfferResources.NotPartialFillAllowed);
		ChangeRemainingBalanceOnProcess = DateTime.Now;
		if (RemainingBalance < commodity.LotFactor && !IsInternal)
		{
			RemainingBalanceOnProcess += RemainingBalance;
			RemainingBalance = 0;
		}

		var fillsForContract = marketTransaction.OrderFills.Where(x => !x.ContractId.HasValue).ToList();
		var fillAmount = fillsForContract.Sum(x => x.Amount);
		var weightedAverageFillPrice = fillsForContract.Sum(x => x.Price * x.Amount) / fillAmount;
		var fillQuantity = fillAmount * commodity.LotFactor;

		var contract = new Contract(TransactionTypeId, ContractTypeId, IsSell, CommodityId, LocationId, DeliveryLocationId, IsDeliveryDatesCustom, DeliveryStartDate,
			DeliveryEndDate < DateTime.Now.Date ? DateTime.Now.Date : DeliveryEndDate, RealCropYear, CustomerId, EmployeeId, FuturesMonth, FuturesPrice,
			PostedBasis, PushBasis, FreightPrice, Fees1, Fees2, RemainingBalanceOnProcess, Comments, false, false, CashSettlement, Expiration, ContractSource.Accumulation, RegionId);
		contract.ChangeTheirContract(TheirContract);

		foreach (var orderFill in fillsForContract)
			orderFill.ContractId = contract.Id;

		contract.AssignOffer(Id);
		if (RemainingBalance == 0)
		{
			Status = Status.ChangeStatusToFilled();
			if (ContractTypeId == ContractTypeDictionary.HTA)
			{
				Price = FuturesPrice.Value + FreightPrice + Fees1 + Fees2;
			}
			else if (ContractTypeId == ContractTypeDictionary.Basis)
			{
				Price = NetBasis.Value + FreightPrice + Fees1 + Fees2;
			}

			CreateHistoricEvent(ETransactionEvent.Filled, RemainingBalanceOnProcess);
			CreateNotificationEvent(EOfferState.Filled);
		}
		else
		{
			CreateHistoricEvent(ETransactionEvent.PartiallyFilled, RemainingBalanceOnProcess);
		}

		RemainingBalanceOnProcess = 0;
		HasRejection = false;
		return contract;
	}

	public void OmitEdition()
	{
		AssertionConcern.ArgumentIsTrue(IsOnStatus(EOfferState.Pending) && (Event == ETransactionEvent.Edition || Event == ETransactionEvent.Creation), "We can not process this transaction because is in incorrect status");
		Event = ETransactionEvent.Creation;
	}

	#endregion

	#region Internal Status

	public void CloseInternally() => InternalStatus = InternalStatus.Close();

	public void SentToMarket() => InternalStatus = InternalStatus.ChangeStatusToSentToMarket();

	public void LocalMonitored() => InternalStatus = InternalStatus.ChangeStatusToLocalMonitored();

	public bool IsOnInternalStatus(EOfferInternalState status) => InternalStatus.Is(status);

	#endregion

	#region Status

	public void PartiallyFill()
	{
		AssertionConcern.ArgumentIsNotTrue(IsExpired(), OfferResources.ItsExpired);
		AssertionConcern.ArgumentIsNotTrue(IsOnStatus(EOfferState.Expired), OfferResources.ItsExpired);
		AssertionConcern.ArgumentIsNotTrue(IsOnStatus(EOfferState.Filled), OfferResources.CannotBePartiallyFilled);
		if (ContractTypeId != ContractTypeDictionary.FlatPrice)
		{
			Price = 0;
		}

		Status = Status.ChangeStatusToPartiallyFilled();
		HasRejection = false;
	}

	public void Reject()
	{
		Status = Status.ChangeStatusToRejected();
		InternalStatus = InternalStatus.Close();
		if (ContractTypeId != ContractTypeDictionary.FlatPrice)
		{
			Price = 0;
		}

		HasRejection = false;
		IsOrphan = false;

		CreateNotificationEvent(EOfferState.Rejected);
	}

	public void Expire()
	{
		Status = Status.ChangeStatusToExpired();
		InternalStatus = InternalStatus.Close();
		if (ContractTypeId != ContractTypeDictionary.FlatPrice)
		{
			Price = 0;
		}

		HasRejection = false;
		IsOrphan = false;

		CreateNotificationEvent(EOfferState.Expired);
	}

	public void ReportRejection() => HasRejection = true;

	public bool IsOnStatus(EOfferState status) => Status.Is(status);

	public void StartCancelation()
	{
		Status = Status.StartCancelation();
		Event = ETransactionEvent.Cancelation;
		HasRejection = false;
	}

	public void StartEdition()
	{
		if (Event != ETransactionEvent.Creation || (Status.Value == EOfferState.Working && InternalStatus.Value != EOfferInternalState.Created))
		{
            Event = ETransactionEvent.Edition;
		}
		Status = Status.StartEdition();		
		HasRejection = false;
	}

	public void StartCreation(bool force = false)
	{
		Status = Status.StartCreation(force);
		Event = ETransactionEvent.Creation;
		InternalStatus = new(EOfferInternalState.Created);
		HasRejection = false;
	}

	public void StartRejectEdition()
	{
		AssertionConcern.ArgumentIsTrue(IsOnStatus(EOfferState.Rejected), OfferResources.OfferMustHaveCorrectStatus);
		Status = new(EOfferState.Pending);
		InternalStatus = InternalStatus.ChangeStatusToCreated();
		Event = ETransactionEvent.Creation;
		HasRejection = false;
	}

	public void FillOnCancel()
	{
		AssertionConcern.ArgumentIsTrue(IsOnStatus(EOfferState.PartiallyFilled), OfferResources.OfferMustHaveCorrectStatus);
		Status = Status.ChangeStatusToFilled();
		HasRejection = false;

		CreateNotificationEvent(EOfferState.Filled);
	}

	public void CompleteCancelation()
	{
		if (Status.Value != EOfferState.Rejected)
		{
			InternalStatus = InternalStatus.Close();
		}

		Status = Status.CompleteCancelation(this);
		if (ContractTypeId != ContractTypeDictionary.FlatPrice)
		{
			Price = 0;
		}

		HasRejection = false;
		IsOrphan = false;

		CreateNotificationEvent(EOfferState.Canceled);
	}

	public void CompleteEdition()
	{
		Status = Status.CompleteEdition(RemainingBalance, Quantity);
		HasRejection = false;
	}

	public void CompleteOffer()
	{
		Status = Status.CompleteNew();
		HasRejection = false;
	}

	public void BookInternally()
	{
		InternalStatus = InternalStatus.ChangeStatusToBooked();
		HasRejection = false;
		IsOrphan = false;
	}

	public void Complete()
	{
		Status = Status.ChangeStatusToCompleted();
		CreateNotificationEvent(EOfferState.Completed);
	}

	#endregion

	#region Changes

	public void MakePrivate() => IsInternal = true;

	public void MakePublic() => IsInternal = false;

	#endregion

	#region Business Validations

	void SetCommonValues(decimal? fees1, decimal? fees2, decimal? freightPrice)
	{
		if (ContractTypeId != ContractTypeDictionary.Basis)
		{
			Fees1 = fees1 ?? Fees1;
			Fees2 = fees2 ?? Fees2;
		}

		FreightPrice = freightPrice ?? FreightPrice;
		HasRejection = false;
	}

	#endregion

	#region Change Properties

	public void ChangeAndVerifyPrice(string futuresMonth,
		decimal? futuresPrice,
		decimal? postedBasis,
		decimal? pushBasis,
		decimal? freightPrice,
		decimal? fees1,
		decimal? fees2,
		decimal? price)
	{
		SetCommonValues(fees1, fees2, freightPrice);
		if (ContractTypeId == ContractTypeDictionary.FlatPrice)
		{
			ValidateFlatPriceFieldRelatedValues(FuturesMonth, FuturesPrice, postedBasis, pushBasis);
			PostedBasis = postedBasis;
			PushBasis = pushBasis;
			Price = price ?? Price;
			CalculateNetBasisValue();
			FuturesPrice = Price - ((NetBasis ?? 0) + FreightPrice + Fees1 + Fees2);
		}
		else if (ContractTypeId == ContractTypeDictionary.HTA)
		{
			ValidateHTAPriceFieldRelatedValues(futuresMonth, futuresPrice, PostedBasis, PushBasis);
			FuturesMonth = futuresMonth;
			FuturesPrice = futuresPrice;
		}
		else if (ContractTypeId == ContractTypeDictionary.Basis)
		{
			ValidateBasisPriceFieldRelatedValues(FuturesMonth, FuturesPrice, postedBasis, pushBasis);
			PostedBasis = postedBasis;
			PushBasis = pushBasis;
			CalculateNetBasisValue();
		}

		HasRejection = false;
	}

	public void ReCalculatePrice()
	{
		Price = (FuturesPrice ?? 0) + (NetBasis ?? 0) + FreightPrice + Fees1 + Fees2;
		HasRejection = false;
	}

	public void ChangeEmployee(Guid employeeId)
	{
		if (employeeId.Equals(Guid.Empty) || employeeId.Equals(CustomerId))
			return;
		UpdatedEmployeeId = employeeId;
		HasRejection = false;
	}

	public void ChangeComments(string comments)
	{
		if (comments == null || comments.Equals(Comments, StringComparison.OrdinalIgnoreCase))
			return;
		Comments = comments;
		HasRejection = false;
	}

	public void ChangePushBasis(decimal? pushBasis)
	{
		HasRejection = false;
		if (!pushBasis.HasValue || PushBasis.Equals(pushBasis))
			return;
		PushBasis = pushBasis.Value;
	}

	public void ChangeDeliveryDates(DateTime deliveryStartDate, DateTime deliveryEndDate, bool isDeliveryDatesCustom = false)
	{
		ValidateDeliveryDates(deliveryStartDate, deliveryEndDate);
		DeliveryStartDate = deliveryStartDate;
		DeliveryEndDate = deliveryEndDate;
		IsDeliveryDatesCustom = isDeliveryDatesCustom;
		HasRejection = false;
	}

	public void ChangeCropYear(short cropYear)
	{
		ContractAssertionConcern.CropYearIsValid(cropYear);
		CropYear = cropYear;
		RealCropYear = cropYear;
		HasRejection = false;
	}

	public void ChangeFuturesMonth(string futuresMonth)
	{
		AssertionConcern.ArgumentIsNotEmpty(futuresMonth, OfferResources.RequiredValueIsNotPresent);
		FuturesMonth = futuresMonth;
		HasRejection = false;
	}

	public void MarkAsOrphan()
	{
		IsOrphan = true;
		HasRejection = false;
	}

	public void MarkAsNotOrphan()
	{
		IsOrphan = false;
		HasRejection = false;
	}

	public bool CanBeSentOrphanToMarket(string futuresMonth, decimal? basis)
	{
		if (IsOrphan)
		{
			return !FuturesMonth.Equals(futuresMonth, StringComparison.Ordinal) || !PostedBasis.Equals(basis);
		}

		return false;
	}

	public void ChangeLocation(Guid locationId)
	{
		if (locationId.Equals(Guid.Empty) || locationId.Equals(LocationId))
			return;
		LocationId = locationId;
	}

	public void ChangeTheirContract(string theirContract) => TheirContract = theirContract;

	public void ChangeQuantity(decimal qty)
	{
		AssertionConcern.ArgumentIsBiggerThan(qty, 0, OfferResources.QuantityMustBePositive);
		Quantity = qty;
	}

	public void ChangeRemainingBalance(decimal remainingBalance)
	{
		AssertionConcern.ArgumentIsBiggerOrEqualThan(RemainingBalance, 0, OfferResources.RemaninigBalanceIsInvalid);
		RemainingBalance = remainingBalance;
	}

	public void AddQuantity(decimal qty)
	{
		AssertionConcern.ArgumentIsBiggerThan(qty, 0, OfferResources.QuantityMustBePositive);
		Quantity += qty;
		RemainingBalance += qty;
	}

	public void ReduceQuantity(decimal qty)
	{
		AssertionConcern.ArgumentIsBiggerThan(Quantity - qty, 0, OfferResources.QuantityMustBePositive);
		AssertionConcern.ArgumentIsBiggerOrEqualThan(RemainingBalance - qty, 0, OfferResources.RemaninigBalanceIsInvalid);
		Quantity -= qty;
		RemainingBalance -= qty;
	}

	#endregion

	public MarketTransaction CreateLimitOrder(Commodity commodity, string cqgInstrument, int accountId)
	{
		HasRejection = false;
		Instrument = cqgInstrument + FuturesMonth;
		return new(InternalCode, RemainingBalance, commodity.GetLots(RemainingBalance), commodity.Id, commodity.Name, Instrument, FuturesMonth, FuturesPrice ?? 0,
			(NetBasis ?? 0) + (FuturesPrice ?? 0) + FreightPrice + Fees1 + Fees2, ContractParentId, Id, !IsSell, 0, Gtc, Expiration, EMarketTransactionType.Limit,
			EMarketTransactionSource.Offer, RealCropYear, accountId, false, shouldNotify: true);
	}

	public void CreateHistoricEvent(ETransactionEvent eventName, decimal quantity) => AddDomainEvent(new OfferHistoric(eventName, Id, quantity));

	public void CreateHistoricEvent(ETransactionEvent eventName) => AddDomainEvent(new OfferHistoric(eventName, Id, Quantity));

	public void CreateBasisSummaryEvent() => AddDomainEvent(new BasisSummaryChanged(Quantity, IsSell, RealCropYear, CommodityId, LocationId));

	public void CreateNotificationEvent(EOfferState state, decimal filledQuantity = 0) => AddDomainEvent(new OfferNotificationEvent(state, Id, filledQuantity));

	public void ChangeRegionId(Guid regionId) => RegionId = regionId;

	public void NotifyForTimeBarSubscription() => AddDomainEvent(new CreateTimeBarRequest(CommodityId, FuturesMonth));
}
