using RJO.OrderService.Domain.Contracts;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.MultiTenancyServer;

namespace RJO.OrderService.Domain.Events;

public class ContractHistoric : IEventPre, IBackgroundTask
{
	public ContractHistoric(EContractEvent eventName, Guid contractId, Guid? contractRealId, Guid? offerId, bool quantityChanged, decimal? lastTransactionQuantity, decimal quantity, string erpNumber)
	{
		Event = eventName;
		ContractId = contractId;
		ContractRealId = contractRealId;
		QuantityChanged = quantityChanged;
		OfferId = offerId;
		LastTransactionQuantity = lastTransactionQuantity;
		Quantity = quantity;
		ErpNumber = erpNumber;
	}

	public EContractEvent Event { get; private set; }
	public Guid ContractId { get; private set; }
	public Guid? ContractRealId { get; private set; }
	public Guid? OfferId { get; private set; }
	public bool QuantityChanged { get; private set; }
	public decimal? LastTransactionQuantity { get; private set; }
	public decimal Quantity { get; private set; }
	public string ErpNumber { get; private set; }

	public ApplicationTenant ApplicationTenant { get; set; }
	
	public Guid Id => ContractRealId ?? ContractId;
}
