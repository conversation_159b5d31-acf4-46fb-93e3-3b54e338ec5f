using RJO.OrderService.Domain.Contracts;
using RJO.OrderService.Domain.MultiTenancyServer;

namespace RJO.OrderService.Domain.Events;

public class ProcessSpreadTransaction : IEventPos, IBackgroundTask
{
	public ProcessSpreadTransaction(Guid transactionId) => TransactionId = transactionId;
	
	public Guid TransactionId { get; private set; }

	public short Attempts { get; set; }

	public ApplicationTenant ApplicationTenant { get; set; }
}
