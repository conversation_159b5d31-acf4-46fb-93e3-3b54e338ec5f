using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.Resources;

namespace RJO.OrderService.Domain;

public class BucketBalanceContract : AggregateRoot, ITenantable
{
	public BucketBalanceContract() { }

	public BucketBalanceContract(Guid contractId, Guid bucketBalanceId)
	{
		ContractId = contractId;
		BucketBalanceId = bucketBalanceId;
		MarketTransactionId = Guid.Empty;

		Id = IdentityGenerator.NewSequentialGuid();
		IsActive = true;
	}
	
	public decimal Balance { get; private set; }
	
	public Guid? MarketTransactionId { get; private set; }

	public Guid BucketBalanceId { get; private set; }
	public BucketBalance BucketBalance { get; set; }
	
	public Guid ContractId { get; private set; }
	public Contract Contract { get; set; }
	
	public override string ToString() => $"Balance:{Balance} - ContractId:{ContractId} - BucketBalanceId:{BucketBalanceId}";

	public void UpdateMarketTransaction(Guid marketTransactionId)
	{
		AssertionConcern.ArgumentIsNotEmpty(marketTransactionId, GeneralResources.RequiredValueIsNotPresent);

		MarketTransactionId = marketTransactionId;
	}

	public void UpdateBalance(decimal balance, bool isSell) => Balance = isSell ? balance * -1 : balance;
	public void SetBalance(decimal balance) => Balance = balance;
}
