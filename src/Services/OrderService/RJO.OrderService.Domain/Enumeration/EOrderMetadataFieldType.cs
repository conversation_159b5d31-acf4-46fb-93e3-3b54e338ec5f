namespace RJO.OrderService.Domain.Enumeration;

public enum EOrderMetadataFieldType
{
	///<summary>
	///Alpha characters only set to &lt;= 25 characters
	///</summary>
	Text,

	/// <summary>
	/// Accommodates numbers with up to 4 decimal places up to &lt;= 25 characters
	/// </summary>
	Numeric,

	/// <summary>
	/// Accommodate both alpha and numeric characters up to &lt;= 25 character
	/// </summary>
	Alphanumeric,

	/// <summary>
	/// Accommodate mm/dd/yyyy via a date picker
	/// </summary>
	Date,

	/// <summary>
	/// Accommodate a yes or no
	/// </summary>
	TrueFalse,

	/// <summary>
	/// Accommodate up to 7 selections with a combination of &lt;=25 characters
	/// </summary>
	MultiSelect,

	/// <summary>
	/// 
	/// </summary>
	Undefined
}
