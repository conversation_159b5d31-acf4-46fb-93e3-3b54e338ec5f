using RJO.BuildingBlocks.CustomExceptions.BusinessExceptions;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain.Enumeration;

namespace RJO.OrderService.Domain;

public class OfferState : ValueObject<OfferState>
{
	public EOfferState Value { get; private set; }
	OfferState() { }
	public OfferState(EOfferState status) => Value = status;
	protected override bool EqualsCore(OfferState other) => other != null && other.Value == Value;

	protected override int GetHashCodeCore() => (int)Value;

	public override string ToString() => Enum.GetName(typeof(EOfferState), Value);

	public OfferState ChangeStatusToExpired() => new(EOfferState.Expired);

	public OfferState ChangeStatusToFilled()
	{
		if (Value != EOfferState.PartiallyFilled && Value != EOfferState.Working && Value != EOfferState.Pending)
		{
			throw new OperationNotPermittedException(OfferResources.OfferMustHaveCorrectStatus);
		}

		return new(EOfferState.Filled);
	}

	public OfferState ChangeStatusToFilledByBook()
	{
		if (Value != EOfferState.PartiallyFilled && Value != EOfferState.Working && Value != EOfferState.Pending)
		{
			throw new OperationNotPermittedException(OfferResources.OfferMustHaveCorrectStatus);
		}

		return new(EOfferState.Filled);
	}

	public OfferState ChangeStatusToPartiallyFilled()
	{
		if (Value != EOfferState.Working && Value != EOfferState.PartiallyFilled && Value != EOfferState.Pending)
		{
			throw new OperationNotPermittedException(OfferResources.OfferMustHaveCorrectStatus);
		}

		return new(EOfferState.PartiallyFilled);
	}

	public OfferState ChangeStatusToRejected()
	{
		if (Value != EOfferState.PartiallyFilled && Value != EOfferState.Working)
		{
			throw new OperationNotPermittedException(OfferResources.OfferMustHaveCorrectStatus);
		}

		return new(EOfferState.Rejected);
	}

	public OfferState ChangeStatusToCompleted()
	{
		if (Value != EOfferState.Filled)
		{
			throw new OperationNotPermittedException(OfferResources.OfferMustHaveCorrectStatus);
		}

		return new(EOfferState.Completed);
	}

	public OfferState StartCancelation()
	{
		if (Value != EOfferState.PartiallyFilled && Value != EOfferState.Working && Value != EOfferState.Pending)
		{
			throw new OperationNotPermittedException(OfferResources.OfferMustHaveCorrectStatus);
		}

		return new(EOfferState.Pending);
	}

	public OfferState StartEdition()
	{
		if (Value != EOfferState.PartiallyFilled && Value != EOfferState.Working && Value != EOfferState.Pending)
		{
			throw new OperationNotPermittedException(OfferResources.OfferMustHaveCorrectStatus);
		}

		return new(EOfferState.Pending);
	}

	public OfferState StartCreation(bool force)
	{
		if (Value != EOfferState.Working && Value != EOfferState.PartiallyFilled && !force)
		{
			throw new OperationNotPermittedException(OfferResources.OfferMustHaveCorrectStatus);
		}

		return new(EOfferState.Pending);
	}

	public OfferState CompleteCancelation(Offer offer)
	{
		if (Value != EOfferState.Pending && Value != EOfferState.Working && Value != EOfferState.Rejected && Value != EOfferState.PartiallyFilled)
		{
			throw new OperationNotPermittedException(OfferResources.OfferMustHaveCorrectStatus);
		}

		if (Value is EOfferState.Pending && offer.Event == ETransactionEvent.Cancelation && offer.FillCount > 0)
			return new(EOfferState.Filled);

		return new(EOfferState.Canceled);
	}

	public OfferState CompleteEdition(decimal remainingBalance, decimal quantity)
	{
		if (Value != EOfferState.Pending)
		{
			throw new OperationNotPermittedException(OfferResources.OfferMustHaveCorrectStatus);
		}

		return new(remainingBalance == quantity ? EOfferState.Working : EOfferState.PartiallyFilled);
	}

	public OfferState CompleteNew()
	{
		if (Value != EOfferState.Pending)
		{
			throw new OperationNotPermittedException(OfferResources.OfferMustHaveCorrectStatus);
		}

		return new(EOfferState.Working);
	}

	public bool Is(EOfferState status) => Value == status;
}
