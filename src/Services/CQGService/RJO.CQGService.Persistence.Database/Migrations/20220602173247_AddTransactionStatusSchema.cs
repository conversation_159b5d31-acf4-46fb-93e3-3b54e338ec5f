#nullable disable

using Microsoft.EntityFrameworkCore.Migrations;

namespace RJO.CQGService.Persistence.Database.Migrations
{
    public partial class AddTransactionStatusSchema : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "AccountId",
                schema: "CQG",
                table: "TransactionStatus",
                newName: "TextMessage");

            migrationBuilder.AddColumn<string>(
                name: "ChainOrderId",
                schema: "CQG",
                table: "TransactionStatus",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClOrderId",
                schema: "CQG",
                table: "TransactionStatus",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "Duration",
                schema: "CQG",
                table: "TransactionStatus",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ExecOrderId",
                schema: "CQG",
                table: "TransactionStatus",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "FillQty",
                schema: "CQG",
                table: "TransactionStatus",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OrderId",
                schema: "CQG",
                table: "TransactionStatus",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "OrderType",
                schema: "CQG",
                table: "TransactionStatus",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OrigClOrderId",
                schema: "CQG",
                table: "TransactionStatus",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "PrevOrderType",
                schema: "CQG",
                table: "TransactionStatus",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RouteClOrderId",
                schema: "CQG",
                table: "TransactionStatus",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "ScaledFillPrice",
                schema: "CQG",
                table: "TransactionStatus",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "ScaledLimitPrice",
                schema: "CQG",
                table: "TransactionStatus",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "ScaledPrevLimitPrice",
                schema: "CQG",
                table: "TransactionStatus",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "Status",
                schema: "CQG",
                table: "TransactionStatus",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<decimal>(
                name: "TransId",
                schema: "CQG",
                table: "TransactionStatus",
                type: "decimal(20,0)",
                nullable: false,
                defaultValue: 0m);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ChainOrderId",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "ClOrderId",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "Duration",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "ExecOrderId",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "FillQty",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "OrderId",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "OrderType",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "OrigClOrderId",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "PrevOrderType",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "RouteClOrderId",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "ScaledFillPrice",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "ScaledLimitPrice",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "ScaledPrevLimitPrice",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "Status",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.DropColumn(
                name: "TransId",
                schema: "CQG",
                table: "TransactionStatus");

            migrationBuilder.RenameColumn(
                name: "TextMessage",
                schema: "CQG",
                table: "TransactionStatus",
                newName: "AccountId");
        }
    }
}
