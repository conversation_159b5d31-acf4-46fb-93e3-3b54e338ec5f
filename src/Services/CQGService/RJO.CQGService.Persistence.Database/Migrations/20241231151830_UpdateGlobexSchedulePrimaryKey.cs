using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RJO.CQGService.Persistence.Database.Migrations
{
    /// <inheritdoc />
    public partial class UpdateGlobexSchedulePrimaryKey : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_GlobexSchedule",
                schema: "Audit",
                table: "GlobexSchedule");

            migrationBuilder.AddPrimaryKey(
                name: "PK_GlobexSchedule",
                schema: "Audit",
                table: "GlobexSchedule",
                column: "AuditId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_GlobexSchedule",
                schema: "Audit",
                table: "GlobexSchedule");

            migrationBuilder.AddPrimaryKey(
                name: "PK_GlobexSchedule",
                schema: "Audit",
                table: "GlobexSchedule",
                column: "Id");
        }
    }
}
