// <auto-generated />

#nullable disable

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;

namespace RJO.CQGService.Persistence.Database.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20220831091608_FillPriceCorrectMigration")]
    partial class FillPriceCorrectMigration
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.3")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("RJO.CQGService.Persistence.Database.Domain.CQGOrderStatus", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("AvgFillPriceCorrect")
                        .HasColumnType("float");

                    b.Property<string>("ChainOrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClOrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<long>("Duration")
                        .HasColumnType("bigint");

                    b.Property<string>("ExecOrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("FillCnt")
                        .HasColumnType("bigint");

                    b.Property<double>("FillPriceCorrect")
                        .HasColumnType("float");

                    b.Property<long>("FillQty")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsSnapshot")
                        .HasColumnType("bit");

                    b.Property<string>("OrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("OrderQty")
                        .HasColumnType("bigint");

                    b.Property<long>("OrderType")
                        .HasColumnType("bigint");

                    b.Property<string>("RejectMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("RemainingQty")
                        .HasColumnType("bigint");

                    b.Property<string>("RouteClOrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("ScaledAvgFillPrice")
                        .HasColumnType("bigint");

                    b.Property<long>("Side")
                        .HasColumnType("bigint");

                    b.Property<long>("Status")
                        .HasColumnType("bigint");

                    b.Property<string>("Symbol")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("OrderStatus", "CQG");
                });

            modelBuilder.Entity("RJO.CQGService.Persistence.Database.Domain.CQGTransactionStatus", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ChainOrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClOrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<long?>("Duration")
                        .HasColumnType("bigint");

                    b.Property<string>("ExecOrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("FillQty")
                        .HasColumnType("bigint");

                    b.Property<string>("OrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("OrderType")
                        .HasColumnType("bigint");

                    b.Property<string>("OrigClOrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("PrevOrderQty")
                        .HasColumnType("bigint");

                    b.Property<long?>("PrevOrderType")
                        .HasColumnType("bigint");

                    b.Property<string>("RouteClOrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("ScaledFillPrice")
                        .HasColumnType("bigint");

                    b.Property<long?>("ScaledLimitPrice")
                        .HasColumnType("bigint");

                    b.Property<long?>("ScaledPrevFillPrice")
                        .HasColumnType("bigint");

                    b.Property<long?>("ScaledPrevLimitPrice")
                        .HasColumnType("bigint");

                    b.Property<long>("Status")
                        .HasColumnType("bigint");

                    b.Property<string>("TextMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("TransId")
                        .HasColumnType("decimal(20,0)");

                    b.HasKey("Id");

                    b.ToTable("TransactionStatus", "CQG");
                });

            modelBuilder.Entity("RJO.CQGService.Persistence.Database.Domain.FuturesPriceSnapshot", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("Ask")
                        .HasColumnType("float");

                    b.Property<double?>("BestAsk")
                        .HasColumnType("float");

                    b.Property<double?>("BestBid")
                        .HasColumnType("float");

                    b.Property<double?>("Bid")
                        .HasColumnType("float");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<double?>("FuturesPrice")
                        .HasColumnType("float");

                    b.Property<string>("Instrument")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<double?>("Settlement")
                        .HasColumnType("float");

                    b.Property<DateTime?>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("FuturesPriceSnapshot", "CQG");
                });

            modelBuilder.Entity("RJO.CQGService.Persistence.Database.Domain.HighAndLowSnapshot", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("BarTime")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("Closed")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("High")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Instrument")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsMarketOpen")
                        .HasColumnType("bit");

                    b.Property<decimal?>("Low")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Open")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Settlement")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("TradeDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ValidFromDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ValidToDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("HighAndLowSnapshot", "CQG");
                });

            modelBuilder.Entity("RJO.CQGService.Persistence.Database.Domain.InstrumentView", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FutureMonths")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Instrument")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LimitOrderPrefix")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("NumberOfCropYears")
                        .HasColumnType("tinyint");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("InstrumentView", "CQG");
                });

            modelBuilder.Entity("RJO.CQGService.Persistence.Database.Domain.LocalMonitoredSymbols", b =>
                {
                    b.Property<string>("FullSymbol")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("LocalMonitoredSymbols", "CQG");
                });

            modelBuilder.Entity("RJO.CQGService.Persistence.Database.Domain.TenantView", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CanonicalName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NormalizedCanonicalName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("TenantView", "CQG");
                });
#pragma warning restore 612, 618
        }
    }
}
