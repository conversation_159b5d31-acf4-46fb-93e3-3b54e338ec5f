CREATE TRIGGER [CQG].[trg_GlobexScheduleDelete]
   ON [CQG].[GlobexSchedule]
   AFTER DELETE
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @action AS VARCHAR(6);
    SET @action = 'Delete'; -- Action for delete operations

    INSERT INTO [Audit].[GlobexSchedule]
       (AuditId, Id, SecurityGroup, TradeDate, StartTime, TradingStatus, SessionName, CreatedOn, AuditAction, AuditTime)
    SELECT
        NEWID(), Id, SecurityGroup, TradeDate, StartTime, TradingStatus, SessionName, CreatedOn, @action, GETDATE()
    FROM DELETED;
END