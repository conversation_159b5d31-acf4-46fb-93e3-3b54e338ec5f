using Microsoft.AspNetCore.Mvc;
using RJO.CQGService.Common.RequestResponse;

namespace RJO.CQGMarketData.WebApi.Controllers;

[ApiController]
[Route("[controller]")]
public class SubscriptionsTrackerController : Controller
{
	readonly SubscriptionTracker _subscriptionTracker;

	public SubscriptionsTrackerController(SubscriptionTracker subscriptionTracker) => _subscriptionTracker = subscriptionTracker;

	[HttpGet]
	public IActionResult GetSubscriptions() => Ok(_subscriptionTracker.Subscriptions);
}
