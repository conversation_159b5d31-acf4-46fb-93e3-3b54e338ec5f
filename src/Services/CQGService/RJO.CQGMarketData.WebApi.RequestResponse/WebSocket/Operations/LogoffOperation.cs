using UserSession2;
using RJO.CQGMarketData.WebApi.WebSocket.Session;

namespace RJO.CQGMarketData.WebApi.WebSocket.Operations;

public class LogoffOperation
{
	readonly ILogger<LogoffOperation> _logger;
	readonly SessionStateValues _sessionStateValues;

	public LogoffOperation(ILogger<LogoffOperation> logger, SessionStateValues sessionStateValues)
	{
		_logger = logger;
		_sessionStateValues = sessionStateValues;
	}

	public void HandleResponse(LoggedOff loggedOff)
	{
		_sessionStateValues.Reset();
		_sessionStateValues.ShouldLoginAgain = true;
		
		switch (loggedOff.LogoffReason)
		{
			case (uint)LoggedOff.Types.LogoffReason.Forced:
				_logger.LogInformation($"logoff operation: logoff with forced result code {loggedOff.LogoffReason}");
				break;
			
			case (uint)LoggedOff.Types.LogoffReason.Reassigned:
				_logger.LogInformation($"logoff operation: logoff with reassigned result code {loggedOff.LogoffReason}");
				break;
			
			case (uint)LoggedOff.Types.LogoffReason.Redirected:
				_logger.LogInformation($"logoff operation: logoff with redirected result code {loggedOff.LogoffReason}");
				break;
			
			case (uint)LoggedOff.Types.LogoffReason.ByRequest:
				_logger.LogInformation($"logoff operation: logoff with by request result code {loggedOff.LogoffReason}");
				break;
		}
	}
}
