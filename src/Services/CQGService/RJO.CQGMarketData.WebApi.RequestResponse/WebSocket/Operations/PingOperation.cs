using Google.Protobuf;
using UserSession2;
using WebAPI2;

namespace RJO.CQGMarketData.WebApi.WebSocket.Operations;

public class PingOperation(ILogger<PingOperation> logger)
{
	public void HandleResponse(Ping ping, Action<IMessage<ClientMsg>> action)
	{
		if (!ping.HasToken || !ping.HasPingUtcTime) return;

		logger.LogInformation("Ping received. Sending pong...");

		var pong = new Pong
		{
			Token = ping.Token,
			PingUtcTime = ping.PingUtcTime,
			PongUtcTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
		};
		action(new ClientMsg { Pong = pong });
	}
}
