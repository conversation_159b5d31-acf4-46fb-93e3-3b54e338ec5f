using Grpc.Core;
using MediatR;
using RJO.CQGService.WebApi.Application.Handlers;
using RJO.CQGService.WebApi.Grpc.Extensions;
using RJO.CQGService.Contracts.Market;
using static Order2.Order.Types;

namespace RJO.CQGService.WebApi.Grpc;

public class CreateOfferMarketService : OfferMarketGrpc.OfferMarketGrpcBase
{
	readonly IMediator _mediator;
	readonly ILogger<CreateOfferMarketService> _logger;

	public CreateOfferMarketService(IMediator mediator, ILogger<CreateOfferMarketService> logger)
	{
		_mediator = mediator;
		_logger = logger;
	}

	public override async Task<OfferMarketCreationStatus> CreateOfferMarketFromData(CreateOfferMarketRequest request, ServerCallContext context)
	{
		_logger.LogInformation($"Offer limit creation started at : {DateTime.UtcNow}");

		Enum.TryParse(request.Duration.ToString(), out Duration duration);
		Enum.TryParse(request.Side.ToString(), out Side side);

		var command = new CreateOfferMarketCommand(
			request.ClOrderId,
			duration,
			request.GoodThruDate.GetDate(_logger),
			request.Qty,
			side,
			request.Instrument,
			request.TenantId,
			request.AccountId,
			request.OnBehalfOfUser
		);

		return await _mediator.Send(command);
	}
}
