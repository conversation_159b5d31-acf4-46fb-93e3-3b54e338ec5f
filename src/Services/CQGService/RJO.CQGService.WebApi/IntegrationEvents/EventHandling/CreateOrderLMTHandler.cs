using Order2;
using Hangfire;
using Microsoft.Extensions.Options;
using RJO.BuildingBlocks.EventBus.Abstractions;
using RJO.CQGService.WebApi.Application.Jobs;
using RJO.CQGService.WebApi.Application.Manager;
using RJO.CQGService.WebApi.Configuration.Models;
using RJO.CQGService.WebApi.IntegrationEvents.Extensions.Assertions;
using RJO.CQGService.WebApi.IntegrationEvents.Extensions.Logging;
using RJO.CQGService.WebApi.WebSocket.Extensions;
using RJO.IntegrationEvents.Commons.Events;

namespace RJO.CQGService.WebApi.IntegrationEvents.EventHandling;

public class CreateOrderLMTHandler : IIntegrationEventHandler<CreateOrderLMTEvent>
{
	readonly ILogger<CreateOrderLMTHandler> _logger;
	readonly IBackgroundJobClient _backgroundJobClient;
	readonly ScheduleManager _scheduleManager;
	readonly Settings _settings;

	public CreateOrderLMTHandler(ILogger<CreateOrderLMTHandler> logger, IBackgroundJobClient backgroundJobClient, ScheduleManager scheduleManager, IOptions<Settings> settings)
	{
		_logger = logger;
		_backgroundJobClient = backgroundJobClient;
		_scheduleManager = scheduleManager;
		_settings = settings.Value;
	}

	public async Task Handle(CreateOrderLMTEvent @event)
	{
		_logger.LogCreateOrderLimitHandlerInformation($"Started handling for event {@event.SerializeObject()}");
		IntegrationEventsAssertions.Tag50IsNotNullOrEmpty(@event.OfferNumber, @event.Tag50, _logger);
		var jobOptions = new CreateOrderLMTJobOptions
		{
			CreateOrderLMTEvent = @event,
			OnBehalfOfUser = @event.Tag50
		};
		if (_settings.ApplicationFlags.ScheduleDisabled)
		{
			_logger.LogCreateOrderLimitHandlerInformation($"Job queued for event {@event.SerializeObject()}");
			_backgroundJobClient.Enqueue<CreateOrderLMTJob>(x => x.Perform(jobOptions, null));
		}
		else
		{
			var sessionInfo = await _scheduleManager.GetSessionScheduleInfoForOrderAsync(@event.Symbol, Order.Types.OrderType.Lmt);
			_logger.LogCreateOrderLimitHandlerInformation(
				$"Session info result for Order Id: {@event.OfferNumber} is {sessionInfo.SerializeObject()}");
			if (sessionInfo.IsScheduled)
			{
				_logger.LogCreateOrderLimitHandlerInformation($"Job scheduled for event {@event.SerializeObject()}");
				_backgroundJobClient.Schedule<CreateOrderLMTJob>(x => x.Perform(jobOptions, null), sessionInfo.Offset);
			}
			else
			{
				_logger.LogCreateOrderLimitHandlerInformation($"Job queued for event {@event.SerializeObject()}");
				_backgroundJobClient.Enqueue<CreateOrderLMTJob>(x => x.Perform(jobOptions, null));
			}
		}

		_logger.LogCreateOrderLimitHandlerInformation($"Ended handling for event {@event.SerializeObject()}");
	}
}
