using Hangfire;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.EventBus.Abstractions;
using RJO.CQGService.WebApi.Application.Jobs;
using RJO.CQGService.WebApi.Application.Jobs.Options;
using RJO.CQGService.WebApi.Common.Extensions;
using RJO.CQGService.WebApi.Common.Helpers;
using RJO.CQGService.WebApi.Common.Settings;
using RJO.IntegrationEvents.Commons.Events;

namespace RJO.CQGService.WebApi.IntegrationEvents.EventHandling;

public class CreateSpreadOrderHandler : IIntegrationEventHandler<CreateSpreadOrderEvent>
{
	readonly IBackgroundJobClient _backgroundJobClient;
	readonly ILogger<CreateSpreadOrderHandler> _logger;
	readonly ApplicationSettings _settings;
	readonly ScheduleManager _scheduleManager;

	public CreateSpreadOrderHandler(
		IBackgroundJobClient backgroundJobClient,
		ILogger<CreateSpreadOrderHandler> logger,
		ApplicationSettings settings,
		ScheduleManager scheduleManager)
	{
		_backgroundJobClient = backgroundJobClient;
		_logger = logger;
		_settings = settings;
		_scheduleManager = scheduleManager;
	}

	public async Task Handle(CreateSpreadOrderEvent @event)
	{
		_logger.LogInformation("Started handling spread order for event {@Event}", @event);
		IntegrationEventsAssertions.Tag50IsNotNullOrEmpty(@event.ContractNumber, @event.Tag50, _logger);
		
		var jobOptions = new CreateSpreadOrderJobOptions
		{
			CreateSpreadOrderEvent = @event,
			OnBehalfOfUser = @event.Tag50
		};

		if (_settings.ApplicationFlags.ScheduleDisabled)
		{
			_logger.LogInformation("Job queued for spread order event {@Event}", @event);
			_backgroundJobClient.Enqueue<CreateSpreadOrderJob>(x => x.Perform(jobOptions, null));
		}
		else
		{
			var orderType = @event.IsMarketOrder ? Order.Types.OrderType.Mkt : Order.Types.OrderType.Lmt;
			var sessionInfo = await _scheduleManager.GetSessionScheduleInfoForOrderAsync(@event.SpreadSymbol, orderType);
			_logger.LogInformation("Session info result for Spread Order Id: {ContractNumber} is {SessionInfo}", @event.ContractNumber, sessionInfo);

			if (sessionInfo.IsScheduled)
			{
				_logger.LogInformation("Job scheduled for spread order event {@Event}", @event);
				_backgroundJobClient.Schedule<CreateSpreadOrderJob>(x => x.Perform(jobOptions, null), sessionInfo.Offset);
			}
			else
			{
				_logger.LogInformation("Job queued for spread order event {@Event}", @event);
				_backgroundJobClient.Enqueue<CreateSpreadOrderJob>(x => x.Perform(jobOptions, null));
			}
		}

		_logger.LogInformation("Ended handling spread order for event {@Event}", @event);
	}
}
