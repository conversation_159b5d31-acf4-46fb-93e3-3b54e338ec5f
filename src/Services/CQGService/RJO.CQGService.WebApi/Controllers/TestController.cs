using MediatR;
using Microsoft.AspNetCore.Mvc;
using Order2;
using RJO.CQGService.WebApi.Application.Handlers;
using RJO.CQGService.WebApi.WebSocket;

namespace RJO.CQGService.WebApi.Controllers;

[ApiController]
[Route("[controller]")]
public class TestController(ISender sender, CqgHostService cqgHostService) : ControllerBase
{
	[HttpGet("/spread-symbols")]
	public async Task<IActionResult> GetSpreadSymbols(string symbol)
	{
		var result = await cqgHostService.GetInstrumentGroupRequestAsync(symbol);
		
		return Ok(result);
	}
	
	[HttpPost("/spread-trade")]
	public async Task<IActionResult> CreateSpreadTrade()
	{
		var command = new CreateOfferLimitCommand
		{
			ClOrderId = $"SPREAD-TRADE-{Guid.NewGuid().ToString("N")[..8]}", // 64 characters allowed
			Duration = Order.Types.Duration.Day, // Limit Order + GTD is not supported for testing
			Price = 3.00m,
			GoodThruDate = DateTime.UtcNow,
			Instrument = "F.US.ZCES1N25",
			Qty = new()
			{
				Significand = 2,
				Exponent = 0
			},
			Side = Order.Types.Side.Buy,
			TenantId = "89a82642-753a-4ad6-8fc5-a15d0854e500",
			AccountId = ********,
			OnBehalfOfUser = "HRVYSTLD5"
		};
		
		await sender.Send(command);
		return Ok();
	}
}
