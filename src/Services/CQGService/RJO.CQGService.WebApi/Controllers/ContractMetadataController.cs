using Metadata2;
using Microsoft.AspNetCore.Mvc;
using RJO.BuildingBlocks.WebCommon.Authorization;
using RJO.CQGService.WebApi.WebSocket;
using System.ComponentModel.DataAnnotations;

namespace RJO.CQGService.WebApi.Controllers;

[ApiController]
[Route("[controller]")]
public class ContractMetadataController : ControllerBase
{
	readonly CqgHostService _cqgHostService;

	public ContractMetadataController(CqgHostService cqgHostService) => _cqgHostService = cqgHostService;

	[HttpGet]
	[ProducesResponseType(typeof(ContractMetadata), StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status404NotFound)]
	public IActionResult SearchContractMetadata([Required] string instrument)
	{
		var result = _cqgHostService.GetContractMetadataPerInstrument(instrument);

		if (result == null)
		{
			return NotFound();
		}
		
		return Ok(result);
	}

	[HttpGet]
	[Route("GetAllSymbols")]
	[ProducesResponseType(typeof(Dictionary<string, List<string>>), StatusCodes.Status200OK)]
	public IActionResult SearchAllCurrentMetadata()
	{
		var result = _cqgHostService.GetAllContractMetadataSymbols();
		return Ok(result);
	}
}
