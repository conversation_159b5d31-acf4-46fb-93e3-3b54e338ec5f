using WebAPI2;
using Metadata2;
using Order2;
using TradeRouting2;
using UserSession2;
using DiagnosticSessionToken2;
using Google.Protobuf;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using RJO.BuildingBlocks.Common;
using RJO.CQGService.Common.Exceptions;
using RJO.CQGService.Common.RequestResponse;
using RJO.CQGService.Persistence.Database;
using RJO.CQGService.WebApi.Configuration.Models;
using RJO.CQGService.WebApi.Core.HealthChecks.Models;
using RJO.CQGService.WebApi.WebSocket.Client;
using RJO.CQGService.WebApi.WebSocket.Exceptions;
using RJO.CQGService.WebApi.WebSocket.Extensions;
using RJO.CQGService.WebApi.WebSocket.Operations;
using RJO.CQGService.WebApi.WebSocket.OrderProcessor;
using RJO.CQGService.WebApi.WebSocket.Session;
using System.Collections.Immutable;
using System.Net.WebSockets;
using System.Reactive.Concurrency;
using System.Reactive.Linq;
using Websocket.Client;
using Decimal = Cqg.Decimal;
using OrderStatus = Shared1.OrderStatus;
using OrderStatus2 = Order2.OrderStatus;

#nullable enable

namespace RJO.CQGService.WebApi.WebSocket;

public sealed class CqgHostService : IHostedService, IAsyncDisposable
{
	readonly TimeSpan _requestTimeout = TimeSpan.FromSeconds(5);
	
	readonly IHostEnvironment _environment;
	readonly CancelOrderOperation _cancelOrderOperation;
	readonly CreateOrderOperation _createOrderOperation;
	readonly EditOrderOperation _editOrderOperation;
	readonly LogoffOperation _logoffOperation;
	readonly LogonOperation _logonOperation;
	readonly RestoreSessionOperation _restoreSessionOperation;
	readonly PingOperation _pingOperation;
	readonly InformationReportOperation _informationReportOperation;
	readonly CqgWebSocketClient _client;
	readonly OrderProcessorManager _orderProcessorManager;
	readonly SessionOrdersStatus _sessionOrdersStatus;
	readonly ILogger<CqgHostService> _logger;
	readonly IServiceProvider _serviceProvider;
	readonly RequestIdGenerator _requestIdGenerator;
	readonly CqgSettings _cqgSettings;
	readonly LogonStateManager _logonStateManager;
	readonly PendingRequestManager _pendingRequestManager;
	readonly SubscriptionManager _subscriptionManager;

	IDisposable _mainSubscription;

	public SessionStateValues SessionStateValues { get; }
	public DateTime SessionBaseTime => SessionStateValues.BaseDateTime;
	public string SessionToken => SessionStateValues.SessionToken;

	public CqgHostService(
		IHostEnvironment environment,
		LogonOperation logonOperation,
		RestoreSessionOperation restoreSessionOperation,
		LogoffOperation logoffOperation,
		CreateOrderOperation createOrderOperation,
		CancelOrderOperation cancelOrderOperation,
		EditOrderOperation editOrderOperation,
		PingOperation pingOperation,
		InformationReportOperation informationReportOperation,
		SessionStateValues sessionStateValues,
		SessionOrdersStatus sessionOrdersStatus,
		OrderProcessorManager orderProcessorManager,
		IOptions<Settings> settings,
		ILogger<CqgHostService> logger,
		IServiceProvider serviceProvider,
		RequestIdGenerator requestIdGenerator,
		LogonStateManager logonStateManager,
		PendingRequestManager pendingRequestManager,
		SubscriptionManager subscriptionManager,
		CqgWebSocketClient? client = null
	)
	{
		_environment = environment;
		_logger = logger;
		_logonOperation = logonOperation;
		_restoreSessionOperation = restoreSessionOperation;
		SessionStateValues = sessionStateValues;
		_sessionOrdersStatus = sessionOrdersStatus;
		_createOrderOperation = createOrderOperation;
		_cancelOrderOperation = cancelOrderOperation;
		_editOrderOperation = editOrderOperation;
		_orderProcessorManager = orderProcessorManager;
		_logoffOperation = logoffOperation;
		_pingOperation = pingOperation;
		_informationReportOperation = informationReportOperation;
		_serviceProvider = serviceProvider;
		_cqgSettings = settings.Value.Cqg;
		_requestIdGenerator = requestIdGenerator;
		_logonStateManager = logonStateManager;
		_pendingRequestManager = pendingRequestManager;
		_subscriptionManager = subscriptionManager;

		_client = client ?? new CqgWebSocketClient(_logger, new(_cqgSettings.HostName), _cqgSettings.Timeout, DisconnectActionAsync, ReconnectActionAsync);
	}

	#region Request/response operations

	public async Task LogonAsync(string username, string password, string privateLabel, string clientAppId, string clientAppVersion, Func<LogonResult, ValueTask> onSuccess, Func<string, ValueTask> onError)
    {
        var logon = new Logon
        {
            UserName = username,
            Password = password,
            PrivateLabel = privateLabel,
            ClientAppId = clientAppId,
            ClientVersion = clientAppVersion,
            SessionSettings = { (uint)Logon.Types.SessionSetting.AllowSessionRestore },
            ProtocolVersionMinor = (uint)ProtocolVersionMinor.ProtocolVersionMinor,
            ProtocolVersionMajor = (uint)ProtocolVersionMajor.ProtocolVersionMajor,
            DropConcurrentSession = true,
			Fingerprint = DateTime.UtcNow.ToString("o")
        };

		logon.SetExtension(DiagnosticSessionToken2Extensions.IncludeDiagnosticSessionToken, true);
		
        await RequestAsync(new() { Logon = logon }, onSuccess, onError, serverMsg => serverMsg.LogonResult != null, serverMsg => serverMsg.LogonResult);
    }

    public async Task RestoreSessionAsync(string sessionToken, string privateLabel, string clientAppId, Func<RestoreOrJoinSessionResult, ValueTask> onSuccess, Func<string, ValueTask> onError)
    {
        var restoreOrJoinSession = new RestoreOrJoinSession
        {
            SessionToken = sessionToken,
            PrivateLabel = privateLabel,
            ClientAppId = clientAppId,
            ProtocolVersionMinor = (uint)ProtocolVersionMinor.ProtocolVersionMinor,
            ProtocolVersionMajor = (uint)ProtocolVersionMajor.ProtocolVersionMajor,
        };

        await RequestAsync(new() { RestoreOrJoinSession = restoreOrJoinSession }, onSuccess, onError,
            serverMsg => serverMsg.RestoreOrJoinSessionResult != null, serverMsg => serverMsg.RestoreOrJoinSessionResult);
    }

    public async Task LogoffAsync(Func<LoggedOff, ValueTask> onSuccess, Func<string, ValueTask> onError) =>
		await RequestAsync(new() { Logoff = new() }, onSuccess, onError, serverMsg => serverMsg.LoggedOff != null, serverMsg => serverMsg.LoggedOff);

	public async Task<InformationReport> GetInstrumentGroupRequestAsync(string groupSymbol) =>
		await ExecuteWithTaskCompletionSource<InformationReport>(async (onSuccess, onError) =>
		{
			await GetInstrumentGroupResolutionAsync(groupSymbol, onSuccess, onError);
		});
	
	public async Task GetInstrumentGroupResolutionAsync(string groupSymbol, Func<InformationReport, ValueTask> onSuccess, Func<string, ValueTask> onError)
	{
		var requestId = (uint)_requestIdGenerator.GetNextRequestId();

		var informationRequest = new InformationRequest
		{
			Id = requestId,
			InstrumentGroupRequest = new() { InstrumentGroupId = groupSymbol }
		};

		await RequestAsync(new() { InformationRequests = { informationRequest } }, onSuccess, onError,
			serverMsg => serverMsg.InformationReports != null && serverMsg.InformationReports.Any(report => report.Id == requestId),
			serverMsg => serverMsg.InformationReports.First(x => x.Id == requestId),
			informationReport => informationReport.IsReportComplete,
			60.Seconds());
	}

	public async Task GetSymbolResolutionAsync(string symbol, Func<InformationReport, ValueTask> onSuccess, Func<string, ValueTask> onError)
	{
		var requestId = (uint)_requestIdGenerator.GetNextRequestId();

		var informationRequest = new InformationRequest
		{
			Id = requestId,
			SymbolResolutionRequest = new() { Symbol = symbol }
		};

		await RequestAsync(new() { InformationRequests = { informationRequest } }, onSuccess, onError,
			serverMsg => serverMsg.InformationReports != null && serverMsg.InformationReports.Any(report => report.Id == requestId),
			serverMsg => serverMsg.InformationReports.First(x => x.Id == requestId));
	}
	
	public async Task SubscribeToSymbolResolutionAsync(string symbol, Func<InformationReport, ValueTask> onSuccess, Func<string, ValueTask> onError)
    {
        var requestId = (uint)_requestIdGenerator.GetNextRequestId();

        var informationRequest = new InformationRequest
        {
            Id = requestId,
            SymbolResolutionRequest = new() { Symbol = symbol },
            Subscribe = true
        };

        await SubscribeAsync(new() { InformationRequests = { informationRequest } }, onSuccess, onError,
            serverMsg => serverMsg.InformationReports != null && serverMsg.InformationReports.Any(report => report.Id == requestId),
            serverMsg => serverMsg.InformationReports.First(x => x.Id == requestId));
    }
	
	public async Task SubscribeToOrderUpdatesAsync(
		Func<ImmutableList<TradeSubscriptionStatus>, ValueTask> onSubscriptionStatus,
		Func<ImmutableList<OrderStatus2>, ValueTask> onOrderStatus,
		Func<ImmutableList<TradeSnapshotCompletion>, ValueTask> onSnapshotCompletion,
		Func<string, ValueTask> onError)
	{
		var requestId = (uint)_requestIdGenerator.GetNextRequestId();

		var tradeSubscription = new TradeSubscription
		{
			Id = requestId,
			SubscriptionScopes = { (uint)TradeSubscription.Types.SubscriptionScope.Orders },
			PublicationType = (uint)TradeSubscription.Types.PublicationType.AllAuthorized,
			Subscribe = true
		};

		var clientMsg = new ClientMsg { TradeSubscriptions = { tradeSubscription } };

		var subscriptions = new (Func<dynamic, ValueTask> OnSuccess, Func<ServerMsg, bool> Matcher, Func<ServerMsg, dynamic> Extractor)[]
		{
			(
				OnSuccess: msg => onSubscriptionStatus((ImmutableList<TradeSubscriptionStatus>)msg),
				Matcher: serverMsg => serverMsg.TradeSubscriptionStatuses.Any(x => x.Id == requestId),
				Extractor: serverMsg => serverMsg.TradeSubscriptionStatuses.ToImmutableList()
			),
			(
				OnSuccess: msg => onOrderStatus((ImmutableList<OrderStatus2>)msg),
				Matcher: serverMsg => serverMsg.OrderStatuses.Any(x => x.SubscriptionIds.Contains(requestId)),
				Extractor: serverMsg => serverMsg.OrderStatuses.ToImmutableList()
			),
			(
				OnSuccess: msg => onSnapshotCompletion((ImmutableList<TradeSnapshotCompletion>)msg),
				Matcher: serverMsg => serverMsg.TradeSnapshotCompletions.Any(x => x.SubscriptionId == requestId),
				Extractor: serverMsg => serverMsg.TradeSnapshotCompletions.ToImmutableList()
			)
		};

		await SubscribeAsync(clientMsg, onError, subscriptions);
	}

	public async Task GetSessionInformationAsync(int sessionId, Func<InformationReport, ValueTask> onSuccess, Func<string, ValueTask> onError)
	{
		var requestId = (uint)_requestIdGenerator.GetNextRequestId();

		var informationRequest = new InformationRequest
		{
			Id = requestId,
			SessionInformationRequest = new()
			{
				SessionInfoId = sessionId,
				FromUtcTime = SessionStateValues.BaseDateTime.GetMillisecondsFromBaseDateTime(DateTime.UtcNow.Date),
				ToUtcTime = SessionStateValues.BaseDateTime.GetMillisecondsFromBaseDateTime(DateTime.UtcNow.Date.AddDays(_cqgSettings.SessionDays))
			}
		};

		await RequestAsync(new() { InformationRequests = { informationRequest } }, onSuccess, onError,
			serverMsg => serverMsg.InformationReports != null && serverMsg.InformationReports.Any(report => report.Id == requestId),
			serverMsg => serverMsg.InformationReports.First(x => x.Id == requestId));
	}
	
	public static async Task<T> ExecuteWithTaskCompletionSource<T>(
		Func<Func<T, ValueTask>, Func<string, ValueTask>, Task> action)
	{
		var tcs = new TaskCompletionSource<T>();

		await action(
			async result =>
			{
				tcs.TrySetResult(result); // Resolve the Task with the result
				await ValueTask.CompletedTask;
			},
			async error =>
			{
				tcs.TrySetException(new InvalidOperationException(error)); // Reject the Task with an exception
				await ValueTask.CompletedTask;
			});

		return await tcs.Task; // Wait for the Task to complete
	}
	
	public async Task<InformationReport> GetSessionInformationAsync(int sessionId) =>
		await ExecuteWithTaskCompletionSource<InformationReport>(async (onSuccess, onError) =>
		{
			await GetSessionInformationAsync(sessionId, onSuccess, onError);
		});
	
	public async Task SubscribeToSessionInformationAsync(int sessionId, Func<InformationReport, ValueTask> onSuccess, Func<string, ValueTask> onError)
	{
		var requestId = (uint)_requestIdGenerator.GetNextRequestId();

		var informationRequest = new InformationRequest
		{
			Id = requestId,
			SessionInformationRequest = new()
			{
				SessionInfoId = sessionId,
				FromUtcTime = SessionStateValues.BaseDateTime.GetMillisecondsFromBaseDateTime(DateTime.UtcNow.Date)
			},
			Subscribe = true
		};

		await SubscribeAsync(new() { InformationRequests = { informationRequest } }, onSuccess, onError,
			serverMsg => serverMsg.InformationReports != null && serverMsg.InformationReports.Any(report => report.Id == requestId),
			serverMsg => serverMsg.InformationReports.First(x => x.Id == requestId));
	}
	
    public async Task GetAccountInformationAsync(Func<InformationReport, ValueTask> onSuccess, Func<string, ValueTask> onError)
    {
        var requestId = (uint)_requestIdGenerator.GetNextRequestId();

        var informationRequest = new InformationRequest
        {
            Id = requestId,
            AccountsRequest = new()
        };

        await RequestAsync(new() { InformationRequests = { informationRequest } }, onSuccess, onError,
            serverMsg => serverMsg.InformationReports != null && serverMsg.InformationReports.Any(report => report.Id == requestId),
            serverMsg => serverMsg.InformationReports.First(x => x.Id == requestId));
    }
	
	#endregion

	#region internal client lifecycle operations

	public async ValueTask DisposeAsync()
	{
		_logger.LogInformation("{CqgHostService} disposing", nameof(CqgHostService));
			
		_mainSubscription?.Dispose();

		await StopClientAsync();
			
		_logger.LogInformation("{CqgHostService} disposed", nameof(CqgHostService));
	}

	public async Task StartAsync(CancellationToken cancellationToken)
	{
		_logger.LogInformation("{CqgHostService} starting", nameof(CqgHostService));
		
		_logger.LogInformation("Starting subscription");
		CreateSubscription();
		_logger.LogInformation("Subscription created");

		_logger.LogInformation("Starting client");
		await _client.Start();
		_logger.LogInformation("Client started");
		
		_logger.LogInformation("{CqgHostService} started", nameof(CqgHostService));
	}

	public async Task StopAsync(CancellationToken cancellationToken)
	{
		_logger.LogInformation("{CqgHostService} stopping", nameof(CqgHostService));
		
		await StopClientAsync();
		
		_logger.LogInformation("{CqgHostService} stopped", nameof(CqgHostService));
	}
	
	async ValueTask StopClientAsync()
	{
		if (!_client.IsStarted) return;
		
		await LogoffAsync(_ =>
		{
			_logger.LogInformation("Successfully logged off while stopping client");
			return ValueTask.CompletedTask;
		}, onError =>
		{
			_logger.LogWarning("Failed to logoff due to {ErrorMessage}", onError);
			return ValueTask.CompletedTask;
		});
		
		await _client.Stop(WebSocketCloseStatus.NormalClosure, "Stopping client");
	}
	
	#endregion internal client lifecycle operations

	#region Operations

	public async Task SendCreateOrderRequestAsync(int accountId,
		string clOrderId,
		uint duration,
		decimal price,
		decimal stopPrice,
		DateTime goodThruDate,
		Decimal qty,
		uint side,
		string instrument,
		uint orderType,
		string tenantId,
		string onBehalfOfUser)
	{
		if (!_client.IsStarted || !SessionStateValues.IsLoggedOn)
		{
			_logger.LogCritical("create order: client is not started");
			throw new("create order: client not started");
		}

		if (!SessionStateValues.IsTradeSnapshotComplete)
		{
			_logger.LogWarning("create order: client is not ready");
			throw new("create order: client is not ready");
		}

		if (!SessionStateValues.ContractSymbolSubscriptionAlreadyRequested.ContainsKey(instrument))
		{
			await GetSymbolResolutionAsync(instrument, report => _informationReportOperation.HandleResponse(report), onError =>
			{
				_logger.LogWarning("create order: Failed to retrieve contract metadata information due to {ErrorMessage}", onError);
				return ValueTask.CompletedTask;
			});
			
			throw new SymbolResolutionNotFoundException($"create order: symbol was not found for instrument {instrument}");
		}

		if (SessionStateValues.ContractIdByContractSymbol.ContainsKey(instrument))
		{
			var requestId = (uint)_requestIdGenerator.GetNextRequestId();
			var clientMessage = _createOrderOperation.CreateRequest(requestId, accountId, clOrderId, duration, price,
				stopPrice, goodThruDate, qty, side, instrument, orderType, tenantId, onBehalfOfUser);
			Send(clientMessage);
		}
		else
		{
			_logger.LogWarning("create order: Unable to create order because no contract metadata subscription found for instrument {Instrument}", instrument);
			throw new($"create order: Unable to create order because no contract metadata subscription found for instrument {instrument} and client order id {clOrderId}");
		}
	}

	async Task<string> FindChainOrderIdAsync(string clOrderId, string fullClOrderId)
	{
		var chainOrderId = string.Empty;

		_logger.LogInformation("edit order: Searching for ClientOrderId {ClientOrderId} in CqgHost::SendEditOrder()", clOrderId);
		if (_sessionOrdersStatus.ChainOrderIdByClientOrderId.TryGetValue(clOrderId, out var chainOrderId1))
		{
			chainOrderId = chainOrderId1;
		}
		else
		{
			_logger.LogInformation("edit order: Searching for full ClientOrderId {FullClientOrderId} in CqgHost::SendEditOrder()", fullClOrderId);
			if (_sessionOrdersStatus.ChainOrderIdByClientOrderId.TryGetValue(fullClOrderId, out var chainOrderId2))
			{
				chainOrderId = chainOrderId2;
			}
		}

		if (string.IsNullOrEmpty(chainOrderId))
		{
			chainOrderId = await GetChainOrderIdFromClientOrderIdAsync(clOrderId);
			if (!string.IsNullOrEmpty(chainOrderId))
			{
				_logger.LogInformation("edit order: Found ChainOrderId {ChainOrderId} for ClientOrderId {ClientOrderId} in the database.", chainOrderId, clOrderId);
			}
			else
			{
				_logger.LogWarning("edit order: Couldn't find ChainOrderId for ClientOrderId {ClientOrderId} in the database.", clOrderId);
			}
		}

		return chainOrderId;
	}
	
	async Task<string> GetChainOrderIdFromClientOrderIdAsync(string clOrderId)
	{
		using var scope = _serviceProvider.CreateScope();
		var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
		
		_logger.LogInformation("Searching for ChainOrderId using ClOrderId {ClientOrderId} in CqgHost", clOrderId);
		
		// 1. search market transaction table. See if client number field matches our search values. search by non tenant search might have to strip tenant id off
		// 2. get the market id from this record.
		// 3. search CQG order status table by market id and get chain order id. Populate that here and allow code to continue
		var chainOrderId = string.Empty;

		try
		{
			var marketId = await dbContext.MarketTransactions
				.AsNoTracking()
				.Where(x => x.ClientNumber == clOrderId)
				.Select(x => x.MarketId)
				.FirstOrDefaultAsync();

			if (string.IsNullOrEmpty(marketId))
			{
				_logger.LogWarning("MarketId not found for ClientOrderId {ClientOrderId}", clOrderId);
				return chainOrderId;
			}

			chainOrderId = await dbContext.CQGOrderStatus
				.AsNoTracking()
				.Where(x => x.OrderId == marketId)
				.Select(x => x.ChainOrderId)
				.FirstOrDefaultAsync();

			if (string.IsNullOrEmpty(chainOrderId))
			{
				_logger.LogWarning("ChainOrderId not found for MarketId {MarketId}", marketId);
			}
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "An error occurred while searching for ChainOrderId using ClientOrderId {ClientOrderId}", clOrderId);
		}

		return chainOrderId;
	}

	public async Task SendCancelOrderAsync(string clOrderId,
		int accountId,
		string tenantId,
		string onBehalfOf)
	{
		if (!_client.IsStarted || !SessionStateValues.IsLoggedOn)
		{
			_logger.LogCritical("cancel order: client is not started");
			throw new("cancel order: client not started");
		}

		if (!SessionStateValues.IsTradeSnapshotComplete)
		{
			_logger.LogWarning("cancel order: client is not ready");
			throw new("cancel order: client is not ready");
		}

		var fullClOrderId = $"{clOrderId}_{tenantId.Replace("-", "", StringComparison.InvariantCultureIgnoreCase).ToLowerInvariant()}";
		var chainOrderId = await FindChainOrderIdAsync(clOrderId, fullClOrderId);

		if (string.IsNullOrEmpty(chainOrderId))
		{
			var log = $"cancel order: Couldn't find the order on the market for client order ID '{clOrderId}'. Current orders in place: {_sessionOrdersStatus.ChainOrderIdByClientOrderId.Values.SerializeObject()} -- {_sessionOrdersStatus.ChainOrderIdByClientOrderId.Keys.SerializeObject()}";
			_logger.LogWarning(log);
			throw new InvalidOperationException(log);
		}

		_logger.LogInformation("cancel order: Found chain order ID '{ChainOrderId}' for client order ID '{ClientOrderId}'.", chainOrderId, clOrderId);

		if (_sessionOrdersStatus.OrdersPlaced.TryGetValue(chainOrderId, out var orderStatus))
		{
			if (orderStatus.Status != (uint)OrderStatus.Types.Status.Working)
			{
				_logger.LogInformation("cancel order: Order with client order ID '{ClientOrderId}' is in a non-working state. Publishing status update.", clOrderId);
				await _orderProcessorManager.ProcessOrderStatus(orderStatus);
			}
			else
			{
				var requestId = (uint)_requestIdGenerator.GetNextRequestId();
				var message = _cancelOrderOperation.CreateRequest(requestId, accountId, orderStatus.OrderId, clOrderId, tenantId, onBehalfOf);
				Send(message);
			}
		}
		else
		{
			var log = $"cancel order: Could not find the order on the market for chain order ID '{chainOrderId}'. Current orders in place: {_sessionOrdersStatus.OrdersPlaced.Values.Select(op => op.Order.ClOrderId).SerializeObject()}";
			_logger.LogWarning(log);
			throw new InvalidOperationException(log);
		}
	}
	
	public async Task SendEditOrderAsync(int accountId,
		string clOrderId,
		Decimal qty,
		decimal limitPrice,
		long stopPrice,
		Order.Types.Duration duration,
		string tenantId,
		string onBehalfOf)
	{
		if (!_client.IsStarted || !SessionStateValues.IsLoggedOn)
		{
			_logger.LogCritical("edit order: client is not started");
			throw new("edit order: client not started");
		}

		if (!SessionStateValues.IsTradeSnapshotComplete)
		{
			_logger.LogWarning("edit order: client is not ready");
			throw new("edit order: client is not ready");
		}

		var fullClOrderId = $"{clOrderId}_{tenantId.Replace("-", "", StringComparison.InvariantCultureIgnoreCase).ToLowerInvariant()}";
		var chainOrderId = await FindChainOrderIdAsync(clOrderId, fullClOrderId);

		if (string.IsNullOrEmpty(chainOrderId))
		{
			var log = $"edit order: Couldn't find the order on the market {clOrderId}, current orders in place are {_sessionOrdersStatus.ChainOrderIdByClientOrderId.Values.SerializeObject()} -- {_sessionOrdersStatus.ChainOrderIdByClientOrderId.Keys.SerializeObject()}";
			_logger.LogWarning(log);
			throw new(log);
		}
		
		_logger.LogInformation($"edit order: Searching for ClientOrderId result {chainOrderId} in CqgHost::SendEditOrder()");
		if (_sessionOrdersStatus.OrdersPlaced.TryGetValue(chainOrderId, out var orderStatus))
		{
			if (orderStatus.Status != (uint)OrderStatus.Types.Status.Working)
			{
				_logger.LogInformation("edit order: the order is in a non-working state, publishing status update to bus");
				await _orderProcessorManager.ProcessOrderStatus(orderStatus);
				return;
			}
			
			ContractMetadata contractMetadata = null;

			if (orderStatus.ContractMetadata.Count > 0)
			{
				contractMetadata = orderStatus.ContractMetadata.FirstOrDefault(x => x.ContractId == orderStatus.Order.ContractId);
			}

			if (contractMetadata == null)
			{
				SessionStateValues.ContractMetadataByContractId.TryGetValue(orderStatus.Order.ContractId, out contractMetadata);
			}

			if (contractMetadata != null)
			{
				var requestId = (uint)_requestIdGenerator.GetNextRequestId();
				var message = _editOrderOperation.CreateRequest(
					requestId,
					orderStatus.Order.ClOrderId,
					accountId,
					orderStatus,
					qty,
					(uint)duration,
					limitPrice,
					stopPrice,
					contractMetadata,
					tenantId,
					onBehalfOf
				);
				Send(message);
			}
			else
			{
				_logger.LogWarning($"edit order: contract metadata was not found for {orderStatus.Order.ContractId}");
				throw new SymbolResolutionNotFoundException($"edit order: contract metadata was not found for {orderStatus.Order.ContractId}");
			}
		}
		else
		{
			var log = $"edit order: Couldn't find the order on the market {clOrderId}, current orders in place are {_sessionOrdersStatus.OrdersPlaced.Values.Select(op => op.Order.ClOrderId).SerializeObject()}";
			_logger.LogWarning(log);
			throw new(log);
		}
	}
	
	#endregion Operations

	#region Non transactional operations

	public Task<CqgHealthCheckReport> GetHealthStatusReport()
	{
		var isReady = SessionStateValues.IsTradeSnapshotComplete;
		var isLoggedOn = SessionStateValues.IsLoggedOn;
		var clientStarted = _client.IsStarted;
		var clientIsRunning = _client.IsRunning;
		
		var clientStatus = new ClientStatus
		{
			IsReady = isReady,
			IsLoggedOn = isLoggedOn,
			ClientRunning = clientIsRunning,
			ClientStarted = clientStarted
		};

		var contractMetadataList = SessionStateValues.ContractMetadataBySymbol.Keys.ToList();

		var subscriptionStatus = new SubscriptionStatus
		{
			ContractMetadataList = contractMetadataList
		};

		var report = new CqgReport
		{
			ClientStatus = clientStatus,
			SubscriptionStatus = subscriptionStatus
		};

		if (!isReady || !isLoggedOn || !clientStarted || !clientIsRunning)
		{
			return Task.FromResult<CqgHealthCheckReport>(new()
			{
				Status = HealthStatus.Unhealthy,
				Report = report
			});
		}
		
		if (contractMetadataList.Count == 0)
		{
			return Task.FromResult<CqgHealthCheckReport>(new()
			{
				Status = HealthStatus.Unhealthy,
				Report = report
			});
		}
		
		return Task.FromResult<CqgHealthCheckReport>(new()
		{
			Status = HealthStatus.Healthy,
			Report = report
		});
	}

	public ContractMetadata GetContractMetadataPerInstrument(string instrument)
	{
		if (SessionStateValues.ContractIdByContractSymbol.TryGetValue(instrument, out var contractId))
		{
			if (SessionStateValues.ContractMetadataByContractId.TryGetValue(contractId, out var contractMetadata))
			{
				return contractMetadata;
			}
		}

		_logger.LogInformation("No contract metadata found for this symbol");
		return null;
	}

	public bool IsClientReady => SessionStateValues.IsTradeSnapshotComplete;

	public SearchResponse SearchByClOrderId(string clOrderId)
	{
		if (!_sessionOrdersStatus.ChainOrderIdByClientOrderId.TryGetValue(clOrderId, out var chainOrderId))
		{
			_logger.LogError("Unable to find a corresponding ChainOrderId for the provided ClOrderId: '{ClOrderId}'. Current ClOrderIds available: {ClOrderIds}. ChainOrderIds: {ChainOrderIds}.",
				clOrderId, string.Join(", ", _sessionOrdersStatus.ChainOrderIdByClientOrderId.Keys), string.Join(", ", _sessionOrdersStatus.ChainOrderIdByClientOrderId.Values));
			return new(null, $"No order associated with ClOrderId: '{clOrderId}' was found.");
		}

		if (!_sessionOrdersStatus.OrdersPlaced.TryGetValue(chainOrderId, out var orderStatus))
		{
			_logger.LogError("Found ChainOrderId: '{ChainOrderId}' for ClOrderId: '{ClOrderId}', but unable to find a corresponding order status. Current order statuses: {OrderStatuses}. ChainOrderIds with statuses: {ChainOrderIdsWithStatuses}.",
				chainOrderId, clOrderId, string.Join(", ", _sessionOrdersStatus.OrdersPlaced.Values), string.Join(", ", _sessionOrdersStatus.OrdersPlaced.Keys));
			return new(null, $"No order status associated with ClOrderId: '{clOrderId}' was found.");
		}

		return new(orderStatus, string.Empty);
	}

	public async Task<bool> RestartSessionAsync()
	{
		_logger.LogInformation("Starting Session Restart");
		
		if (!_client.IsStarted || !SessionStateValues.IsTradeSnapshotComplete)
		{
			_logger.LogInformation("Client is not started");
			return false;
		}

		await LogoffAsync(async _ =>
		{
			SessionStateValues.Reset();
			_sessionOrdersStatus?.Reset();
			_logger.LogInformation("reset session values done");
				
			SessionStateValues.ShouldLoginAgain = true;
			_logger.LogInformation("login again set to true");
				
			_logger.LogInformation("restart in progress");
			await _client.Reconnect();
		}, onError =>
		{
			_logger.LogWarning("Failed to logoff due to {ErrorMessage}", onError);
			return ValueTask.CompletedTask;
		});

		return true;
	}

	public async Task<DateTime> GetLatestTradeDateAsync(string fullSymbol)
	{
		var status = await GetHealthStatusReport();
		
		if (status.Status != HealthStatus.Healthy)
		{
			throw new CqgClientNotReadyException($"Client is not ready to process this request at {DateTime.UtcNow}");
		}

		if (!SessionStateValues.ContractMetadataBySymbol.TryGetValue(fullSymbol, out var contractMetadata))
		{
			throw new CqgMetadataNotFoundException($"Couldn't find any contract metadata with this symbol: {fullSymbol}");
		}

		var latestTradeDate = contractMetadata.LastTradingDate.GetDatetimeFromMillisecondsToBase(SessionStateValues.BaseDateTime).Date;
		return latestTradeDate;
	}

	public OrdersPlacedResponse GetOrdersPlaced() =>
		new(_sessionOrdersStatus.OrdersPlaced.Values
			.Select(x => new OrderDto(x.Order.ClOrderId, x.GetStatusName()))
			.ToImmutableList());

	public Dictionary<string, List<string>> GetAllContractMetadataSymbols()
	{
		var result = new Dictionary<string, List<string>>();
		foreach (var contractMetadata in SessionStateValues.ContractMetadataBySymbol.Values)
		{
			if (!result.ContainsKey(contractMetadata.InstrumentGroupName))
			{
				result.Add(contractMetadata.InstrumentGroupName, new() { contractMetadata.ContractSymbol });
			}
			else
			{
				result[contractMetadata.InstrumentGroupName].Add(contractMetadata.ContractSymbol);
			}
		}

		return result;
	}

	#endregion Non transactional operations

	#region Private Methods

	public void Send<T>(IMessage<T> clientMsg) where T : IMessage<ClientMsg>, IMessage<T>
	{
		if (_logonStateManager.CurrentState == LogonState.LoggedOff)
		{
			_logger.LogWarning("Attempt to send a message while logged off. {ClientMsg}", clientMsg);
			return;
		}
		
		try
		{
			if (clientMsg is null) return;

			byte[] serverMessageRaw;
			using (var memoryStream = new MemoryStream())
			{
				clientMsg.WriteTo(memoryStream);
				serverMessageRaw = memoryStream.ToArray();
			}

			_client.Send(serverMessageRaw);
			_logger.LogInformation("Message to channel was sent {ClientMsg}", clientMsg);
		}
		catch (Exception ex)
		{
			_logger.LogError("An error occurred when trying to send the message to the channel: {Message} -- {InnerException}", ex.Message, ex.InnerException);
		}
	}

	public Task RequestAsync<T>(
		ClientMsg request,
		Func<T, ValueTask> onSuccess,
		Func<string, ValueTask> onError,
		Func<ServerMsg, bool> matcher,
		Func<ServerMsg, T> extractor,
		Func<T, bool>? completionCheck = null,
		TimeSpan? timeout = null) where T : IMessage
	{
		var requestTimeout = timeout ?? _requestTimeout;
		if (_pendingRequestManager.TryAdd(request, onSuccess, onError, requestTimeout, matcher, extractor, completionCheck))
		{
			Send(request);
		}

		return Task.CompletedTask;
	}

	public Task SubscribeAsync<T>(
		ClientMsg request,
		Func<T, ValueTask> onSuccess,
		Func<string, ValueTask> onError,
		Func<ServerMsg, bool> matcher,
		Func<ServerMsg, T> extractor)
	{
		var uniqueKey = Guid.NewGuid().ToString();

		if (_subscriptionManager.TryAdd(uniqueKey, msg => onSuccess((T)msg), onError, matcher, msg => extractor(msg)))
		{
			Send(request);
		}

		return Task.CompletedTask;
	}
	
	public async Task SubscribeAsync<T>(
		ClientMsg request,
		Func<string, ValueTask> onError,
		params (Func<T, ValueTask> OnSuccess, Func<ServerMsg, bool> Matcher, Func<ServerMsg, T> Extractor)[] subscriptions)
	{
		ArgumentNullException.ThrowIfNull(onError);
		ArgumentNullException.ThrowIfNull(subscriptions);

		var uniqueKeyBase = Guid.NewGuid().ToString();
		var allSubscriptionsAdded = true;

		for (var i = 0; i < subscriptions.Length; i++)
		{
			var (onSuccess, matcher, extractor) = subscriptions[i];
			var uniqueKey = $"{uniqueKeyBase}_{i}";

			if (!_subscriptionManager.TryAdd(uniqueKey, msg => onSuccess((T)msg), onError, matcher, msg => extractor(msg)))
			{
				allSubscriptionsAdded = false;
				break;
			}
		}

		if (allSubscriptionsAdded)
		{
			Send(request);
		}
		else
		{
			await onError("Failed to add all subscriptions");
		}
	}

	public async Task AuthenticateAsync(Func<LogonResult, ValueTask> onSuccess, Func<string, ValueTask> onError)
	{
		ArgumentNullException.ThrowIfNull(_cqgSettings.User);
		ArgumentNullException.ThrowIfNull(_cqgSettings.Password);
		ArgumentNullException.ThrowIfNull(_cqgSettings.ClientAppId);
		ArgumentNullException.ThrowIfNull(_cqgSettings.ClientAppVersion);
		ArgumentNullException.ThrowIfNull(_cqgSettings.PrivateLabel);
		ArgumentNullException.ThrowIfNull(_cqgSettings.ProtocolVersionMajor);
		ArgumentNullException.ThrowIfNull(_cqgSettings.ProtocolVersionMinor);
		
		var result = await _logonStateManager.TryLogonAsync(() => LogonAsync(_cqgSettings.User, _cqgSettings.Password, _cqgSettings.PrivateLabel, _cqgSettings.ClientAppId, _cqgSettings.ClientAppVersion, onSuccess, onError));

		if (!result.Success)
		{
			_logger.LogWarning("Failed to initiate logon due to: {Message}", result.Message);
		}
	}
	
	void CreateSubscription() =>
		_mainSubscription = _client.MessageReceived
			.Select(msg => ServerMsg.Parser.WithExtensionRegistry(new() { DiagnosticSessionToken2Extensions.DiagnosticSessionToken }).ParseFrom(msg.Binary))
			.Where(parsedMsg => parsedMsg != null)
			.TakeWhile(_ => SessionStateValues.Unsubscribe != true)
			.ObserveOn(TaskPoolScheduler.Default)
			.SubscribeSafelyAsync(ProcessServerResponse, _logger);

	bool IsSessionIntegrityMaintained(ServerMsg serverMsg)
	{
		const string integrityCheckLogPrefix = "SessionIntegrity";
		
		// Exclude specific server message types from the session integrity check.
		if (serverMsg.Ping != null || serverMsg.Pong != null || serverMsg.LogonResult != null)
		{
			return true;
		}
        
		if (!serverMsg.HasExtension(DiagnosticSessionToken2Extensions.DiagnosticSessionToken))
		{
			_logger.LogDebug("{LogPrefix}: No session token in server message. Assuming session integrity.", integrityCheckLogPrefix);
			return true;
		}
		
		var sessionToken = serverMsg.GetExtension(DiagnosticSessionToken2Extensions.DiagnosticSessionToken);
		
		_logger.LogDebug("{LogPrefix}: Received session token from server message: '{ReceivedSessionToken}'. Checking against current session state.", integrityCheckLogPrefix, sessionToken);

		switch (string.IsNullOrEmpty(SessionStateValues.SessionToken))
		{
			case false when SessionStateValues.SessionToken != sessionToken:
				_logger.LogWarning("{LogPrefix}: Detected mismatch. Expected '{ExpectedSessionToken}', received '{ReceivedSessionToken}'.", integrityCheckLogPrefix, SessionStateValues.SessionToken, sessionToken);
				return false;
        
			case false:
				_logger.LogDebug("{LogPrefix}: Session token match confirmed. Proceeding with message processing.", integrityCheckLogPrefix);
				break;
		}

		return true;
	}
	
	async Task ProcessServerResponse(ServerMsg serverMsg)
	{
		if (!_environment.IsDevelopment())
		{
			_logger.LogInformation("CQGService Recv {ServerMsg}", serverMsg);
		}
		
		var isIntegrityMaintained = IsSessionIntegrityMaintained(serverMsg);
		if (!isIntegrityMaintained)
		{
			_logger.LogInformation("Restarting socket connection due to session token mismatch.");
			await _client.Reconnect();
			return;
		}
		
		if (serverMsg.LogonResult != null)
		{
			if (serverMsg.LogonResult.ResultCode == (uint)LogonResult.Types.ResultCode.Success)
			{
				_logonStateManager.SetLoggedInState();
			}
			else
			{
				_logonStateManager.SetLoggedOffState();
			}
		}

		if (serverMsg.RestoreOrJoinSessionResult != null)
		{
			if (serverMsg.RestoreOrJoinSessionResult.ResultCode == (uint)RestoreOrJoinSessionResult.Types.ResultCode.Success)
			{
				_logonStateManager.SetLoggedInState();
			}
			else
			{
				_logonStateManager.SetLoggedOffState();
			}
		}
		
		if (serverMsg.LoggedOff != null)
		{
			try
			{
				_logoffOperation.HandleResponse(serverMsg.LoggedOff);
				_logonStateManager.SetLoggedOffState();
			}
			catch (Exception exception)
			{
				_logger.LogError(exception, "Error handling {Message}", nameof(serverMsg.LoggedOff));
			}
		}
		
		if (serverMsg.UserMessages.Any())
		{
			_logger.LogInformation("User messages received. {UserMessages}", serverMsg.UserMessages);
		}
		
		if (serverMsg.Ping != null)
		{
			_pingOperation.HandleResponse(serverMsg.Ping, Send);
		}

		if (serverMsg.Pong != null)
		{
			_logger.LogInformation("Pong received at {UtcNow}", DateTime.UtcNow.ToIso8601());
		}
		
		await _pendingRequestManager.TryInvokeAsync(serverMsg);
		await _subscriptionManager.TryInvokeAsync(serverMsg);
	}
	
	Task DisconnectActionAsync(DisconnectionInfo info)
	{
		switch (info.Type)
		{
			case DisconnectionType.ByServer:
				_logger.LogInformation("Disconnection happened: type: By Server {Info}", info.SerializeObject());
				SessionStateValues.Reset();
				_sessionOrdersStatus?.Reset();
				SessionStateValues.ShouldLoginAgain = true;
				SessionStateValues.IsLoggedOn = true;
				break;

			case DisconnectionType.NoMessageReceived:
				_logger.LogInformation("Disconnection happened: type: No message received {Info}", info.SerializeObject());
				SessionStateValues.IsLoggedOn = false;
				SessionStateValues.ShouldLoginAgain = false;
				break;

			case DisconnectionType.ByUser:
				_logger.LogInformation("Disconnection happened: type: By User {Info}", info.SerializeObject());
				SessionStateValues.Reset();
				_sessionOrdersStatus?.Reset();
				SessionStateValues.ShouldLoginAgain = true;
				break;

			case DisconnectionType.Error:
			case DisconnectionType.Exit:
			case DisconnectionType.Lost:
			default:
				_logger.LogInformation("Disconnection happened: type: Error Exit or Lost {Info}", info.SerializeObject());
				SessionStateValues.Reset();
				_sessionOrdersStatus?.Reset();
				SessionStateValues.ShouldLoginAgain = true;
				break;
		}
		
		_pendingRequestManager.Reset();
		_subscriptionManager.Reset();
		_logonStateManager.SetLoggedOffState();
		
		return Task.CompletedTask;
	}

	async Task ReconnectActionAsync(ReconnectionInfo info)
	{
		_logger.LogInformation("Reconnection happened, type: {Info}", info.SerializeObject());
		
		// Reconnection can occur without a prior disconnection, so we need to log back in.
		_logonStateManager.SetLoggedOffState();
		
		SessionStateValues.SetLoggedOff();
		
		if (string.IsNullOrEmpty(SessionStateValues.SessionToken) || SessionStateValues.ShouldLoginAgain)
		{
			SessionStateValues.Reset();
			_sessionOrdersStatus?.Reset();
			_logger.LogInformation("{OperationName}: Sending message to channel at {UtcNow}", nameof(LogonOperation), DateTime.UtcNow.ToIso8601());

			await AuthenticateAsync(_logonOperation.HandleResponse, onError =>
			{
				_logger.LogWarning("Failed to authenticate due to {ErrorMessage}", onError);
				return ValueTask.CompletedTask;
			});
		}
		else
		{
			_logger.LogInformation("{OperationName}: Sending message to channel at {UtcNow}", nameof(RestoreSessionOperation), DateTime.UtcNow.ToIso8601());

			await RestoreSessionAsync(SessionStateValues.SessionToken, _cqgSettings.PrivateLabel, _cqgSettings.ClientAppId, _restoreSessionOperation.HandleResponse, onError =>
			{
				_logger.LogWarning("Failed to restore session due to {ErrorMessage}", onError);
				return ValueTask.CompletedTask;
			});
		}
	}

	#endregion Private Methods
}
