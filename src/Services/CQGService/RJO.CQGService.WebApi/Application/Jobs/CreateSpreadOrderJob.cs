using Hangfire;
using Hangfire.Server;
using MediatR;
using RJO.CQGService.WebApi.Application.Commands;
using RJO.CQGService.WebApi.Application.Jobs.Options;
using RJO.CQGService.WebApi.Common.Attributes;

namespace RJO.CQGService.WebApi.Application.Jobs;

public class CreateSpreadOrderJob
{
	readonly IMediator _mediator;

	public CreateSpreadOrderJob(IMediator mediator)
	{
		_mediator = mediator;
	}

	[DynamicWindow("trades", "trades")]
	public async Task Perform(CreateSpreadOrderJobOptions jobOptions, PerformContext performContext)
	{
		var orderEvent = jobOptions.CreateSpreadOrderEvent;
		performContext?.AddTags($"{orderEvent.ContractNumber}");

		if (orderEvent.IsMarketOrder)
		{
			var command = new CreateSpreadMarketCommand
			{
				ClOrderId = orderEvent.ContractNumber,
				Duration = (Order.Types.Duration)orderEvent.Duration,
				GoodThruDate = orderEvent.Limit ?? DateTime.UtcNow,
				SpreadSymbol = orderEvent.SpreadSymbol,
				OldContractSymbol = orderEvent.OldContractSymbol,
				NewContractSymbol = orderEvent.NewContractSymbol,
				Qty = new()
				{
					Significand = orderEvent.Quantity,
					Exponent = 0
				},
				Side = orderEvent.IsSell ? Order.Types.Side.Sell : Order.Types.Side.Buy,
				TenantId = orderEvent.TenantId,
				AccountId = orderEvent.AccountId,
				OnBehalfOfUser = jobOptions.OnBehalfOfUser
			};

			await _mediator.Send(command);
		}
		else
		{
			var command = new CreateSpreadLimitCommand
			{
				ClOrderId = orderEvent.ContractNumber,
				Duration = (Order.Types.Duration)orderEvent.Duration,
				Price = orderEvent.LimitPrice ?? 0,
				GoodThruDate = orderEvent.Limit ?? DateTime.UtcNow,
				SpreadSymbol = orderEvent.SpreadSymbol,
				OldContractSymbol = orderEvent.OldContractSymbol,
				NewContractSymbol = orderEvent.NewContractSymbol,
				Qty = new()
				{
					Significand = orderEvent.Quantity,
					Exponent = 0
				},
				Side = orderEvent.IsSell ? Order.Types.Side.Sell : Order.Types.Side.Buy,
				TenantId = orderEvent.TenantId,
				AccountId = orderEvent.AccountId,
				OnBehalfOfUser = jobOptions.OnBehalfOfUser
			};

			await _mediator.Send(command);
		}
	}
}
