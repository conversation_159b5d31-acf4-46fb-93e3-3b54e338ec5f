using Hangfire;
using Hangfire.Server;
using Metadata2;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common;
using RJO.CQGService.Persistence.Database;
using RJO.CQGService.Persistence.Database.Domain;
using RJO.CQGService.WebApi.WebSocket;
using RJO.CQGService.WebApi.WebSocket.Extensions;
using System.Collections.Concurrent;
using WebAPI2;

namespace RJO.CQGService.WebApi.Application.Jobs;

public sealed record DownloadInstrumentJobOptions;

[Queue("default")]
public sealed class DownloadInstrumentJob
{
	readonly IServiceProvider _serviceProvider;
	readonly ILogger<DownloadInstrumentJob> _logger;
	readonly CqgHostService _cqgHostService;
	readonly ConcurrentDictionary<string, List<InformationReport>> _responseDictionary = new ();
	readonly ConcurrentDictionary<string, string> _errorDictionary = new ();

	public DownloadInstrumentJob(IServiceProvider serviceProvider, ILoggerFactory loggerFactory, CqgHostService cqgHostService)
	{
		_serviceProvider = serviceProvider;
		_logger = loggerFactory.CreateLogger<DownloadInstrumentJob>();
		_cqgHostService = cqgHostService;
	}

	public async Task Perform(DownloadInstrumentJobOptions jobOptions, PerformContext performContext)
	{
		_logger.LogInformation("{JobName}: Start to download all active future instruments with SessionToken {token}.", nameof(DownloadInstrumentJob), _cqgHostService.SessionToken);

		using var scope = _serviceProvider.CreateScope();
		var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
		var products = await dbContext.Products
			.AsNoTracking()
			.Where(x => x.IsActive && x.ExchangeSymbol.Length > 0)
			.Select(x => new
			{
				GroupSymbol = x.LimitOrderPrefix + x.Code,
				x.ExchangeSymbol
			})
			.Distinct()
			.ToListAsync();

		// Get responses from CQG
		var msgCount = 0;
		foreach (var product in products)
		{
			_logger.LogInformation("{JobName}: Fetch instruments for {GroupSymbol}", nameof(DownloadInstrumentJob), product.GroupSymbol);
			msgCount += await FetchInstrumentInfo(product.GroupSymbol);
		}

		// Process responses
		_logger.LogInformation("{JobName}: Received {msgCount} report messages.", nameof(DownloadInstrumentJob), msgCount);
		foreach (var product in products)
		{
			_logger.LogInformation("{JobName}: Check if {GroupSymbol} is in CQG Response", nameof(DownloadInstrumentJob), product.GroupSymbol);
			if (!_responseDictionary.ContainsKey(product.GroupSymbol))
				continue;

			_logger.LogInformation("{JobName}: Add {GroupSymbol} to database", nameof(DownloadInstrumentJob), product.GroupSymbol);
			var reportList = _responseDictionary[product.GroupSymbol];
			foreach (var report in reportList) 
				await HandleResponse(Guid.Empty, product.ExchangeSymbol, report.InstrumentGroupReport);
		}

		var expired = await dbContext.FutureInstruments.Where(x => x.LastTradingDate < DateTime.Today).ToListAsync();
		foreach (var ex in expired)
		{
			ex.IsExpired = true;
			ex.UpdatedOn = DateTime.UtcNow;
		}
		dbContext.SaveChanges();

		if (!_errorDictionary.IsEmpty)
		{
			string errorMessages = $"Exception: {nameof(DownloadInstrumentJob)} failed to get " + string.Join(", ", _errorDictionary.Select(kv => $"{kv.Key}: {kv.Value}"));
			throw new Exception(errorMessages);
		}
		_logger.LogInformation("{JobName}: Finished successfully.", nameof(DownloadInstrumentJob));
	}

	async Task<int> FetchInstrumentInfo(string groupSymbol)
	{
		int msgCount = 0;
		using var countdownEvent = new CountdownEvent(1);
		await _cqgHostService.GetInstrumentGroupResolutionAsync(groupSymbol,
			report =>
			{
				msgCount++;
				_logger.LogInformation("{JobName}: Succeed to retrieve contract metadata information for {GroupSymbol}", nameof(DownloadInstrumentJob), groupSymbol);
				_logger.LogInformation("{JobName}: Response for {GroupSymbol}: {report}", nameof(DownloadInstrumentJob), groupSymbol, report);
				if (!_responseDictionary.ContainsKey(groupSymbol))
				{
					var item = new List<InformationReport>();
					_responseDictionary.AddOrUpdate(groupSymbol, k => item, (k, v) => item);
				}
				_responseDictionary[groupSymbol].Add(report);
				if (report.IsReportComplete)
					countdownEvent.Signal();
				return ValueTask.CompletedTask;
			},
			error =>
			{
				var errorMessage = $"{nameof(DownloadInstrumentJob)}: Failed to retrieve contract metadata information for {groupSymbol}, due to {error}";
				_logger.LogError(errorMessage);
				_errorDictionary.AddOrUpdate(groupSymbol, k => errorMessage, (k, v) => errorMessage);
				countdownEvent.Signal();
				return ValueTask.CompletedTask;
			}
		);

		// it takes less than 10 seconds to receive responses from CQG, the job will continue when all responses are received.
		//  the job waits 200 seconds in worst case before timed-out so it will not freeze 
		var completed = countdownEvent.Wait(60.Seconds());

		// Errors
		if (!completed)
			throw new Exception($"Exception: {nameof(DownloadInstrumentJob)} timed out");
		return msgCount;
	}

	private async ValueTask HandleResponse(Guid productId, string exchangeGroupSymbol, InstrumentGroupReport instrumentGroup)
	{
		using var scope = _serviceProvider.CreateScope();
		var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

		foreach (var instrument in instrumentGroup.Instruments)
		{
			var newInstFlag = false;
			var brokerSymbol = instrument.ContractMetadata.ContractSymbol;
			var inst = await dbContext.FutureInstruments.FirstOrDefaultAsync(x => x.BrokerSymbol == brokerSymbol);
			if (inst == null)
			{
				newInstFlag = true;
				inst = new FutureInstrument();
			}

			inst.ProductId = productId;
			inst.Description = instrument.ContractMetadata.Description;
			inst.BrokerSymbol = instrument.ContractMetadata.CqgContractSymbol;
			inst.GroupCode = instrument.ContractMetadata.ShortInstrumentGroupName;
			inst.MonthYear = instrument.ContractMetadata.MaturityMonthYear;
			inst.LastTradingDate = MarketDateHelperExtensions.GetDatetimeFromMillisecondsToBase(instrument.ContractMetadata.LastTradingDate, _cqgHostService.SessionBaseTime);

			inst.Exchange = instrument.ContractMetadata.MicDescription;
			inst.PriceScale = (decimal)instrument.ContractMetadata.CorrectPriceScale;
			inst.TickSize = (decimal)instrument.ContractMetadata.TickSize;
			inst.Currency = instrument.ContractMetadata.Currency;

			if (instrument.ContractMetadata.ListingPeriodType == (int)PeriodType.Month)
				inst.MonthNumber = (int)instrument.ContractMetadata.ListingPeriodValue;
			inst.YearNumber = 2000 + int.Parse(instrument.ContractMetadata.MaturityMonthYear[1..]);
			inst.ExchangeSymbol = exchangeGroupSymbol + instrument.ContractMetadata.MaturityMonthYear;
			inst.UpdatedOn = DateTime.UtcNow;
			// LotFactor = instrument.ContractMetadata.ContractSizeInUnits.Significand * Math.Pow(10, instrument.ContractMetadata.ContractSizeInUnits.Exponent);

			_logger.LogInformation("{JobName}: Upsert {inst} to database", nameof(DownloadInstrumentJob), inst);
			if (newInstFlag)
				dbContext.FutureInstruments.Add(inst);
			else
				dbContext.FutureInstruments.Update(inst);
		}

		_ = await dbContext.SaveChangesAsync();
	}
}

