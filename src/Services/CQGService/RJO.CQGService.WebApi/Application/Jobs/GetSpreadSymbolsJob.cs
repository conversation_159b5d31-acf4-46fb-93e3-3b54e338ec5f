using Hangfire;
using Hangfire.Server;
using RJO.CQGService.Persistence.Database;

namespace RJO.CQGService.WebApi.Application.Jobs;

public sealed record GetSpreadSymbolsJobOptions;

[Queue("default")]
public sealed class GetSpreadSymbolsJob(IServiceProvider serviceProvider)
{
	public async Task Perform(GetSpreadSymbolsJobOptions jobOptions, PerformContext performContext)
	{
		using var scope = serviceProvider.CreateScope();
		var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
		
		
	}
}
