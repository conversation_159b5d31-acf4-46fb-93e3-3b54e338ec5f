using MediatR;
using Microsoft.Extensions.Logging;
using RJO.CQGService.WebApi.WebSocket.Operations;

namespace RJO.CQGService.WebApi.Application.Commands.Handlers;

public class CreateSpreadLimitCommandHandler : IRequestHandler<CreateSpreadLimitCommand>
{
	readonly CreateOrderOperation _createOrderOperation;
	readonly ILogger<CreateSpreadLimitCommandHandler> _logger;

	public CreateSpreadLimitCommandHandler(
		CreateOrderOperation createOrderOperation,
		ILogger<CreateSpreadLimitCommandHandler> logger)
	{
		_createOrderOperation = createOrderOperation;
		_logger = logger;
	}

	public async Task Handle(CreateSpreadLimitCommand request, CancellationToken cancellationToken)
	{
		_logger.LogInformation("Creating spread limit order for {ClOrderId} with spread symbol {SpreadSymbol} at price {Price}", 
			request.ClOrderId, request.SpreadSymbol, request.Price);

		try
		{
			// Use the spread symbol as the instrument for the order
			// CQG will handle the spread order internally
			await _createOrderOperation.CreateOrder(
				request.ClOrderId,
				request.SpreadSymbol, // Use spread symbol instead of individual contract symbols
				(uint)Order.Types.OrderType.Lmt,
				request.Qty,
				request.Side,
				request.Duration,
				request.GoodThruDate,
				request.AccountId,
				request.TenantId,
				request.OnBehalfOfUser,
				request.Price);

			_logger.LogInformation("Successfully created spread limit order for {ClOrderId}", request.ClOrderId);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Failed to create spread limit order for {ClOrderId}", request.ClOrderId);
			throw;
		}
	}
}
