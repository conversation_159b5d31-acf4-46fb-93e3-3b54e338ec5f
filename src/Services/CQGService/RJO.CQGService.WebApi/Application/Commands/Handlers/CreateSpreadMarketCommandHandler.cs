using MediatR;
using Microsoft.Extensions.Logging;
using RJO.CQGService.WebApi.WebSocket.Operations;

namespace RJO.CQGService.WebApi.Application.Commands.Handlers;

public class CreateSpreadMarketCommandHandler : IRequestHandler<CreateSpreadMarketCommand>
{
	readonly CreateOrderOperation _createOrderOperation;
	readonly ILogger<CreateSpreadMarketCommandHandler> _logger;

	public CreateSpreadMarketCommandHandler(
		CreateOrderOperation createOrderOperation,
		ILogger<CreateSpreadMarketCommandHandler> logger)
	{
		_createOrderOperation = createOrderOperation;
		_logger = logger;
	}

	public async Task Handle(CreateSpreadMarketCommand request, CancellationToken cancellationToken)
	{
		_logger.LogInformation("Creating spread market order for {ClOrderId} with spread symbol {SpreadSymbol}", 
			request.ClOrderId, request.SpreadSymbol);

		try
		{
			// Use the spread symbol as the instrument for the order
			// CQG will handle the spread order internally
			await _createOrderOperation.CreateOrder(
				request.ClOrderId,
				request.SpreadSymbol, // Use spread symbol instead of individual contract symbols
				(uint)Order.Types.OrderType.Mkt,
				request.Qty,
				request.Side,
				request.Duration,
				request.GoodThruDate,
				request.AccountId,
				request.TenantId,
				request.OnBehalfOfUser);

			_logger.LogInformation("Successfully created spread market order for {ClOrderId}", request.ClOrderId);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Failed to create spread market order for {ClOrderId}", request.ClOrderId);
			throw;
		}
	}
}
