using MediatR;

namespace RJO.CQGService.WebApi.Application.Commands;

public class CreateSpreadLimitCommand : IRequest
{
	public string ClOrderId { get; set; }
	public Order.Types.Duration Duration { get; set; }
	public decimal Price { get; set; }
	public DateTime GoodThruDate { get; set; }
	public string SpreadSymbol { get; set; }
	public string OldContractSymbol { get; set; }
	public string NewContractSymbol { get; set; }
	public Decimal128 Qty { get; set; }
	public Order.Types.Side Side { get; set; }
	public string TenantId { get; set; }
	public int AccountId { get; set; }
	public string OnBehalfOfUser { get; set; }
}
