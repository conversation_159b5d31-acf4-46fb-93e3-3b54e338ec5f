using MediatR;
using RJO.CQGService.WebApi.Application.Commands;
using RJO.CQGService.Contracts.Market;
using RJO.CQGService.WebApi.WebSocket;
using RJO.CQGService.WebApi.WebSocket.Extensions;
using System.Runtime.Serialization;
using static Order2.Order.Types;
using Decimal = Cqg.Decimal;

namespace RJO.CQGService.WebApi.Application.Handlers;

public class EditOrderCommand : CqgCommandBase, IRequest<EditOrderResponseStatus>
{
	[DataMember]
	public string ClOrderId { get; set; }

	[DataMember]
	public Duration Duration { get; set; }

	[DataMember]
	public Decimal Qty { get; set; }

	[DataMember]
	public decimal LimitPrice { get; set; }

	[DataMember]
	public long StopPrice { get; set; }

	[DataMember]
	public DateTime CreationDate { get; set; }

	[DataMember]
	public string Instrument { get; set; }

	[DataMember]
	public string TenantId { get; set; }

	[DataMember]
	public int AccountId { get; set; }

	public string OnBehalfOf { get; set; }

	public EditOrderCommand()
	{
	}

	public EditOrderCommand(string clOrderId,
		Duration duration,
		long limitPrice,
		long stopPrice,
		Decimal quantity,
		DateTime creationDate,
		string instrument,
		string tenantId,
		int accountId,
		string onBehalfOf)
	{
		ClOrderId = clOrderId;
		Duration = duration;
		Qty = quantity;
		LimitPrice = limitPrice;
		StopPrice = stopPrice;
		CreationDate = creationDate;
		Instrument = instrument;
		TenantId = tenantId;
		AccountId = accountId;
		OnBehalfOf = onBehalfOf;
	}
}

public class EditOrderCommandHandler : IRequestHandler<EditOrderCommand, EditOrderResponseStatus>
{
	readonly ILogger<EditOrderCommandHandler> _logger;
	readonly CqgHostService _cqgHostService;

	public EditOrderCommandHandler(ILogger<EditOrderCommandHandler> logger, CqgHostService cqgHostService)
	{
		_logger = logger ?? throw new ArgumentNullException(nameof(logger));
		_cqgHostService = cqgHostService;
	}

	public async Task<EditOrderResponseStatus> Handle(EditOrderCommand request, CancellationToken cancellationToken)
	{
		_logger.LogInformation($"edit order: start handling request: {request.SerializeObject()} -- at: {DateTime.UtcNow}");
		await _cqgHostService.SendEditOrderAsync(request.AccountId, request.ClOrderId, request.Qty, request.LimitPrice,
			request.StopPrice, request.Duration, request.TenantId, request.OnBehalfOf);

		var editOrderResponseStatus = new EditOrderResponseStatus();
		_logger.LogInformation($"edit order: ending handling request: {request.SerializeObject()} -- at: {DateTime.UtcNow}");
		return await Task.FromResult(editOrderResponseStatus);
	}
}
