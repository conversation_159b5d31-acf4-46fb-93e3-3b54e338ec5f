<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>1135e3c5-9452-4b68-8c3b-ba03c12ef1cf</UserSecretsId>
    <RootNamespace>RJO.CQGMarketData.WebApi</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EFCoreSecondLevelCacheInterceptor" />
    <PackageReference Include="Elastic.Apm.NetCoreAll" />
    <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.AzureAD.UI" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" />
    <PackageReference Include="FluentValidation" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" />
    <PackageReference Include="Websocket.Client" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\BuildingBlocks\RJO.BuildingBlocks.Common\RJO.BuildingBlocks.Common.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\RJO.BuildingBlocks.EventBus\RJO.BuildingBlocks.EventBus.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\RJO.BuildingBlocks.WebCommon\RJO.BuildingBlocks.WebCommon.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\RJO.IntegrationEvents.Commons\RJO.IntegrationEvents.Commons.csproj" />
    <ProjectReference Include="..\RJO.CQGService.Contracts\RJO.CQGService.Contracts.csproj" />
    <ProjectReference Include="..\RJO.CQGService.Persistence.Database\RJO.CQGService.Persistence.Database.csproj" />
  </ItemGroup>

</Project>
