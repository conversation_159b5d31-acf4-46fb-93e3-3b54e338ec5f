using Microsoft.AspNetCore.Mvc;
using RJO.BuildingBlocks.WebCommon.Authorization;
using RJO.CQGMarketData.WebApi.WebSocket;
using System.ComponentModel.DataAnnotations;

namespace RJO.CQGMarketData.WebApi.Controllers;

[ApiController]
[Route("[controller]")]
public class SpreadController : Controller
{
	readonly CqgHostService _cqgHostService;

	public SpreadController(CqgHostService cqgHostService) => _cqgHostService = cqgHostService;

	[HttpGet]
	public IActionResult Latest([Required] string symbol)
	{
		if (!_cqgHostService.IsClientReady)
			return BadRequest(new { ErrorMessage = "Client is not ready or it's restarting" });
		var result = _cqgHostService.GetSpreadPrice(symbol);
		return Ok(new { SpreadPriceResponse = result });
	}
}
