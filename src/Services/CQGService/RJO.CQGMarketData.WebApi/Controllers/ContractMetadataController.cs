using MediatR;
using Microsoft.AspNetCore.Mvc;
using RJO.BuildingBlocks.WebCommon.Authorization;
using RJO.CQGMarketData.WebApi.Application.Handlers;
using RJO.CQGMarketData.WebApi.WebSocket;
using System.ComponentModel.DataAnnotations;

namespace RJO.CQGMarketData.WebApi.Controllers;

[ApiController]
[Route("[controller]")]
public class ContractMetadataController : Controller
{
	ILogger<ContractMetadataController> _logger;
	readonly IMediator _mediator;
	readonly CqgHostService _cqgHostService;

	public ContractMetadataController(ILogger<ContractMetadataController> logger, IMediator mediator, CqgHostService cqgHostService)
	{
		_logger = logger;
		_mediator = mediator;
		_cqgHostService = cqgHostService;
	}

	[HttpGet]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<IActionResult> SearchContractMetadata([Required] string instrument)
	{
		var result = await _mediator.Send(new ContractMetadataCommand(instrument));
		return Ok(result);
	}

	[HttpGet]
	[Route("GetAllSymbols")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public IActionResult SearchAllCurrentMetadata()
	{
		var result = _cqgHostService.GetAllContractMetadataSymbols();
		return Ok(result);
	}

	[HttpGet]
	[Route("GetAllMarketSubscriptions")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public IActionResult SearchAllMarketSubscriptions()
	{
		var result = _cqgHostService.GetAllMarketDataSubscriptions();
		return Ok(result);
	}

	[HttpGet]
	[Route("GetAllTimeBarSubscriptions")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public IActionResult SearchAllTimeBarSubscriptions()
	{
		var result = _cqgHostService.GetAllTimeBarSubscriptions();
		return Ok(result);
	}
}
