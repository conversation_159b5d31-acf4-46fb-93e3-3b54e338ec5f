using Hangfire;
using Hangfire.Server;
using Hangfire.Throttling;
using MediatR;
using RJO.CQGMarketData.WebApi.Application.Handlers;
using RJO.CQGMarketData.WebApi.WebSocket.Extensions;

namespace RJO.CQGMarketData.WebApi.Application.Jobs;

[AutomaticRetry(Attempts = 1, OnAttemptsExceeded = AttemptsExceededAction.Delete, Order = 4)]
[Semaphore("internal-requests", Mode = ThrottlerMode.Release, Order = 4)]
[Queue("default")]
public class CreateTimeBarSubscriptionJob
{
	readonly IMediator _mediator;
	readonly ILogger<CreateTimeBarSubscriptionJob> _logger;

	public CreateTimeBarSubscriptionJob(IMediator mediator, ILogger<CreateTimeBarSubscriptionJob> logger)
	{
		_mediator = mediator;
		_logger = logger;
	}

	[DynamicWindow("internal-requests", "internal-requests", Mode = ThrottlerMode.Release, Order = 4)]
	[Mutex("internal-requests", "internal-requests", Mode = ThrottlerMode.Release, Order = 4)]
	public async Task Perform(CreateTimeBarSubscriptionJobOptions jobOptions, PerformContext performContext)
	{
		_logger.LogInformation(string.Format("{0}: Create time bar message received for symbol {1} with origin {2} at {3}", nameof(CreateTimeBarSubscriptionJob), jobOptions.FullSymbol, jobOptions.Origin, DateTime.UtcNow.ToIso8601()));

		CreateTimeBarSubscriptionCommand command = new() { FullSymbol = jobOptions.FullSymbol };

		var result = await _mediator.Send(command);

		_logger.LogInformation(string.Format("{0}: result of processing {1} with origin {2} was {3} at {4}.", nameof(CreateTimeBarSubscriptionJob), jobOptions.FullSymbol, jobOptions.Origin, result.SerializeObject(),
			DateTime.UtcNow.ToIso8601()));
	}
}
