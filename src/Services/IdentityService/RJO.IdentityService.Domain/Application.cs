using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.Resources;

namespace RJO.IdentityService.Domain;

public class Application : Entity
{
	public string Name { get; private set; }
	public string Code { get; private set; }
	Application() { }

	public Application(string name, string code)
	{
		AssertionConcern.ArgumentStringNotNullOrEmpty(name, GeneralResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentStringNotNullOrEmpty(code, GeneralResources.RequiredValueIsNotPresent);
		Name = name;
		Code = code;

		Id = IdentityGenerator.NewSequentialGuid();
		UpdatedOn = DateTime.Now;
		CreatedOn = DateTime.Now;
		IsActive = true;
	}

	public void ChangeName(string name)
	{
		AssertionConcern.ArgumentStringNotNullOrEmpty(name, GeneralResources.RequiredValueIsNotPresent);
		Name = name;
		UpdatedOn = DateTime.Now;
	}

	public void ChangeCode(string code)
	{
		AssertionConcern.ArgumentStringNotNullOrEmpty(code, GeneralResources.RequiredValueIsNotPresent);
		Code = code;
		UpdatedOn = DateTime.Now;
	}
}
