{"Sentry": {"Environment": "QA"}, "Azure": {"AppConfiguration": {"Endpoint": "https://qas-eastus-appconfiguration.azconfig.io"}, "KeyVault": {"Endpoint": "https://qas-eastus-rjohrvyst-kv.vault.azure.net/"}}, "Authentication": {"ClientId": "903011ae-fdbc-4dc4-a122-e9ca062e63c5"}, "HangfireAuth": {"ClientId": "79974567-a805-44b6-879a-23b91f6fbfff", "TenantId": "fa6d831d-dc2b-41a9-b180-55610cc31345", "ObjectId": "b9cde860-0654-4474-87a8-bfc435400b7c", "Instance": "https://login.microsoftonline.com/", "Domain": "https://qas-eastus-identity-app.azurewebsites.net", "CallbackPath": "/signin-oidc"}, "MicrosoftGraph": {"RedirectUrl": "https://qas-eastus-hrvyst-app.azurewebsites.net/", "TokenUrl": "https://login.microsoftonline.com/0186f940-e4ce-4765-a9d2-dd71bc85f446/oauth2/v2.0/token", "ClientId": "903011ae-fdbc-4dc4-a122-e9ca062e63c5", "Scope": "https://graph.microsoft.com/.default", "GrantType": "client_credentials"}}