using Autofac;
using RJO.IdentityService.Services.IntegrationEvents.EventHandling;

namespace RJO.IdentityService.WebApi.Core.Modules;

public class ApplicationModule : Module
{
	protected override void Load(ContainerBuilder builder)
	{
		builder.RegisterType<SentToCreateUserIntegrationEventHandler>().InstancePerLifetimeScope();
		builder.RegisterType<UserRoleUpdatedIntegrationEventHandler>().InstancePerLifetimeScope();
	}
}
