using Hangfire;
using Hangfire.Pro.Redis;
using IdentityServer4.EntityFramework.DbContexts;
using IdentityServer4.EntityFramework.Entities;
using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using RJO.BuildingBlocks.EventBus;
using RJO.BuildingBlocks.EventBus.Abstractions;
using RJO.IdentityService.Services.Helpers;
using RJO.IdentityService.Services.IntegrationEvents.EventHandling;
using RJO.IdentityService.Services.Jobs;
using RJO.IdentityService.WebApi.Authorization;
using RJO.BuildingBlocks.Common.Hangfire;

namespace RJO.IdentityService.WebApi.Core.Configuration.Extensions;

public static class ConfigurationExtensions
{
	public static IServiceCollection AddAuthorizationHandlers(this IServiceCollection services, IConfiguration configuration)
	{
		services.AddSingleton<IAuthorizationHandler, SecurityAuthorizationHandler>();

		return services;
	}

	public static IServiceCollection AddEventBus(this IServiceCollection services, IConfiguration configuration)
	{
		services.AddSingleton<IEventBus, EventBusServiceBus>(provider =>
		{
			var subscriptionClientName = "security";
			var serviceScopeFactory = provider.GetRequiredService<IServiceScopeFactory>();
			var logger = provider.GetRequiredService<ILogger<EventBusServiceBus>>();
			var eventBusSubscriptionManager = provider.GetRequiredService<InMemoryEventBusSubscriptionsManager>();
			var telemetryClient = provider.GetRequiredService<TelemetryClient>();
			return new(logger, eventBusSubscriptionManager, subscriptionClientName, serviceScopeFactory, telemetryClient, configuration);
		});
		services.AddHttpClient();
		services.AddHostedService<EventBusSubscription>();

		services.AddSingleton<InMemoryEventBusSubscriptionsManager>();

		services.AddScoped<SentToCreateUserIntegrationEventHandler>();
		services.AddScoped<UserRoleUpdatedIntegrationEventHandler>();
		services.AddScoped<UpdateUserIntegrationEventHandler>();
		services.AddTransient<MicrosoftGraphApiClient>();

		return services;
	}

	public static void ConfigureHangfireServices(this IServiceCollection services, IConfiguration configuration)
	{
		var redisConnectionString = configuration.RedisConnectionString();
		var dataShardingPrefix = configuration.DataShardingPrefix();
		var redisStorageOptions = new RedisStorageOptions { Prefix = $"{{{dataShardingPrefix}security}}:" };

		services.AddSingleton(redisStorageOptions);
		services.AddHangfireElasticApmTelemetry();
		services.AddHangfireMetricsTelemetry();

		services.AddHangfire((serviceProvider, x) => x
			.SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
			.UseSimpleAssemblyNameTypeSerializer()
			.UseRecommendedSerializerSettings()
			.UseRedisStorage(redisConnectionString, redisStorageOptions)
			.UseHangfireElasticApmTelemetry(serviceProvider)
			.UseHangfireMetricsTelemetry(serviceProvider));

		services.AddHangfireServer(options =>
		{
			options.WorkerCount = 1;
			options.ServerName = Environment.MachineName;
		});
	}

	public static IServiceCollection AddJobs(this IServiceCollection services, IConfiguration configuration)
	{
		services.AddScoped<CreateUserJob>();
		services.AddScoped<UpdateUserRoleJob>();

		return services;
	}

	public static IServiceCollection ConfigureSwagger(this IServiceCollection services, string version, string title, string description, string xmlPath)
	{
		services.AddSwaggerGen(c =>
		{
			c.CustomSchemaIds(x => x.FullName);
			c.SwaggerDoc("v1", new()
			{
				Version = version,
				Title = title,
				Description = description,
				Contact = new()
				{
					Name = "RJO",
					Email = "<EMAIL>",
					Url = new("https://www.rjobrien.com/")
				}
			});
			c.AddSecurityDefinition("Bearer", new()
			{
				Description = @"JWT Authorization header using the Bearer scheme. \r\n\r\n 
                      Enter 'Bearer' [space] and then your token in the text input below.
                      \r\n\r\nExample: 'Bearer 12345abcdef'",
				Name = "Authorization",
				In = ParameterLocation.Header,
				Type = SecuritySchemeType.ApiKey,
				BearerFormat = "JWT",
				Scheme = "Bearer"
			});
			c.AddSecurityRequirement(new()
			{
				{
					new()
					{
						Reference = new()
						{
							Type = ReferenceType.SecurityScheme,
							Id = "Bearer"
						},
						Scheme = "oauth2",
						Name = "Bearer",
						In = ParameterLocation.Header
					},
					new List<string>()
				}
			});
			c.IncludeXmlComments(xmlPath);
			//c.OperationFilter<SecurityRequirementsOperationFilter>();
			//c.OperationFilter<AppendAuthorizeToSummaryOperationFilter>();
		});

		return services;
	}
}

public class ConfigureKeyClientOptions : IConfigureOptions<ClientsConfiguration>
{
	readonly IServiceScopeFactory _serviceScopeFactory;

	public ConfigureKeyClientOptions(IServiceScopeFactory serviceScopeFactory) => _serviceScopeFactory = serviceScopeFactory;

	public void Configure(ClientsConfiguration options)
	{
		using (var scope = _serviceScopeFactory.CreateScope())
		{
			var provider = scope.ServiceProvider;
			using (var dbContext = provider.GetRequiredService<ConfigurationDbContext>())
			{
				options.Clients = dbContext.Clients.ToList();
			}
		}
	}
}

public class ClientsConfiguration
{
	public IList<Client> Clients { get; set; }
}
