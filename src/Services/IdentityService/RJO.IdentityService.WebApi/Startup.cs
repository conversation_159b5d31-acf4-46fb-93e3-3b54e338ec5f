using Hangfire;
using HealthChecks.UI.Client;
using IdentityServer4;
using IdentityServer4.AccessTokenValidation;
using IdentityServer4.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Common.HealthChecks;
using RJO.BuildingBlocks.WebCommon;
using RJO.BuildingBlocks.WebCommon.Configuration;
using RJO.BuildingBlocks.Common.Extensions;
using RJO.BuildingBlocks.Common.Hangfire;
using RJO.IdentityService.Domain;
using RJO.IdentityService.Domain.Tenants;
using RJO.IdentityService.Persistence.DataBase;
using RJO.IdentityService.WebApi.Authorization;
using RJO.IdentityService.WebApi.Core.Configuration.Extensions;
using RJO.IdentityService.WebApi.Identity;
using RJO.MultiTenancyServer.AspNetCore.Extensions;
using RJO.MultiTenancyServer.EFCore.Extensions;
using System.Reflection;
using OpenTelemetry.Metrics;

namespace RJO.IdentityService.WebApi;

static class Startup
{
	public const string SwaggerVersion = "S9.0.0.1";

	public static IServiceCollection ConfigureApplication(this IServiceCollection services, IConfiguration configuration, IHostEnvironment hostEnvironment)
	{
		services.AddAllElasticApm();
		services.AddInfrastructureServices(configuration, hostEnvironment);
		services.AddHttpContextAccessor();
		services.AddLocalization();
		services.AddDataProtection()
			.SetApplicationName("RJOHrvystEdge"); //we should use data protection strategy here
		services.Configure<RouteOptions>(config => { config.LowercaseUrls = true; });
		services.AddCustomApplicationInsightsTelemetry(configuration, hostEnvironment);

		services
			.AddHealthChecks()
			.AddApplicationHealthChecks<ApplicationDbContext>(configuration);

		var azureADConfiguration = configuration.GetSection("Authentication").Get<AzureADConfiguration>();

		services.AddSingleton<ICorsPolicyService>(container =>
		{
			var logger = container.GetRequiredService<ILogger<DefaultCorsPolicyService>>();
			return new DefaultCorsPolicyService(logger) { AllowAll = true };
		});

		services.AddControllers()
			.ConfigureModelBindingExceptionHandling();

		services.ConfigureHangfireServices(configuration);

		services.AddAuthentication().AddAzureAD(options =>
		{
			options.Instance = configuration["HangfireAuth:Instance"];
			options.Domain = configuration["HangfireAuth:Domain"];
			options.TenantId = configuration["HangfireAuth:TenantId"];
			options.ClientId = configuration["HangfireAuth:ClientId"];
			options.CallbackPath = configuration["HangfireAuth:CallbackPath"];
		});

		services
			.AddEventBus(configuration)
			.AddJobs(configuration);

		var connectionString = configuration.DatabaseDefaultConnectionString();
		services.AddDbContext<ApplicationDbContext>(options =>
		{
			options.UseSqlServer(connectionString, x =>
			{
				x.MigrationsHistoryTable("EFMigration", "Security");
			});
		});

		var xmlPath = Path.Combine(AppContext.BaseDirectory, $"{Assembly.GetExecutingAssembly().GetName().Name}.xml");
		services.ConfigureSwagger(SwaggerVersion, "RJO Identity Service", "RJO Identity Services", xmlPath);

		var signingCredentials = configuration.GetSigningCredentials();

		services.AddAuthentication(IdentityServerAuthenticationDefaults.AuthenticationScheme)
			.AddOpenIdConnect("oidc", "Azure AD", options =>
			{
				options.SignInScheme = IdentityServerConstants.ExternalCookieAuthenticationScheme;
				options.SignOutScheme = IdentityServerConstants.SignoutScheme;
				options.Authority = azureADConfiguration.Authority;
				options.ClientId = azureADConfiguration.ClientId;
				options.CallbackPath = "/signin-microsoft";
				options.Prompt = "select_account";

				options.ClientSecret = azureADConfiguration.ClientSecret;
				options.ResponseType = "code";
				options.Scope.Add("profile");
				options.Scope.Add("email");
				options.SignInScheme = "Identity.External";

				options.RequireHttpsMetadata = false;

				options.TokenValidationParameters = new()
				{
					ValidateIssuerSigningKey = true,
					ValidateIssuer = false,
					ValidateAudience = false,
					IssuerSigningKey = signingCredentials.Key
				};
			})
			.AddJwtBearer(options =>
			{
				options.RequireHttpsMetadata = false;
				options.TokenValidationParameters = new()
				{
					ValidateIssuerSigningKey = false,
					ValidateIssuer = false,
					ValidateAudience = false,
					ValidateLifetime = false,
					ValidateTokenReplay = false,
					ValidateActor = false,
					IssuerSigningKey = signingCredentials.Key
				};
			});

		services.AddAuthorization(options =>
		{
			options.AddPolicy(Claims.Profile, policyAdmin =>
			{
				policyAdmin.RequireClaim("role", Claims.Profile);
			});
			options.AddPolicy("HangfirePolicy", builder =>
			{
				builder
					.AddAuthenticationSchemes("AzureAD")
					.RequireAuthenticatedUser();
			});
		});

		services.AddOpenTelemetry()
			.WithMetrics(builder =>
			{
				builder.AddAspNetCoreInstrumentation()
					.AddHangfireInstrumentation()
					.AddPrometheusExporter();
			});

		services.AddIdentity<ApplicationUser, ApplicationRole>().AddEntityFrameworkStores<ApplicationDbContext>();
		services.AddIdentityServer(o => o.IssuerUri = azureADConfiguration.Authority)
			.AddSigningCredential(signingCredentials)
			.AddConfigurationStore(options =>
			{
				options.DefaultSchema = "Identity";
				options.ConfigureDbContext = builder =>
					builder.UseSqlServer(connectionString, optionsBuilder =>
						optionsBuilder.MigrationsAssembly(typeof(ApplicationDbContext).Namespace));
			})
			.AddOperationalStore(options =>
			{
				options.DefaultSchema = "Identity";
				options.ConfigureDbContext = builder =>
					builder.UseSqlServer(connectionString,
						sql => sql.MigrationsAssembly(typeof(ApplicationDbContext).Namespace));
				options.EnableTokenCleanup = true;
				options.TokenCleanupInterval = 30;
			})
			.AddAspNetIdentity<ApplicationUser>();

		services.AddTransient<ITokenCreationService, KeyVaultTokenCreationService>();

		services.Configure<CookiePolicyOptions>(options =>
		{
			options.MinimumSameSitePolicy = SameSiteMode.Lax;
			options.OnAppendCookie = cookieContext => CheckSameSite(cookieContext.Context, cookieContext.CookieOptions);
			options.OnDeleteCookie = cookieContext => CheckSameSite(cookieContext.Context, cookieContext.CookieOptions);
		});

		services.AddLogging(builder => builder.AddConsole());

		services.AddAuthorization(options =>
		{
			options.AddPolicy(AuthorizationPolicyNames.Security, policy => policy.Requirements.Add(new SecurityAuthorizationRequirement()));
		});

		services.AddAuthorizationHandlers(configuration);
		services.AddMultiTenancy<ApplicationTenant, Guid>()
			.AddClaimParser(CustomClaimTypes.Tenant)
			.AddEntityFrameworkStore<ApplicationDbContext, ApplicationTenant, Guid>();

		return services;
	}

	static void CheckSameSite(HttpContext httpContext, CookieOptions options)
	{
		if (options.SameSite == SameSiteMode.None)
		{
			var userAgent = httpContext.Request.Headers["User-Agent"].ToString();
			if (DisallowsSameSiteNone(userAgent))
			{
				options.SameSite = SameSiteMode.Unspecified;
			}
		}
	}

	static bool DisallowsSameSiteNone(string userAgent)
	{
		// Cover all iOS based browsers here. This includes:
		// - Safari on iOS 12 for iPhone, iPod Touch, iPad
		// - WkWebview on iOS 12 for iPhone, iPod Touch, iPad
		// - Chrome on iOS 12 for iPhone, iPod Touch, iPad
		// All of which are broken by SameSite=None, because they use the iOS networking stack
		if (userAgent.Contains("CPU iPhone OS 12", StringComparison.Ordinal) || userAgent.Contains("iPad; CPU OS 12", StringComparison.Ordinal))
		{
			return true;
		}

		// Cover Mac OS X based browsers that use the Mac OS networking stack. This includes:
		// - Safari on Mac OS X.
		// This does not include:
		// - Chrome on Mac OS X
		// Because they do not use the Mac OS networking stack.
		if (userAgent.Contains("Macintosh; Intel Mac OS X 10_14", StringComparison.Ordinal) &&
		    userAgent.Contains("Version/", StringComparison.Ordinal) && userAgent.Contains("Safari", StringComparison.Ordinal))
		{
			return true;
		}

		// Cover Chrome 50-69, because some versions are broken by SameSite=None,
		// and none in this range require it.
		// Note: this covers some pre-Chromium Edge versions,
		// but pre-Chromium Edge does not require SameSite=None.
		if (userAgent.Contains("Chrome/5", StringComparison.Ordinal) || userAgent.Contains("Chrome/6", StringComparison.Ordinal))
		{
			return true;
		}

		return false;
	}

	public static IApplicationBuilder ConfigureApplication(this IApplicationBuilder app)
	{
		var env = app.ApplicationServices.GetRequiredService<IHostEnvironment>();

		if (env.IsDevelopment() || env.IsLocal())
		{
			app.UseDeveloperExceptionPage();
		}

		app.UseHttpsRedirection();

		app.UseRouting();

		app.UseCors(config => config
			.AllowAnyOrigin()
			.AllowAnyHeader()
			.AllowAnyMethod());

		app.UseIdentityServer();
		app.UseAuthentication();
		app.UseAuthorization();

		app.UseMultiTenancy<ApplicationTenant>();

		app.ConfigureExceptionHandler();
		
		if (env.IsDevelopment() || env.IsLocal())
		{
			app.UseSwagger();
			app.UseSwaggerUI(c =>
			{
				c.SwaggerEndpoint("/swagger/v1/swagger.json", "RJO Identity Service " + SwaggerVersion);
				c.RoutePrefix = string.Empty;
			});
		}
		
		app.UseEndpoints(endpoints =>
		{
			endpoints.MapHealthChecks("/health", new() { ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse });
			endpoints.MapControllers();
			endpoints.MapHangfireDashboardWithLock()
				.RequireAuthorization("HangfirePolicy");
			endpoints.MapObservability();
		});
		app.UseCookiePolicy();

		return app;
	}
}
