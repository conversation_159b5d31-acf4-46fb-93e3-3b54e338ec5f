{
  "Sentry": {
    "Environment": "Local"
  },
  "Azure": {
    "AppConfiguration": {
      "Endpoint": "https://dv-eastus-appconfiguration.azconfig.io"
    },
    "KeyVault": {
      "Endpoint": "https://dv-eastus-rjohrvyst-kv.vault.azure.net/"
    }
  },
  "Authentication": {
    "ClientId": "3b9ca8b4-5216-4e92-9111-a0591f802831"
  },
  //Looks like there is no identity service app set up for development, if we do set one up we will have to update the hangfire auth section with the appropriate values
  "HangfireAuth": {
    "ClientId": "79974567-a805-44b6-879a-23b91f6fbfff",
    "TenantId": "fa6d831d-dc2b-41a9-b180-55610cc31345",
    "ObjectId": "b9cde860-0654-4474-87a8-bfc435400b7c",
    "Instance": "https://login.microsoftonline.com/",
    "Domain": "https://qas-eastus-identity-app.azurewebsites.net",
    "CallbackPath": "/signin-oidc"
  },
  "MicrosoftGraph": {
    "RedirectUrl": "https://dv-rjohrvystdemo-app.azurewebsites.net/",
    "TokenUrl": "https://login.microsoftonline.com/0186f940-e4ce-4765-a9d2-dd71bc85f446/oauth2/v2.0/token",
    "ClientId": "3b9ca8b4-5216-4e92-9111-a0591f802831",
    "Scope": "https://graph.microsoft.com/.default",
    "GrantType": "client_credentials"
  }
}