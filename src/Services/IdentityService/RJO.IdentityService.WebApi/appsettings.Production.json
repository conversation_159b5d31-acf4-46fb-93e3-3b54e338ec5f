{"Sentry": {"Environment": "Production"}, "Azure": {"AppConfiguration": {"Endpoint": "https://pr-eastus-appconfiguration.azconfig.io"}, "KeyVault": {"Endpoint": "https://preastushrvystkv.vault.azure.net/"}}, "Authentication": {"ClientId": "783ec63f-bede-40cb-9760-8d962868af42"}, "HangfireAuth": {"ClientId": "a1afeaf5-2cf6-4001-b395-5990e9b3bea6", "TenantId": "fa6d831d-dc2b-41a9-b180-55610cc31345", "ObjectId": "b8f16237-2fa6-4ab0-b296-b55dc5d15c7a", "Instance": "https://login.microsoftonline.com/", "Domain": "https://pr-eastus-identity-app.azurewebsites.net", "CallbackPath": "/signin-oidc"}, "MicrosoftGraph": {"RedirectUrl": "https://hedge.hrvyst.com/", "TokenUrl": "https://login.microsoftonline.com/0186f940-e4ce-4765-a9d2-dd71bc85f446/oauth2/v2.0/token", "ClientId": "783ec63f-bede-40cb-9760-8d962868af42", "Scope": "https://graph.microsoft.com/.default", "GrantType": "client_credentials"}}