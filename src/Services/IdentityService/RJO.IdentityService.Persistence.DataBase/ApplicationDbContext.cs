using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RJO.IdentityService.Domain;
using RJO.IdentityService.Domain.Tenants;
using RJO.IdentityService.Persistence.DataBase.Configuration;
using RJO.MultiTenancyServer.Core;
using RJO.MultiTenancyServer.Core.Options;
using RJO.MultiTenancyServer.EFCore;
using RJO.MultiTenancyServer.EFCore.Extensions;

namespace RJO.IdentityService.Persistence.DataBase;

public class ApplicationDbContext : IdentityDbContext<ApplicationUser,
		ApplicationRole,
		Guid,
		ApplicationUserClaim,
		ApplicationUserRole,
		ApplicationUserLogin,
		ApplicationRoleClaim,
		IdentityUserToken<Guid>>,
	ITenantDbContext<ApplicationTenant, Guid>
{
	readonly ITenancyContext<ApplicationTenant> _tenancyContext;
	static TenancyModelState<Guid> _tenancyModelState;
	readonly ILogger _logger;

	public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options,
		ITenancyContext<ApplicationTenant> tenancyContext,
		ILogger<ApplicationDbContext> logger) : base(options)
	{
		_tenancyContext = tenancyContext;
		_logger = logger ?? throw new ArgumentNullException(nameof(logger));
	}

	public DbSet<ApplicationTenant> Tenants { get; set; }
	public DbSet<ApplicationUser> Users { get; set; }

	protected override void OnModelCreating(ModelBuilder builder)
	{
		base.OnModelCreating(builder);

		var tenantStoreOptions = new TenantStoreOptions();
		builder.ConfigureTenantContext<ApplicationTenant, Guid>(tenantStoreOptions);

		// Add multi-tenancy support to model.
		var tenantReferenceOptions = new TenantReferenceOptions();
		builder.HasTenancy(tenantReferenceOptions, out _tenancyModelState);

		builder.Entity<ApplicationTenant>(b =>
		{
			b.Property(t => t.DisplayName).HasMaxLength(256);
		});

		builder.Entity<ApplicationTenant>().ToTable("Tenant", "Security");

		builder.Entity<ApplicationRole>(t => { t.HasTenancy(() => _tenancyContext.Tenant.Id, _tenancyModelState, hasIndex: false); });
		builder.Entity<ApplicationRoleClaim>(t => { t.HasTenancy(() => _tenancyContext.Tenant.Id, _tenancyModelState, hasIndex: false); });
		builder.Entity<ApplicationUserClaim>(t => { t.HasTenancy(() => _tenancyContext.Tenant.Id, _tenancyModelState, hasIndex: false); });
		builder.Entity<ApplicationUserRole>(t => { t.HasTenancy(() => _tenancyContext.Tenant.Id, _tenancyModelState, hasIndex: false); });

		ModelConfig(builder);
	}

	static void ModelConfig(ModelBuilder builder)
	{
		builder.HasDefaultSchema("Security");

		builder.ApplyConfiguration(new ApplicationConfiguration());
		builder.ApplyConfiguration(new ApplicationClaimConfiguration());
		builder.ApplyConfiguration(new ApplicationGrantConfiguration());

		builder.ApplyConfiguration(new ApplicationUserConfiguration());
		builder.ApplyConfiguration(new ApplicationRoleConfiguration());
		builder.ApplyConfiguration(new ApplicationUserRoleConfiguration());


		builder.ApplyConfiguration(new ApplicationUserLoginConfiguration());
		builder.ApplyConfiguration(new ApplicationUserClaimConfiguration());
		builder.ApplyConfiguration(new ApplicationRoleClaimConfiguration());

		builder.ApplyConfiguration(new TenantSettingConfiguration());

		builder.Entity<IdentityUserToken<Guid>>().ToTable("UserTokens");
	}

	public override int SaveChanges(bool acceptAllChangesOnSuccess)
	{
		// Ensure multi-tenancy for all tenantable entities.
		this.EnsureTenancy(_tenancyContext?.Tenant?.Id, _tenancyModelState, _logger);
		return base.SaveChanges(acceptAllChangesOnSuccess);
	}

	public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default)
	{
		// Ensure multi-tenancy for all tenantable entities.
		this.EnsureTenancy(_tenancyContext?.Tenant?.Id, _tenancyModelState, _logger);
		return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
	}
}
