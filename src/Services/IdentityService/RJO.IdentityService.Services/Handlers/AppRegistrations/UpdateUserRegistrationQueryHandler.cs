using MediatR;
using RJO.IdentityService.Services.Helpers;

namespace RJO.IdentityService.Services.Handlers.AppRegistrations;

public class UpdateUserRegistrationQuery : IRequest<Unit>
{
	public Guid GraphId { get; set; }
	public string EmailAddress { get; set; }
	public string GivenName { get; set; }
	public string Surname { get; set; }
}

public class UpdateUserRegistrationQueryHandler : IRequestHandler<UpdateUserRegistrationQuery, Unit>
{
	readonly MicrosoftGraphApiClient _client;

	public UpdateUserRegistrationQueryHandler(MicrosoftGraphApiClient client) => _client = client;

	public async Task<Unit> Handle(UpdateUserRegistrationQuery request, CancellationToken cancellationToken)
	{
		await _client.GetToken();
		await _client.UpdateUser(request.GraphId, request.GivenName, request.Surname, request.EmailAddress);
		return Unit.Value;
	}
}
