using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.IdentityService.Persistence.DataBase;
using RJO.IdentityService.Services.Helpers;

namespace RJO.IdentityService.Services.Handlers.AppRegistrations;

public class RegisterUserCommand : IRequest<Unit>, ITransactionalRequest
{
	public string EmailAddress { get; set; }
	public string DisplayName { get; set; }
	public string GivenName { get; set; }
	public string Surname { get; set; }
}

public class RegisterUserQueryHandler : IRequestHandler<RegisterUserCommand, Unit>
{
	readonly ApplicationDbContext _dbContext;
	readonly MicrosoftGraphApiClient _client;

	public RegisterUserQueryHandler(ApplicationDbContext dbContext, MicrosoftGraphApiClient client)
	{
		_dbContext = dbContext;
		_client = client;
	}

	public async Task<Unit> Handle(RegisterUserCommand request, CancellationToken cancellationToken)
	{
		var user = await _dbContext.Users.FirstOrDefaultAsync(a => a.Email == request.EmailAddress, cancellationToken);
		await _client.GetToken();
		var newUserId = await _client.RegisterUser(request.EmailAddress, request.DisplayName, request.GivenName, request.Surname);
		user.GraphId = newUserId;
		await _dbContext.SaveChangesAsync(cancellationToken);
		await _client.UpdateUser(newUserId, request.GivenName, request.Surname);
		return Unit.Value;
	}
}
