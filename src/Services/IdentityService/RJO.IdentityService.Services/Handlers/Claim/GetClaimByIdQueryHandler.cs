using MediatR;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.Resources;
using RJO.IdentityService.Persistence.Repositories;
using RJO.IdentityService.Services.DTO.Claim;

namespace RJO.IdentityService.Services.Handlers.Claim;

public class GetClaimByIdQuery : IRequest<ClaimItemDto>
{
	public string Name { get; set; }
}

public class GetClaimByIdQueryHandler : IRequestHandler<GetClaimByIdQuery, ClaimItemDto>
{
	readonly ILogger<GetClaimByIdQueryHandler> _logger;
	readonly ApplicationClaimRepository _claimRepository;

	public GetClaimByIdQueryHandler(ILogger<GetClaimByIdQueryHandler> logger, ApplicationClaimRepository claimRepository)
	{
		_logger = logger;
		_claimRepository = claimRepository;
	}

	public async Task<ClaimItemDto> Handle(GetClaimByIdQuery request, CancellationToken cancellationToken)
	{
		AssertionConcern.ArgumentIsNotNull(request.Name, GeneralResources.RequiredValueIsNotPresent);
		var claim = (await _claimRepository.GetAllEntities()).Where(x => x.Name == request.Name).FirstOrDefault();
		AssertionConcern.ArgumentIsNotNull(claim, $"Claim with name '{request.Name}' does not exist");
		//this._logger.OrderAll();
		return new()
		{
			Id = claim.Id,
			ApplicationId = claim.ApplicationId,
			Name = claim.Name
		};
	}
}
