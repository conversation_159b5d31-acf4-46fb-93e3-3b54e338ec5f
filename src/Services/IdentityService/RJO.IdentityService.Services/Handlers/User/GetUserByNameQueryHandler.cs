using MediatR;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.Resources;
using RJO.IdentityService.Domain;
using RJO.IdentityService.Persistence.Repositories;

namespace RJO.IdentityService.Services.Handlers.User;

public class GetUserByNameQuery : IRequest<ApplicationUser>
{
	public string Name { get; set; }
}

public class GetUserByNameQueryHandler : IRequestHandler<GetUserByNameQuery, ApplicationUser>
{
	readonly ILogger<GetUserByNameQueryHandler> _logger;
	readonly ApplicationUserRepository _applicationUserRepository;

	public GetUserByNameQueryHandler(ApplicationUserRepository applicationUserRepository, ILogger<GetUserByNameQueryHandler> logger)
	{
		_applicationUserRepository = applicationUserRepository;
		_logger = logger;
	}

	public async Task<ApplicationUser> Handle(GetUserByNameQuery request, CancellationToken cancellationToken)
	{
		AssertionConcern.ArgumentIsNotNull(request, GeneralResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentStringNotNullOrEmpty(request.Name, GeneralResources.RequiredValueIsNotPresent);
		var query = (await _applicationUserRepository.GetAllEntities()).Where(x => x.Name == request.Name);
		var user = query.FirstOrDefault();
		AssertionConcern.ArgumentIsNotNull(user, "User  was not found");
		return user;
	}
}
