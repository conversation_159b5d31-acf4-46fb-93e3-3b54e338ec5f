using MediatR;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.Resources;
using RJO.IdentityService.Persistence.Repositories;

namespace RJO.IdentityService.Services.Handlers;

public class GetSystemFlagsQuery : IRequest<List<string>>
{
	public Guid TenantId { get; set; }
}

public class GetSystemFlagsQueryHandler : IRequestHandler<GetSystemFlagsQuery, List<string>>
{
	readonly TenantSettingRepository _tenantSettingRepository;

	public GetSystemFlagsQueryHandler(TenantSettingRepository tenantSettingRepository) => _tenantSettingRepository = tenantSettingRepository;

	public Task<List<string>> Handle(GetSystemFlagsQuery request, CancellationToken cancellationToken)
	{
		AssertionConcern.ArgumentIsNotNull(request, GeneralResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotEmpty(request.TenantId, GeneralResources.RequiredValueIsNotPresent);
		var data = new List<string>(1);
		if (_tenantSettingRepository.GetValue<bool>("OmsOnly", request.TenantId))
		{
			data.Add("ContractService.Contract.OMSOnly");
		}

		if (_tenantSettingRepository.GetValue<bool>("ContractService.Allow.ContractNTC", request.TenantId))
		{
			data.Add("ContractService.Allow.ContractNTC");
		}

		if (_tenantSettingRepository.GetValue<bool>("EnableFutures", request.TenantId))
		{
			data.Add("ContractService.Contract.EnableFutures");
		}
		if (_tenantSettingRepository.GetValue<bool>("ContractService.originatorBidsQuotes.Tenant.View", request.TenantId))
		{
			data.Add("ContractService.originatorBidsQuotes.Tenant.View");
		}
		return Task.FromResult(data);
	}
}
