using Hangfire.Server;
using MediatR;
using RJO.IdentityService.Services.Handlers.AppRegistrations;
using RJO.IdentityService.Services.Handlers.User;

namespace RJO.IdentityService.Services.Jobs;

public class CreateUserJob
{
	readonly IMediator _mediator;

	public CreateUserJob(IMediator mediator) => _mediator = mediator;

	public async Task Perform(CreateUserJobOptions jobOptions, PerformContext performContext)
	{
		var createUserEvent = jobOptions.SentToCreateUserEvent;
		var command = new CreateUserRoleDefaultCommand
		{
			UserName = createUserEvent.UserName,
			LastName = createUserEvent.LastName,
			Name = createUserEvent.FirstName,
			Email = createUserEvent.Email,
			RoleId = createUserEvent.RoleId,
			TenantId = createUserEvent.TenantId
		};
		await _mediator.Send(command);

		var registerCommand = new RegisterUserCommand
		{
			EmailAddress = createUserEvent.Email,
			DisplayName = createUserEvent.UserName,
			GivenName = createUserEvent.FirstName,
			Surname = createUserEvent.LastName,
		};
		await _mediator.Send(registerCommand);
	}
}
