using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Net.Http.Headers;

namespace RJO.IdentityService.Services.Helpers;

public class MicrosoftGraphApiClient
{
	const string RequestUrl = "https://graph.microsoft.com/v1.0";

	readonly string _tokenUrl; 
	readonly IHttpClientFactory _factory;
    readonly IConfiguration _configuration;
	RegisteredUserToken _registerToken;
	InviteResponse _inviteResponse;

	public MicrosoftGraphApiClient(IConfiguration configuration, IHttpClientFactory clientFactory)
	{
        _configuration = configuration;
		_factory = clientFactory;
		_tokenUrl = _configuration.GetValue<string>("MicrosoftGraph:TokenUrl");
	}

	public async Task GetToken()
	{
		var request = new List<KeyValuePair<string, string>>();
		request.Add(new("client_id", _configuration.GetValue<string>("MicrosoftGraph:ClientId")));
		request.Add(new("scope", _configuration.GetValue<string>("MicrosoftGraph:Scope")));
		request.Add(new("grant_type", _configuration.GetValue<string>("MicrosoftGraph:GrantType")));
		request.Add(new("client_secret", _configuration.GetValue<string>("MicrosoftGraph:ClientSecret")));

		string result;

		using var client = _factory.CreateClient();
		using var message = new HttpRequestMessage(HttpMethod.Post, _tokenUrl) { Content = new FormUrlEncodedContent(request) };
		var response = await client.SendAsync(message);
		result = await response.Content.ReadAsStringAsync();
		_registerToken = JsonConvert.DeserializeObject<RegisteredUserToken>(result);
	}

	public async Task<Guid> RegisterUser(string emailAddress, string displayName, string givenName, string surname)
	{
		var request = new
		{
			invitedUserEmailAddress = emailAddress,
			inviteRedirectUrl = _configuration.GetValue<string>("MicrosoftGraph:RedirectUrl"),
			displayName,
			givenName,
			surname,
			sendInvitationMessage = true
		};

		string result;

		using var client = _factory.CreateClient();

		client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _registerToken.AccessToken);
		using var message = new HttpRequestMessage();

		message.Headers.Add("Accept", "application/json");
		message.Content = new StringContent(JsonConvert.SerializeObject(request), System.Text.Encoding.UTF8, "application/json");
		message.Method = HttpMethod.Post;
		message.RequestUri = new($"{RequestUrl}/invitations");
		var response = await client.SendAsync(message);
		result = await response.Content.ReadAsStringAsync();
		_inviteResponse = JsonConvert.DeserializeObject<InviteResponse>(result);

		return _inviteResponse.InvitedUser.Id;
	}

    public async Task UpdateUser(Guid userId, string givenName, string surname)
    {
        var request = new
		{
            givenName,
            surname
        };

        await UpdateUser(userId, request);
    }

	public async Task UpdateUser(Guid userId, string givenName, string surname, string email)
	{
		var request = new
        {
            givenName,
            surname,
            mail = email,
            userPrincipalName = $"{email.Replace('@', '_')}#EXT#@hrvysthedge.onmicrosoft.com"
        };
        await UpdateUser(userId, request);
	}

    public async Task UpdateUser(Guid userId, object request)
    {
		using var client = _factory.CreateClient();

        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _registerToken.AccessToken);
		using var message = new HttpRequestMessage();
		message.Headers.Add("Accept", "application/json");
        message.Content = new StringContent(JsonConvert.SerializeObject(request), System.Text.Encoding.UTF8, "application/json");
        message.Method = HttpMethod.Patch;
        message.RequestUri = new Uri($"{RequestUrl}/users/{userId}");
        var response = await client.SendAsync(message);
        _ = await response.Content.ReadAsStringAsync();
    }
}

public class RegisterUserDto
{
	public string InvitedUserEmailAddress { get; set; }
	public string InviteRedirectUrl { get; set; }
	public string DisplayName { get; set; }
	public string GivenName { get; set; }
	public string Surname { get; set; }
	public bool SendInvitationMessage { get; set; }
}

public class RegisteredUserToken
{
	[JsonProperty("token_type")]
	public string TokenType { get; set; }

	[JsonProperty("expires_in")]
	public int ExpiresIn { get; set; }

	[JsonProperty("ext_expires_in")]
	public int ExtExpiresIn { get; set; }

	[JsonProperty("access_token")]
	public string AccessToken { get; set; }
}

public class InviteResponse
{
	public string Id { get; set; }
	public string InviteRedeemUrl { get; set; }
	public string InvitedUserDisplayName { get; set; }
	public string InvitedUserType { get; set; }
	public string InvitedUserEmailAddress { get; set; }
	public bool SendInvitationMessage { get; set; }
	public bool ResetRedemption { get; set; }
	public string InviteRedirectUrl { get; set; }
	public string Status { get; set; }
	public object InvitedUserMessageInfo { get; set; }
	public InvitedUserDto InvitedUser { get; set; }
}

public class InvitedUserDto
{
	public Guid Id { get; set; }
}

