<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="Autofac" Version="8.1.1" />
    <PackageVersion Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageVersion Include="AutoMapper" Version="13.0.1" />
    <PackageVersion Include="ClosedXML" Version="0.104.1" />
    <PackageVersion Include="Elastic.Apm" Version="1.30.0" />
    <PackageVersion Include="Elastic.Apm.AspNetCore" Version="1.30.0" />
    <PackageVersion Include="Elastic.Apm.Extensions.Hosting" Version="1.30.0" />
    <PackageVersion Include="Magneto" Version="1.0.0" />
    <PackageVersion Include="MediatR" Version="10.0.1" />
    <PackageVersion Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="10.0.1" />
    <PackageVersion Include="MediatR.Extensions.Autofac.DependencyInjection" Version="8.0.1" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc" Version="2.2.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.11.1" />
    <PackageVersion Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.11.0-beta.1" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.11.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.11.0" />
    <PackageVersion Include="RestSharp" Version="107.3.0" />
    <PackageVersion Include="Ben.Demystifier" Version="0.4.1" />
    <PackageVersion Include="GuerrillaNtp" Version="3.1.0" />
    <PackageVersion Include="JetBrains.Annotations" Version="2024.3.0" />
    <PackageVersion Include="LaunchDarkly.ServerSdk" Version="7.0.0" />
    <PackageVersion Include="libphonenumber-csharp" Version="8.13.49" />
    <PackageVersion Include="prometheus-net.AspNetCore" Version="8.2.1" />
    <PackageVersion Include="prometheus-net.Contrib" Version="0.9.5" />
    <PackageVersion Include="System.Runtime" Version="4.3.1" />
    <PackageVersion Include="System.Runtime.Caching" Version="8.0.1" />
    <PackageVersion Include="System.Reactive" Version="6.0.1" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="OwaspHeaders.Core" Version="9.4.2" />
    <PackageVersion Include="KodeAid.Common" Version="1.5.1" />
    <PackageVersion Include="Websocket.Client" Version="5.1.2" />
    <PackageVersion Include="FluentValidation" Version="10.4.0" />
    <PackageVersion Include="LanguageExt.Core" Version="4.4.9" />
    <PackageVersion Include="Sentry.AspNetCore" Version="4.13.0" />
    <PackageVersion Include="Elastic.Apm.NetCoreAll" Version="1.30.0" />
  </ItemGroup>
  <!-- Azure Core packages -->
  <ItemGroup>
    <PackageVersion Include="Azure.Communication.Email" Version="1.0.1" />
    <PackageVersion Include="Azure.Communication.Sms" Version="1.0.1" />
    <PackageVersion Include="Azure.Messaging.ServiceBus" Version="7.18.2" />
    <PackageVersion Include="Azure.Security.KeyVault.Keys" Version="4.7.0" />
    <PackageVersion Include="Microsoft.Azure.AppConfiguration.AspNetCore" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Azure.SignalR" Version="1.28.0" />
    <PackageVersion Include="Microsoft.ApplicationInsights" Version="2.22.0" />
    <PackageVersion Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
  </ItemGroup>
  <!-- ASP.NET Core packages -->
  <ItemGroup>
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.Abstractions" Version="2.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication" Version="2.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.Core" Version="2.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.AzureAD.UI" Version="6.0.35" />
    <PackageVersion Include="Microsoft.AspNetCore.Authorization" Version="8.0.10" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.10" />
    <PackageVersion Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageVersion Include="Microsoft.AspNetCore.Cors" Version="2.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Server.Kestrel.Https" Version="2.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Routing.Abstractions" Version="2.2.0" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Http.Polly" Version="8.0.10" />
    <PackageVersion Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageVersion Include="Polly.Contrib.DuplicateRequestCollapser" Version="0.2.1" />
    <PackageVersion Include="Polly.Contrib.WaitAndRetry" Version="1.1.1" />
    <PackageVersion Include="Microsoft.AspNetCore.Diagnostics" Version="2.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.2" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Console" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.1" />
    <PackageVersion Include="Microsoft.Windows.Compatibility" Version="8.0.10" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="7.2.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Swagger" Version="6.9.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.9.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.9.0" />
  </ItemGroup>
  <!-- Identity packages -->
  <ItemGroup>
    <PackageVersion Include="Azure.Identity" Version="1.13.1" />
    <PackageVersion Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.10" />
    <PackageVersion Include="Microsoft.Identity.Web" Version="3.3.1" />
    <PackageVersion Include="Microsoft.IdentityModel.Tokens" Version="8.3.0" />
    <PackageVersion Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.3.0" />
    <PackageVersion Include="IdentityServer4" Version="4.1.2" />
    <PackageVersion Include="IdentityServer4.AccessTokenValidation" Version="3.0.1" />
    <PackageVersion Include="IdentityServer4.AspNetIdentity" Version="4.1.2" />
    <PackageVersion Include="IdentityServer4.EntityFramework" Version="4.1.2" />
    <PackageVersion Include="IdentityServer4.EntityFramework.Storage" Version="4.1.2" />
    <PackageVersion Include="IdentityServer4.Storage" Version="4.1.2" />
    <PackageVersion Include="Cnblogs.IdentityServer4.EntityFramework.Storage" Version="4.2.1" />
  </ItemGroup>
  <!-- Health Check packages -->
  <ItemGroup>
    <PackageVersion Include="AspNetCore.HealthChecks.AzureServiceBus" Version="8.0.1" />
    <PackageVersion Include="AspNetCore.HealthChecks.Publisher.ApplicationInsights" Version="8.0.1" />
    <PackageVersion Include="AspNetCore.HealthChecks.Redis" Version="8.0.1" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.10" />
  </ItemGroup>
  <!-- EF Core packages -->
  <ItemGroup>
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.10" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.10" />
    <PackageVersion Include="EFCore.BulkExtensions" Version="8.1.1" />
    <PackageVersion Include="EFCoreSecondLevelCacheInterceptor" Version="4.8.4" />
  </ItemGroup>
  <!-- Hangfire packages -->
  <ItemGroup>
    <PackageVersion Include="Hangfire.AspNetCore" Version="1.8.18" />
    <PackageVersion Include="Hangfire.Pro" Version="3.0.4" />
    <PackageVersion Include="Hangfire.Pro.Redis" Version="3.2.0" />
    <PackageVersion Include="Hangfire.Throttling" Version="1.4.1" />
    <PackageVersion Include="FaceIT.Hangfire.Tags" Version="1.9.0-beta.4" />
    <PackageVersion Include="FaceIT.Hangfire.Tags.Pro.Redis" Version="3.0.0-beta.2" />
  </ItemGroup>
  <!-- gRPC packages -->
  <ItemGroup>
    <PackageVersion Include="Google.Protobuf" Version="3.28.3" />
    <PackageVersion Include="Google.Protobuf.Tools" Version="3.28.3" />
    <PackageVersion Include="Grpc.AspNetCore" Version="2.66.0" />
    <PackageVersion Include="Grpc.Tools" Version="2.67.0" />
  </ItemGroup>
  <!-- Tests packages -->
  <ItemGroup>
    <PackageVersion Include="xunit" Version="2.9.2" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageVersion Include="xunit.assemblyfixture" Version="2.2.0" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.10" />
    <PackageVersion Include="Testcontainers.MsSql" Version="4.0.0" />
    <PackageVersion Include="Testcontainers.Redis" Version="4.0.0" />
    <PackageVersion Include="TestStack.BDDfy.Xunit" Version="1.2.0" />
    <PackageVersion Include="Faker.Net" Version="2.0.163" />
    <PackageVersion Include="JustEat.HttpClientInterception" Version="4.3.0" />
    <PackageVersion Include="MartinCostello.Logging.XUnit" Version="0.4.0" />
    <PackageVersion Include="NSubstitute" Version="5.3.0" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="NBuilder" Version="6.1.0" />
    <PackageVersion Include="Specify" Version="3.0.0" />
    <PackageVersion Include="WebMotions.Fake.Authentication.JwtBearer" Version="8.0.1" />
  </ItemGroup>
</Project>