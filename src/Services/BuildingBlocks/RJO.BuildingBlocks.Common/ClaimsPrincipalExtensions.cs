using LaunchDarkly.Sdk;
using System.Security.Claims;

namespace RJO.BuildingBlocks.Common;

public static class ClaimsPrincipalExtensions
{
	public static Context GetFlagContext(this ClaimsPrincipal principal) => Context
		.Builder(principal.GetEmailAddress())
		.Name(principal.GetName())
		.Set("tenant", principal.GetTenant())
		.Build();

	public static bool IsMobile(this ClaimsPrincipal principal)
	{
		var isMobileValue = principal.FindFirstValue(CustomClaimTypes.IsMobile);
		return !string.IsNullOrEmpty(isMobileValue) &&
			   bool.TryParse(isMobileValue, out var result) &&
			   result;
	}

	public static string GetEmailAddress(this ClaimsPrincipal principal) => principal.GetRequiredClaimValue(ClaimTypes.Email);

	public static string GetName(this ClaimsPrincipal principal) => principal.GetRequiredClaimValue(CustomClaimTypes.Name);

	public static string GetTenant(this ClaimsPrincipal principal) => principal.GetRequiredClaimValue(CustomClaimTypes.Tenant);

	public static string GetRequiredClaimValue(this ClaimsPrincipal principal, string claimType)
	{
		var claim = principal.FindFirst(claimType);

		if (claim == null)
			throw new ArgumentException($"The given claims principal is missing the {claimType} claim");

		return claim.Value;
	}
}
