using System.Globalization;

namespace RJO.BuildingBlocks.Common;

public static class TimeExtensions
{
	public static TimeSpan Microseconds(this int value) => TimeSpan.FromMilliseconds((double)value / 1000);

	public static TimeSpan Milliseconds(this int value) => TimeSpan.FromMilliseconds(value);

	public static TimeSpan Seconds(this int value) => TimeSpan.FromSeconds(value);

	public static TimeSpan Minutes(this int value) => TimeSpan.FromMinutes(value);

	public static TimeSpan Hours(this int value) => TimeSpan.FromHours(value);

	public static TimeSpan Days(this int value) => TimeSpan.FromDays(value);

	public static string AsDuration(this TimeSpan? value) => value.HasValue
		? AsDuration(value.Value)
		: StringExtensions.Nothing;

	public static string AsDuration(this TimeSpan value)
	{
		var duration = value.Duration();

		if (duration.TotalDays >= 365)
			return StringExtensions.Nothing;

		if (duration.TotalHours > 36)
			return $"{value.Days}d{AppendHours()}";

		if (duration.Days == 1)
			return $"{24 + value.Hours}h{AppendMinutes()}";

		if (duration.Hours >= 1)
			return $"{value.Hours}h{AppendMinutes()}";

		if (duration.Minutes >= 1)
			return $"{value.Minutes}m{AppendSeconds()}";

		if (duration.Seconds >= 1)
			return $"{Math.Round(value.TotalSeconds, 2)}s";

		if (duration.Milliseconds >= 10)
			return $"{Math.Round(value.TotalMilliseconds)}ms";

		if (duration.Milliseconds >= 1)
			return $"{Math.Round(value.TotalMilliseconds, 1)}ms";

		if (duration.TotalMilliseconds > 0)
			return $"{(int)(value.TotalMilliseconds * 1000)}µs";

		return StringExtensions.Nothing;

		string AppendHours() => value.Hours > 0
			? $" {value.Hours}h{AppendMinutes()}"
			: string.Empty;

		string AppendMinutes() => value.Minutes > 0
			? $" {value.Minutes}m"
			: string.Empty;

		string AppendSeconds() => value.Seconds > 0
			? $" {value.Seconds}s"
			: string.Empty;
	}

	public static string AsDate(this DateTime value) => value.ToString("ddd, dd/MM", new DateTimeFormatInfo());

	public static string AsTime(this DateTime? value) =>
		value.HasValue
			? value.Value.AsTime()
			: StringExtensions.Nothing;

	public static string AsTime(this DateTime value) => value.ToString("HH:mm:ss.fff", new DateTimeFormatInfo());

	public static double AsSecondsSinceUnixEpoch(this DateTime value) => value.Subtract(DateTime.UnixEpoch).TotalSeconds;

	public static double AsMillisecondsSinceUnixEpoch(this DateTime value) => value.Subtract(DateTime.UnixEpoch).TotalMilliseconds;
}
