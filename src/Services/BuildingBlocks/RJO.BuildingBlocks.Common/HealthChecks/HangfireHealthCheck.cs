using Hangfire;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace RJO.BuildingBlocks.Common.HealthChecks;

public class HangfireHealthCheck : IHealthCheck
{
	readonly JobStorage _jobStorage;

	public HangfireHealthCheck(JobStorage jobStorage) => _jobStorage = jobStorage;

	public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = new())
	{
		try
		{
			var monitoringApi = _jobStorage.GetMonitoringApi();
			var servers = monitoringApi.Servers();

			var result = servers.Any(s => s.Heartbeat > DateTime.UtcNow.AddMinutes(-3))
				? HealthStatus.Healthy
				: HealthStatus.Unhealthy;

			return Task.FromResult(new HealthCheckResult(result));
		}
		catch
		{
			return Task.FromResult(new HealthCheckResult(HealthStatus.Unhealthy));
		}
	}
}
