using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using RJO.BuildingBlocks.Common.Hangfire;

// ReSharper disable once CheckNamespace
namespace Microsoft.Extensions.Configuration;

public static class HangfireElasticApmExtensions
{
	public static IServiceCollection AddHangfireElasticApmTelemetry(this IServiceCollection services)
	{
		ArgumentNullException.ThrowIfNull(services);

		services.AddSingleton<ElasticApmJobFilter>();
		return services;
	}

	public static IGlobalConfiguration UseHangfireElasticApmTelemetry(this IGlobalConfiguration configuration, IServiceProvider serviceProvider)
	{
		ArgumentNullException.ThrowIfNull(configuration);
		ArgumentNullException.ThrowIfNull(serviceProvider);

		return configuration.UseFilter(serviceProvider.GetRequiredService<ElasticApmJobFilter>());
	}
}
