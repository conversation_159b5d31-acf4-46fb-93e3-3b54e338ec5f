using Hangfire.Client;
using Hangfire.Server;
using Elastic.Apm;
using Elastic.Apm.Api;

namespace RJO.BuildingBlocks.Common.Hangfire;

public class ElasticApmJobFilter : IClientFilter, IServerFilter
{
	public void OnCreating(CreatingContext context)
	{
		// Set metadata before the job is created
		context.SetJobParameter("CreatedAt", DateTime.UtcNow.ToString("o"));
	}

	public void OnCreated(CreatedContext context)
	{ 
	}

	public void OnPerforming(PerformingContext context)
	{
		// Start an Elastic APM transaction when the job starts
		var transaction = Agent.Tracer.StartTransaction($"{context.Job.Type.Name}", "hangfire");
		context.Items["ElasticApmTransaction"] = transaction;
	}

	public void OnPerformed(PerformedContext context)
	{
		if (context.Items.TryGetValue("ElasticApmTransaction", out var transactionObj) && transactionObj is ITransaction transaction)
		{
			if (context.Exception == null)
			{
				transaction.SetLabel("job.status", "Success");
			}
			else
			{
				transaction.SetLabel("job.status", "Failed");
				transaction.CaptureException(context.Exception);
			}
			transaction.End();
		}
	}
}
