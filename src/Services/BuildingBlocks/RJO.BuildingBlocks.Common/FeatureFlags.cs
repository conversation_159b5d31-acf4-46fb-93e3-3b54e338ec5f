namespace RJO.BuildingBlocks.Common;

public static class FeatureFlags
{
	public const string TempHelloWorld = nameof(TempHelloWorld);
	public const string EnableNotifications = nameof(EnableNotifications);
	public const string EnableAutoRefresh = nameof(EnableAutoRefresh);
	public const string EnableHedgeIntegration = "enableHedgeIntegration";
	public const string EnablePreHedge = nameof(EnablePreHedge);
	public const string EnableOfferQuantityEdit = nameof(EnableOfferQuantityEdit);
	public const string EnableSkipDuplicateOrderStatus = nameof(EnableSkipDuplicateOrderStatus);
	public const string EnableDayLightTimeChange = nameof(EnableDayLightTimeChange);
	public const string UseBackgroundTaskQueue = nameof(UseBackgroundTaskQueue);
	public const string EnableBidsheetDeletion = nameof(EnableBidsheetDeletion);
	public const string EnableRejectedOrderNotificationFix = nameof(EnableRejectedOrderNotificationFix);
	public const string EnableRollOfferInPendingCancel = nameof(EnableRollOfferInPendingCancel);
	public const string EnableTag50OnBidRollFix = nameof(EnableTag50OnBidRollFix);
	public const string TriggerHedgeBalanceWhenDNHTurnedOff = nameof(TriggerHedgeBalanceWhenDNHTurnedOff);
	public const string EnableHedgingThreshold = nameof(EnableHedgingThreshold);
}
