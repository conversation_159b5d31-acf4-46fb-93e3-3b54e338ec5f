using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.BusinessExceptions;
using RJO.BuildingBlocks.CustomExceptions.NonBusinessExceptions;
using RJO.BuildingBlocks.CustomExceptions.SecurityExceptions;
using RJO.BuildingBlocks.WebCommon.Models;
using System.Net;

namespace RJO.BuildingBlocks.WebCommon;

public static class ExceptionMiddlewareExtensions
{
	public static void ConfigureExceptionHandler(this IApplicationBuilder app) =>
		app.UseExceptionHandler(appError =>
		{
			appError.Run(async context =>
			{
				context.Response.ContentType = "application/json";
				var logger = context.RequestServices.GetService<ILogger<Exception>>();
				var contextFeature = context.Features.Get<IExceptionHandlerFeature>();
				if (contextFeature != null)
				{
					logger.LogError(contextFeature.Error, "General Exception");
					var errorDetail = contextFeature.CreateErrorDetail();
					context.Response.StatusCode = (int)errorDetail.Key;
					await context.Response.WriteAsync(errorDetail.Value.ToString()!);
				}
			});
		});

	static KeyValuePair<HttpStatusCode, object> CreateErrorDetail(this IExceptionHandlerFeature handlerFeature)
	{
		switch (handlerFeature.Error)
		{
			case ArgumentException ae:
				return new(HttpStatusCode.BadRequest, new ErrorDetail
				{
					Data = HttpStatusCode.BadRequest,
					Message = ae.Message
				});
			case ItemNotFoundException ie:
				return new(HttpStatusCode.BadRequest, new ErrorDetail
				{
					Data = HttpStatusCode.NotFound,
					Message = ie.Message
				});
			case ModelBindingException mbe:
				return new(HttpStatusCode.BadRequest, new ErrorDetail
				{
					Data = HttpStatusCode.BadRequest,
					Message = mbe.Message
				});
			case DatabaseUpdateException db:
				return new(HttpStatusCode.BadRequest, new ErrorDetail
				{
					Data = HttpStatusCode.BadRequest,
					Message = "An error trying to save the changes to the database, the data is not in the correct format"
				});
			case ValidationException ve:
				return new(HttpStatusCode.NotAcceptable, new ValidationErrors(ve.Errors) { Message = ve.Message });
			case BusinessException be:
				return new(HttpStatusCode.BadRequest, new ErrorDetail
				{
					Data = HttpStatusCode.BadRequest,
					Message = be.Message
				});
			case SecurityException se:
				return new(HttpStatusCode.Unauthorized, new ErrorDetail
				{
					Data = HttpStatusCode.Unauthorized,
					Message = se.Message
				});
			default:
				return new(HttpStatusCode.BadRequest, new ErrorDetail
				{
					Data = HttpStatusCode.InternalServerError,
					Message = "The server has encountered a situation it doesn't know how to handle"
				});
		}
	}
}
