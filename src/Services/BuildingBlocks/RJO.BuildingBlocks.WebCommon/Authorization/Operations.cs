using Microsoft.AspNetCore.Authorization.Infrastructure;
using RJO.BuildingBlocks.WebCommon;

namespace RJO.BuildingBlocks.WebCommon.Authorization;

public static class Operations
{
	public static OperationAuthorizationRequirement Create = new() { Name = OperationsConstants.CreateOperationName };
	public static OperationAuthorizationRequirement Update = new() { Name = OperationsConstants.EditOperationName };
	public static OperationAuthorizationRequirement Read = new() { Name = OperationsConstants.ReadOperationName };
	public static OperationAuthorizationRequirement Delete = new() { Name = OperationsConstants.DeleteOperationName };
	public static OperationAuthorizationRequirement Approve = new() { Name = OperationsConstants.ApproveOperationName };
	public static OperationAuthorizationRequirement Reject = new() { Name = OperationsConstants.RejectOperationName };
	public static OperationAuthorizationRequirement View = new() { Name = OperationsConstants.CreateOperationName };
}

public static class OperationsConstants
{
	#region General Operations

	public const string CreateOperationName = "Create";
	public const string EditOperationName = "Edit";
	public const string ReadOperationName = "Read";
	public const string DeleteOperationName = "Delete";
	public const string ApproveOperationName = "Approve";
	public const string RejectOperationName = "Reject";
	public const string ViewOperationName = "View";
	public const string ConvertOperationName = "Convert";

	#endregion

	public const string MobileAppEnable = "ContractService.Customer.MobileAppEnable";

	#region Contract Operations

	public const string UseSalesTrading = "ContractService.Contract.UseSalesTrading";

	public const string ContractActivateDNH = "ContractService.Contract.ActivateDNH";

	public const string ContractBasisCreate = "ContractService.ContractBasis.Create";
	public const string ContractBasisEditNonQuantity = "ContractService.ContractBasis.EditNonQuantity";
	public const string ContractBasisEditQuantity = "ContractService.ContractBasis.EditQuantity";

	public const string ContractFlatPriceCreate = "ContractService.ContractFlatPrice.Create";
	public const string ContractFlatPriceEditNonQuantity = "ContractService.ContractFlatPrice.EditNonQuantity";
	public const string ContractFlatPriceEditQuantity = "ContractService.ContractFlatPrice.EditQuantity";

	public const string ContractHTACreate = "ContractService.ContractHTA.Create";
	public const string ContractHTAEditNonQuantity = "ContractService.ContractHTA.EditNonQuantity";
	public const string ContractHTAEditQuantity = "ContractService.ContractHTA.EditQuantity";

	public const string ContractNTCCreate = "ContractService.ContractNTC.Create";
	public const string ContractNTCEditNonQuantity = "ContractService.ContractNTC.EditNonQuantity";
	public const string ContractNTCEditQuantity = "ContractService.ContractNTC.EditQuantity";
	public const string ContractNTCConvertQuantity = "ContractService.ContractNTC.ConvertQuantity";

	public const string ResendToErp = "ContractService.Contract.ResendToErp";
	public const string ContractCancel = "ContractService.Contract.Cancel";

	#endregion

	#region Role Specific Contract Operation

	public const string AllowNTCView = "ContractService.Allow.ContractNTC";

	#endregion

	#region Offer Operations

	public const string OfferBasisCreate = "ContractService.OfferBasis.Create";
	public const string OfferBasisEdit = "ContractService.OfferBasis.Edit";

	public const string OfferFlatPriceCreate = "ContractService.OfferFlatPrice.Create";
	public const string OfferFlatPriceEdit = "ContractService.OfferFlatPrice.Edit";

	public const string OfferHTACreate = "ContractService.OfferHTA.Create";
	public const string OfferHTAEdit = "ContractService.OfferHTA.Edit";

	#endregion

	#region Live Ledger Operations

	public const string LiveLedgerView = "ContractService.LiveLedger.View";

	#endregion

	#region Review And Release  Operations

	public const string ReviewAndReleaseView = "ContractService.ReviewAndRelease.View";

	#endregion

	#region Settings Operations

	public const string SettingsAdmin = SecurityConstants.SettingsAdmin;

	#endregion

	public const string UploadBidsheet = "ContractService.Bidsheet.Upload";

	#region Settings Flags

	public const string OMSOnly = "ContractService.Contract.OMSOnly";

	#endregion

	public const string FutureFirst = "ContractService.FutureFirst.View";
}

public static class ClaimTypesConstants
{
	public static readonly string Grant = "Grant";
	public static readonly string Role = "Role";
}
