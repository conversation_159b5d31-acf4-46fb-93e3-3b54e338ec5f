<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Elastic.Apm" />
    <PackageReference Include="IdentityServer4.AccessTokenValidation" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
    <PackageReference Include="Newtonsoft.Json" />
		<PackageReference Include="OwaspHeaders.Core" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RJO.BuildingBlocks.Common\RJO.BuildingBlocks.Common.csproj" />
    <ProjectReference Include="..\RJO.BuildingBlocks.CustomExceptions\RJO.BuildingBlocks.CustomExceptions.csproj" />
  </ItemGroup>

</Project>
