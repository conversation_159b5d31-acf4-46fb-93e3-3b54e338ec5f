namespace RJO.IntegrationEvents.Commons.Events;

public class CreateSpreadOrderEvent : MarketOperationEvent
{
	public string SpreadSymbol { get; set; }
	public string OldContractSymbol { get; set; }
	public string NewContractSymbol { get; set; }
	public DateTime? Limit { get; set; }
	public uint Duration { get; set; }
	public bool IsSell { get; set; }
	public string ContractNumber { get; set; }
	public long Quantity { get; set; }
	public string TenantId { get; set; }
	public int AccountId { get; set; }
	public decimal? LimitPrice { get; set; }
	public bool IsMarketOrder { get; set; }
}
