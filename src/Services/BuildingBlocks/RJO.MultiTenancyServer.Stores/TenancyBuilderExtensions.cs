// Copyright (c) <PERSON>. All rights reserved.
// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RJO.MultiTenancyServer.Core.Configuration;

namespace RJO.MultiTenancyServer.Stores;

public static class TenancyBuilderExtensions
{
	/// <summary>
	/// Adds the in-memory tenant store.
	/// </summary>
	/// <typeparam name="TTenant">The type representing a Tenant in the system.</typeparam>
	/// <typeparam name="TKey">The type of the primary key for a tenant.</typeparam>
	/// <param name="builder">The builder.</param>
	/// <param name="tenants">The tenants.</param>
	/// <returns></returns>
	public static TenancyBuilder<TTenant, TKey> AddInMemoryStore<TTenant, TKey>(this TenancyBuilder<TTenant, TKey> builder, IEnumerable<TTenant> tenants)
		where TTenant : TenancyTenant<TKey>
		where TK<PERSON> : IEquatable<TKey>
	{
		builder.Services.AddSingleton(tenants);
		builder.AddTenantStore<InMemoryTenantStore<TTenant, TKey>>();
		return builder;
	}

	/// <summary>
	/// Adds the in-memory tenant store.
	/// </summary>
	/// <typeparam name="TTenant">The type representing a Tenant in the system.</typeparam>
	/// <typeparam name="TKey">The type of the primary key for a tenant.</typeparam>
	/// <param name="builder">The builder.</param>
	/// <param name="section">The configuration section containing the configuration data.</param>
	/// <returns></returns>
	public static TenancyBuilder<TTenant, TKey> AddInMemoryStore<TTenant, TKey>(this TenancyBuilder<TTenant, TKey> builder, IConfigurationSection section)
		where TTenant : TenancyTenant<TKey>
		where TKey : IEquatable<TKey>
	{
		var tenants = new List<TTenant>();
		section.Bind(tenants);
		return builder.AddInMemoryStore(tenants);
	}
}
