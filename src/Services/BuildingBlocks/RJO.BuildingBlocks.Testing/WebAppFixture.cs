#nullable enable
using JustEat.HttpClientInterception;
using MartinCostello.Logging.XUnit;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Common.Clock;
using RJO.BuildingBlocks.Common.Http;
using WebMotions.Fake.Authentication.JwtBearer;
using Xunit.Abstractions;

namespace RJO.BuildingBlocks.Testing;

public abstract class WebAppFixture<T> : HangfireWebApplicationFactory<T>, IWebAppFixture where T : class
{
	protected WebAppFixture()
	{
		// Use HTTPS by default and do not follow
		// redirects so they can tested explicitly.
		ClientOptions.AllowAutoRedirect = false;
		ClientOptions.BaseAddress = new("https://localhost");

		// Configure HTTP requests that are not intercepted by
		// the tests to throw an exception to cause it to fail.
		OutboundHttp = new HttpClientInterceptorOptions().ThrowsOnMissingRegistration();

		// Create a MemoryCacheManager for replacing the default
		// with an implementation that lets us reset it.
		MemoryCache = new(Options.Create(new MemoryCacheOptions()));

		Containers = new(this);
	}

	public ITestOutputHelper? OutputHelper { get; set; }

	public HttpClientInterceptorOptions OutboundHttp { get; }

	public MemoryCacheManager MemoryCache { get; }

	public Containers Containers { get; }

	public void EnsureServerStarted() => _ = Server;

	protected override void ConfigureWebHost(IWebHostBuilder builder)
	{
		builder.UseDataShardingTestPrefix();
		builder.UseContainers(Containers);
		
		builder.UseSetting(ConfigurationKeys.ApplicationInsightsInstrumentationKey, Guid.Empty.ToString());

		builder.ConfigureLogging(loggingBuilder => loggingBuilder.ClearProviders().AddXUnit(this, ConfigureLogger));

		builder.ConfigureServices(services =>
		{
			services.AddSingleton(Containers);
			services.AddAuthentication(FakeJwtBearerDefaults.AuthenticationScheme).AddFakeJwtBearer();
			services.AddSingleton<IHttpMessageHandlerBuilderFilter, HttpRequestInterceptionFilter>(_ => new(OutboundHttp));
			services.Replace(ServiceDescriptor.Singleton<IMemoryCache>(MemoryCache));

			services.Configure<ClockSynchronization>(x => x.Disabled = true);
			services.Configure<HttpConfiguration>(x =>
			{
				x.ClientTimeout = 10.Minutes();
				x.LogRequests = HttpLogLevel.TimingsOnly;
			});
		});
	}

	static void ConfigureLogger(XUnitLoggerOptions options) => options.ForLevelAndAbove(LogLevel.Information, "System.", "Microsoft.", "Hangfire.", "WebMotions.");
}
