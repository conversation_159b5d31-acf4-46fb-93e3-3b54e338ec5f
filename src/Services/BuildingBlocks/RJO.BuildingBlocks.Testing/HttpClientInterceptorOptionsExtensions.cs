using FluentAssertions.Extensions;
using JustEat.HttpClientInterception;
using System.ComponentModel;

namespace RJO.BuildingBlocks.Testing;

[EditorBrowsable(EditorBrowsableState.Never)]
public static class HttpClientInterceptorOptionsExtensions
{
	public static void RespondWithJsonFor<T>(this HttpClientInterceptorOptions options, RequestMatcher predicate, params T[] responses) =>
		options.RespondWithJsonFor(predicate, Random.Shared.Next(10, 20).Milliseconds(), responses);

	public static void RespondWithJsonFor<T>(this HttpClientInterceptorOptions options, RequestMatcher predicate, TimeSpan delay, params T[] responses) =>
		options.RespondWithJsonFor(predicate, (_, _, cancellationToken) => Task.Delay(delay, cancellationToken), responses);

	public static void RespondWithJsonFor<T>(this HttpClientInterceptorOptions options, RequestMatcher predicate, Action<int, T> callback, params T[] responses) =>
		options.RespondWithJsonFor(predicate, (number, item, _) =>
		{
			callback(number, item);
			return Task.CompletedTask;
		}, responses);

	public static void RespondWithJsonFor<T>(this HttpClientInterceptorOptions options, RequestMatcher predicate, Func<int, T, CancellationToken, Task> callback, params T[] responses) =>
		options.ConfigureInterceptionFor(predicate, x => x.WithJsonResponses(callback, responses));

	public static void ConfigureInterceptionFor(this HttpClientInterceptorOptions options, RequestMatcher predicate, Func<HttpRequestInterceptionBuilder, HttpRequestInterceptionBuilder> configure) =>
		configure(new HttpRequestInterceptionBuilder().For(predicate)).RegisterWith(options);

	public static void RespondWithJsonFor<T>(this HttpClientInterceptorOptions options, AsyncRequestMatcher predicate, params T[] responses) =>
		options.RespondWithJsonFor(predicate, Random.Shared.Next(10, 20).Milliseconds(), responses);

	public static void RespondWithJsonFor<T>(this HttpClientInterceptorOptions options, AsyncRequestMatcher predicate, TimeSpan delay, params T[] responses) =>
		options.RespondWithJsonFor(predicate, (_, _, cancellationToken) => Task.Delay(delay, cancellationToken), responses);

	public static void RespondWithJsonFor<T>(this HttpClientInterceptorOptions options, AsyncRequestMatcher predicate, Action<int, T> callback, params T[] responses) =>
		options.RespondWithJsonFor(predicate, (number, item, _) =>
		{
			callback(number, item);
			return Task.CompletedTask;
		}, responses);

	public static void RespondWithJsonFor<T>(this HttpClientInterceptorOptions options, AsyncRequestMatcher predicate, Func<int, T, CancellationToken, Task> callback, params T[] responses) =>
		options.ConfigureInterceptionFor(predicate, x => x.WithJsonResponses(callback, responses));

	public static void ConfigureInterceptionFor(this HttpClientInterceptorOptions options, AsyncRequestMatcher predicate, Func<HttpRequestInterceptionBuilder, HttpRequestInterceptionBuilder> configure) =>
		configure(new HttpRequestInterceptionBuilder().For(predicate)).RegisterWith(options);
}
